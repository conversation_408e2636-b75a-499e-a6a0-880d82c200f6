---
title: Environment Improvements
description: New environments and benchmark experiments we would like to add.
---

These are the things we would like to implement:

- [Make dataset to test compositional objects](environment-improvements/make-dataset-to-test-compositional-objects.md) #compositional #multiobj
- [Set up Environment that allows for object manipulation](environment-improvements/set-up-environment-that-allows-for-object-manipulation.md) #goalpolicy
- [Set up object manipulation benchmark tasks and evaluation measures](environment-improvements/set-up-object-manipulation-benchmark-tasks-and-evaluation-measures.md) #goalpolicy
- [Create dataset and metrics to evaluate categories and generalization](environment-improvements/create-dataset-and-metrics-to-evaluate-categories-and-generalization.md) #generalization
- [Create dataset and metrics to test new feature-morphology pairs](environment-improvements/create-dataset-and-metrics-to-test-new-feature-morphology-pairs.md) #featsandmorph

!snippet[../snippets/contributing-tasks.md]