---
title: Bottom-Up Exploration Policy for Surface Agent
---

For the distant agent, we have a policy specifically tailored to learning, the naive scan policy, which systematically explores the visible surface of an object. We would like a similar policy for the surface agent that systematically spirals or scans across the surface of an object, at least in a local area.

This would likely be complemented by [Model-Based Exploration Policies](model-based-exploration-policy.md).
