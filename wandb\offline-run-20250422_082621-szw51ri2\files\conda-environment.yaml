name: tbp.monty
channels:
  - pytorch
  - pyg
  - aihabitat
  - conda-forge
  - https://repo.anaconda.com/pkgs/main
  - https://repo.anaconda.com/pkgs/r
  - defaults
dependencies:
  - aom=3.5.0=hf0c8a7f_0
  - appdirs=1.4.4=pyh9f0ad1d_0
  - attrs=24.2.0=pyh71513ae_0
  - blas=2.112=mkl
  - blas-devel=3.9.0=12_osx64_mkl
  - blosc=1.21.5=heccf04b_0
  - brotli=1.0.9=hb7f2c08_9
  - brotli-bin=1.0.9=hb7f2c08_9
  - brotli-python=1.0.9=py38h4cd09af_9
  - brunsli=0.1=h046ec9c_0
  - bzip2=1.0.8=hfdf4475_7
  - c-ares=1.34.5=hf13058a_0
  - c-blosc2=2.12.0=h0ae8482_0
  - ca-certificates=2025.1.31=hbd8a1cb_1
  - cairo=1.18.4=h950ec3b_0
  - certifi=2024.8.30=pyhd8ed1ab_0
  - cffi=1.17.0=py38hc8bcfa4_0
  - cfitsio=4.2.0=hd56cc12_0
  - charls=2.4.2=he965462_0
  - charset-normalizer=3.4.0=pyhd8ed1ab_0
  - click=8.1.7=unix_pyh707e725_0
  - cmake=4.0.1=h477996e_0
  - colorama=0.4.6=pyhd8ed1ab_0
  - contourpy=1.1.1=py38h15a1a5b_1
  - cpython=3.8.20=py38hd8ed1ab_2
  - cycler=0.12.1=pyhd8ed1ab_0
  - dav1d=1.2.1=h0dc2134_0
  - docker-pycreds=0.4.0=py_0
  - ffmpeg=6.0.0=gpl_h789f13d_104
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=h77eed37_3
  - fontconfig=2.15.0=h37eeddb_1
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - fonttools=4.53.1=py38hc718529_0
  - freetype=2.13.3=h40dfd5c_0
  - fribidi=1.0.10=hbcb3906_0
  - gettext=0.23.1=hd385c8e_0
  - gettext-tools=0.23.1=h27064b9_0
  - giflib=5.2.2=h10d778d_0
  - gitdb=4.0.11=pyhd8ed1ab_0
  - gitpython=3.1.43=pyhd8ed1ab_0
  - gmp=6.3.0=hf036a51_2
  - gmpy2=2.1.5=py38hdb2c785_1
  - gnutls=3.7.9=h1951705_0
  - graphite2=1.3.13=h73e2aa4_1003
  - h2=4.1.0=pyhd8ed1ab_0
  - habitat-sim-mutex=1.0=display_bullet
  - harfbuzz=9.0.0=h098a298_1
  - hpack=4.0.0=pyh9f0ad1d_0
  - hyperframe=6.0.1=pyhd8ed1ab_0
  - icu=75.1=h120a0e1_0
  - idna=3.10=pyhd8ed1ab_0
  - imagecodecs=2023.1.23=py38h5408aff_0
  - imageio=2.36.0=pyh12aca89_1
  - imageio-ffmpeg=0.5.1=pyhd8ed1ab_0
  - importlib-metadata=8.5.0=pyha770c72_0
  - importlib-resources=6.4.5=pyhd8ed1ab_0
  - importlib_resources=6.4.5=pyhd8ed1ab_0
  - jinja2=3.1.4=pyhd8ed1ab_0
  - joblib=1.4.2=pyhd8ed1ab_0
  - jpeg=9e=hb7f2c08_3
  - jxrlib=1.1=h10d778d_3
  - kiwisolver=1.4.5=py38h15a1a5b_1
  - krb5=1.21.3=h37d8d59_0
  - lame=3.100=hb7f2c08_1003
  - lazy-loader=0.4=pyhd8ed1ab_1
  - lazy_loader=0.4=pyhd8ed1ab_1
  - lcms2=2.15=h29502cd_0
  - lerc=4.0.0=hcca01a6_1
  - libabseil=20240116.2=cxx17_hf036a51_1
  - libaec=1.1.3=h73e2aa4_0
  - libasprintf=0.23.1=h27064b9_0
  - libasprintf-devel=0.23.1=h27064b9_0
  - libass=0.17.1=h5386a9e_2
  - libavif=0.11.1=h22361c6_2
  - libblas=3.9.0=12_osx64_mkl
  - libbrotlicommon=1.0.9=hb7f2c08_9
  - libbrotlidec=1.0.9=hb7f2c08_9
  - libbrotlienc=1.0.9=hb7f2c08_9
  - libcblas=3.9.0=12_osx64_mkl
  - libcurl=8.13.0=h5dec5d8_0
  - libcxx=20.1.3=hf95d169_0
  - libdeflate=1.17=hac1461d_0
  - libedit=3.1.20250104=pl5321ha958ccf_0
  - libev=4.33=h10d778d_2
  - libexpat=2.7.0=h240833e_0
  - libffi=3.4.6=h281671d_1
  - libgettextpo=0.23.1=h27064b9_0
  - libgettextpo-devel=0.23.1=h27064b9_0
  - libgfortran=14.2.0=hef36b68_105
  - libgfortran5=14.2.0=h58528f3_105
  - libglib=2.84.0=h5c976ab_0
  - libhwloc=2.11.2=default_h4cdd727_1001
  - libiconv=1.18=h4b5e92a_1
  - libidn2=2.3.8=he8ff88c_0
  - libintl=0.23.1=h27064b9_0
  - libintl-devel=0.23.1=h27064b9_0
  - liblapack=3.9.0=12_osx64_mkl
  - liblapacke=3.9.0=12_osx64_mkl
  - libllvm14=14.0.6=hc8e404f_4
  - liblzma=5.8.1=hd471939_0
  - liblzma-devel=5.8.1=hd471939_0
  - libnghttp2=1.64.0=hc7306c3_0
  - libopus=1.5.2=he3325bb_0
  - libpng=1.6.47=h3c4a55f_0
  - libprotobuf=4.25.3=hd4aba4c_1
  - libsqlite=3.49.1=hdb6dae5_2
  - libssh2=1.11.1=h3dc7d44_0
  - libtasn1=4.20.0=h6e16a3a_0
  - libtiff=4.5.0=hee9004a_2
  - libunistring=0.9.10=h0d85af4_0
  - libuv=1.50.0=h4cb831e_0
  - libvpx=1.13.1=he965462_0
  - libwebp-base=1.5.0=h6cf52b4_0
  - libxcb=1.13=h0d85af4_1004
  - libxml2=2.13.7=h93c44a6_1
  - libzlib=1.3.1=hd23fc13_2
  - libzopfli=1.0.3=h046ec9c_0
  - llvm-openmp=20.1.3=ha54dae1_0
  - llvmlite=0.41.1=py38h6008ce5_0
  - lz4-c=1.9.4=hf0c8a7f_0
  - markupsafe=2.1.5=py38hae2e43d_0
  - matplotlib=3.7.3=py38h50d1736_0
  - matplotlib-base=3.7.3=py38hcd1b199_0
  - mkl=2021.4.0=h89fa619_689
  - mkl-devel=2021.4.0=h694c41f_690
  - mkl-include=2021.4.0=hf224eb6_689
  - mpc=1.3.1=h9d8efa1_1
  - mpfr=4.2.1=haed47dc_3
  - mpmath=1.3.0=pyhd8ed1ab_0
  - munkres=1.1.4=pyh9f0ad1d_0
  - ncurses=6.5=h0622a9a_3
  - nettle=3.9.1=h8e11ae5_0
  - networkx=3.1=pyhd8ed1ab_0
  - numba=0.58.1=py38h730cc54_0
  - numpy=1.23.5=py38hc2f29e8_0
  - openh264=2.3.1=hf0c8a7f_2
  - openjpeg=2.5.0=h13ac156_2
  - openssl=3.5.0=hc426f3f_0
  - p11-kit=0.24.1=h65f8906_0
  - packaging=25.0=pyhd8ed1ab_0
  - pandas=2.0.3=py38h78e6021_1
  - pcre2=10.44=h7634a1b_2
  - pillow=9.4.0=py38hf04c7c8_1
  - pip=24.3.1=pyh8b19718_0
  - pixman=0.44.2=h1fd1274_0
  - platformdirs=4.3.6=pyhd8ed1ab_0
  - pooch=1.8.2=pyhd8ed1ab_0
  - protobuf=4.25.3=py38hba9f9f8_0
  - psutil=6.0.0=py38hc718529_0
  - pthread-stubs=0.4=h00291cd_1002
  - pycparser=2.22=pyhd8ed1ab_0
  - pyg=2.1.0=py38_torch_1.11.0_cpu
  - pyparsing=3.1.4=pyhd8ed1ab_0
  - pysocks=1.7.1=pyha2e5f31_6
  - python=3.8.20=h4f978b9_2_cpython
  - python-dateutil=2.9.0=pyhd8ed1ab_0
  - python-tzdata=2024.2=pyhd8ed1ab_0
  - python_abi=3.8=7_cp38
  - pytorch=1.11.0=py3.8_0
  - pytorch-cluster=1.6.0=py38_torch_1.11.0_cpu
  - pytorch-scatter=2.0.9=py38_torch_1.11.0_cpu
  - pytorch-sparse=0.6.15=py38_torch_1.11.0_cpu
  - pytz=2024.2=pyhd8ed1ab_0
  - pywavelets=1.4.1=py38h49861b3_1
  - pyyaml=6.0.2=py38hc718529_0
  - quaternion=2023.0.3=py38h7092abf_0
  - readline=8.2=h7cca4af_2
  - requests=2.32.3=pyhd8ed1ab_0
  - rhash=1.4.5=ha44c9a9_0
  - scikit-image=0.21.0=py38h940360d_0
  - scikit-learn=1.3.2=py38h43a65be_2
  - scipy=1.10.1=py38h9cf86d3_3
  - sentry-sdk=2.19.2=pyhd8ed1ab_0
  - setproctitle=1.3.3=py38hcafd530_0
  - setuptools=75.3.0=pyhd8ed1ab_0
  - six=1.16.0=pyh6c4a22f_0
  - smmap=3.0.5=pyh44b312d_0
  - snappy=1.1.10=h6dc393e_1
  - svt-av1=1.7.0=he965462_0
  - sympy=1.13.3=pyh2585a3b_104
  - tbb=2021.13.0=hb890de9_1
  - threadpoolctl=3.5.0=pyhc1e730c_0
  - tifffile=2023.7.10=pyhd8ed1ab_0
  - tk=8.6.13=h1abcd95_1
  - torchvision=0.12.0=py38_cpu
  - tornado=6.4.1=py38hc718529_0
  - tqdm=4.67.1=pyhd8ed1ab_0
  - typing_extensions=4.12.2=pyha770c72_0
  - unicodedata2=15.1.0=py38hcafd530_0
  - urllib3=2.2.3=pyhd8ed1ab_0
  - wandb=0.16.6=pyhd8ed1ab_1
  - wget=1.21.4=hca547e6_0
  - wheel=0.45.1=pyhd8ed1ab_0
  - withbullet=2.0=0
  - x264=1!164.3095=h775f41a_2
  - x265=3.5=hbb4e6a2_3
  - xorg-libxau=1.0.12=h6e16a3a_0
  - xorg-libxdmcp=1.1.5=h00291cd_0
  - xz=5.8.1=h357f2ed_0
  - xz-gpl-tools=5.8.1=h357f2ed_0
  - xz-tools=5.8.1=hd471939_0
  - yaml=0.2.5=h0d85af4_2
  - zfp=1.0.1=h469392a_2
  - zipp=3.21.0=pyhd8ed1ab_0
  - zlib=1.3.1=hd23fc13_2
  - zlib-ng=2.0.7=hb7f2c08_0
  - zstandard=0.19.0=py38hef030d1_0
  - zstd=1.5.7=h8210216_2
  - pip:
      - coverage==7.6.1
      - deptry==0.20.0
      - execnet==2.1.1
      - habitat-sim==0.2.2
      - iniconfig==2.1.0
      - mypy==1.11.2
      - mypy-extensions==1.0.0
      - pluggy==1.5.0
      - py==1.11.0
      - pytest==7.1.1
      - pytest-cov==3.0.0
      - pytest-forked==1.6.0
      - pytest-xdist==2.5.0
      - ruff==0.11.4
      - tbp-monty==0.3.0
      - tomli==2.2.1
prefix: /opt/anaconda3/envs/tbp.monty
