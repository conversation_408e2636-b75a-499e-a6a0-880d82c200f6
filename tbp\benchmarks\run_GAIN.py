# Copyright 2025 Thousand Brains Project
# Copyright 2022-2024 Numenta Inc.
#
# Copyright may exist in Contributors' modifications
# and/or contributions to the work.
#
# Use of this source code is governed by the MIT
# license that can be found in the LICENSE file or at
# https://opensource.org/licenses/MIT .

import os
import sys

# Set up the path to access all Monty modules properly
sys.path.insert(
    0, os.path.dirname(os.path.dirname(os.path.expanduser(os.path.realpath(__file__))))
)

from benchmarks.configs.load import load_configs
from benchmarks.configs.names import NAMES
from tbp.monty.frameworks.config_utils.cmd_parser import create_cmd_parser
from tbp.monty.frameworks.run_env import setup_env

setup_env()

from tbp.monty.frameworks.run import main  # noqa: E402

# --- New Import for Information Gain Action Selector ---
from tbp.monty.frameworks.actions.information_gain_actions import InformationGainActionSelector

if __name__ == "__main__":
    cmd_args = None
    cmd_parser = create_cmd_parser(experiments=NAMES)
    cmd_args = cmd_parser.parse_args()
    experiments = cmd_args.experiments

    if cmd_args.quiet_habitat_logs:
        os.environ["MAGNUM_LOG"] = "quiet"
        os.environ["HABITAT_SIM_LOG"] = "quiet"

    # Load existing experiment configs
    CONFIGS = load_configs(experiments)

    # Debugging: Print CONFIGS type and content
    print("Type of CONFIGS:", type(CONFIGS))
    print("CONFIGS content:", CONFIGS)

    # Check if CONFIGS is a list of configurations
    if isinstance(CONFIGS, list):
        for config in CONFIGS:
            # Debugging: Check if config is a string or an object
            if isinstance(config, str):
                print(f"Config entry is a string: {config}")
                # Convert string to configuration object if necessary
                # config = convert_to_config_object(config)
            else:
                print("Config entry is an object.")
                # Inject the Information Gain Action Selector into each config
                config.action_selector_class = InformationGainActionSelector
                config.action_selector_args = {
                    "model": getattr(config, "model", None),
                    "entropy_estimation_method": "variance",  # Optional: allow variants later
                }
    else:
        print("CONFIGS is not a list. Type:", type(CONFIGS))
        # Handle non-list CONFIGS if necessary

    # Run main loop with modified configs
    main(all_configs=CONFIGS, experiments=cmd_args.experiments)