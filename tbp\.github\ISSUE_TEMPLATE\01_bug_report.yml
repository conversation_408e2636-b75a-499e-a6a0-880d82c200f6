name: Bug Report
description: Create a report to help us reproduce and fix the bug.
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        ## Before creating a bug report.

        Please check if the issue has already been reported by [searching issues](https://github.com/thousandbrainsproject/tbp.monty/issues?q=is%3Aissue+label%3Abug+sort%3Acreated-desc).
  - type: textarea
    attributes:
      label: Describe the bug
      description: |
        Please provide a clear and concise description of what the bug is. A helpful format is "What I did, what I expected, and what happened instead."

        If relevant and able, please provide a minimal code snippet or steps to reproduce the bug.

        When pasting code or errors, it is helpful to wrap it in triple backticks (```) for formatting. For example:

        ```python
        import tbp.monty as monty
        ...
        ```

        If the code is too long, please use a public gist and link it in the issue: https://gist.github.com.
    validations:
      required: true
  - type: markdown
    attributes:
      value: |
        Thank you for your bug report 🥳!