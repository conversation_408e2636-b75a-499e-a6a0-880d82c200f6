<!-- vale off -->
# overview: Overview
- [welcome-to-the-thousand-brains-project-documentation](overview/welcome-to-the-thousand-brains-project-documentation.md)
- [vision-of-the-thousand-brains-project](overview/vision-of-the-thousand-brains-project.md)
  - [long-term-goals-and-principles](overview/vision-of-the-thousand-brains-project/long-term-goals-and-principles.md)
  - [short-term-goals](overview/vision-of-the-thousand-brains-project/short-term-goals.md)
  - [challenging-preconceptions](overview/vision-of-the-thousand-brains-project/challenging-preconceptions.md)
  - [capabilities-of-the-system](overview/vision-of-the-thousand-brains-project/capabilities-of-the-system.md)
- [architecture-overview](overview/architecture-overview.md)
  - [sensor-modules](overview/architecture-overview/sensor-modules.md)
  - [learning-modules](overview/architecture-overview/learning-modules.md)
  - [cortical-messaging-protocol](overview/architecture-overview/cortical-messaging-protocol.md)
  - [other-aspects](overview/architecture-overview/other-aspects.md)
  - [bringing-it-together](overview/architecture-overview/bringing-it-together.md)
- [application-criteria](overview/application-criteria.md)
- [benchmark-experiments](overview/benchmark-experiments.md)
  - [results-from-alternative-implementations](overview/benchmark-experiments/results-from-alternative-implementations.md)
- [faq-thousand-brains-project](overview/faq-thousand-brains-project.md)
- [glossary](overview/glossary.md)
- [further-reading](overview/further-reading.md)

# how-to-use-monty: How to Use Monty
- [getting-started](how-to-use-monty/getting-started.md)
- [code-base-structure](how-to-use-monty/code-base-structure.md)
- [running-benchmarks](how-to-use-monty/running-benchmarks.md)
- [logging-and-analysis](how-to-use-monty/logging-and-analysis.md)
- [customizing-monty](how-to-use-monty/customizing-monty.md)
- [tutorials](how-to-use-monty/tutorials.md)
  - [running-your-first-experiment](how-to-use-monty/tutorials/running-your-first-experiment.md)
  - [pretraining-a-model](how-to-use-monty/tutorials/pretraining-a-model.md)
  - [running-inference-with-a-pretrained-model](how-to-use-monty/tutorials/running-inference-with-a-pretrained-model.md)
  - [unsupervised-continual-learning](how-to-use-monty/tutorials/unsupervised-continual-learning.md)
  - [multiple-learning-modules](how-to-use-monty/tutorials/multiple-learning-modules.md)
- [common-issues-and-how-to-fix-them](how-to-use-monty/common-issues-and-how-to-fix-them.md)

# how-monty-works: How Monty Works
- [implementation-overview](how-monty-works/implementation-overview.md)
- [experiment](how-monty-works/experiment.md)
- [model](how-monty-works/model.md)
- [environment-agent](how-monty-works/environment-agent.md)
- [observations-transforms-sensor-modules](how-monty-works/observations-transforms-sensor-modules.md)
- [policy](how-monty-works/policy.md)
- [how-learning-modules-work](how-monty-works/how-learning-modules-work.md)
- [learning-module-outputs](how-monty-works/learning-module-outputs.md)
- [evidence-based-learning-module](how-monty-works/evidence-based-learning-module.md)
  - [evidence-as-a-similarity-measure](how-monty-works/evidence-based-learning-module/evidence-as-a-similarity-measure.md)
- [connecting-lms-into-a-heterarchy](how-monty-works/connecting-lms-into-a-heterarchy.md)
- [faq-monty](how-monty-works/faq-monty.md)
- [open-questions](how-monty-works/open-questions.md)

# contributing: Contributing
- [why-contribute](contributing/why-contribute.md)
- [contributing](contributing/contributing.md)
- [documentation](contributing/documentation.md)
- [contributing-tutorials](contributing/contributing-tutorials.md)
- [comment-on-issues](contributing/comment-on-issues.md)
- [request-for-comments-rfc](contributing/request-for-comments-rfc.md)
- [pull-requests](contributing/pull-requests.md)
  - [contributor-license-agreement](contributing/pull-requests/contributor-license-agreement.md)
  - [pull-request-flow](contributing/pull-requests/pull-request-flow.md)
  - [reviewing-a-pull-request](contributing/pull-requests/reviewing-a-pull-request.md)
- [ways-to-contribute-to-code](contributing/ways-to-contribute-to-code.md)
  - [identify-an-issue-to-work-on](contributing/ways-to-contribute-to-code/identify-an-issue-to-work-on.md)
- [style-guide](contributing/style-guide.md)
- [guides-for-maintainers](contributing/guides-for-maintainers.md)
  - [triage](contributing/guides-for-maintainers/triage.md)

# community: Community
- [code-of-conduct](community/code-of-conduct.md)
- [governance](community/governance.md)
- [our-licence-and-patent-pledge-explained](community/our-licence-and-patent-pledge-explained.md)
- [tbp-based-papers](community/tbp-based-papers.md)
- [project-showcase](community/project-showcase.md)

# future-work: Future Work
- [project-roadmap](future-work/project-roadmap.md)
- [sensor-module-improvements](future-work/sensor-module-improvements.md)
  - [extract-better-features](future-work/sensor-module-improvements/extract-better-features.md)
  - [detect-local-and-global-flow](future-work/sensor-module-improvements/detect-local-and-global-flow.md)
- [learning-module-improvements](future-work/learning-module-improvements.md)
  - [contributing-learning-modules](future-work/learning-module-improvements/contributing-learning-modules.md)
  - [use-off-object-observations](future-work/learning-module-improvements/use-off-object-observations.md)
  - [implement-and-test-rapid-evidence-decay-as-form-of-unsupervised-memory-resetting](future-work/learning-module-improvements/implement-and-test-rapid-evidence-decay-as-form-of-unsupervised-memory-resetting.md)
  - [improve-bounded-evidence-performance](future-work/learning-module-improvements/improve-bounded-evidence-performance.md)
  - [use-models-with-fewer-points](future-work/learning-module-improvements/use-models-with-fewer-points.md)
  - [make-it-possible-to-store-multiple-feature-maps-on-one-graph](future-work/learning-module-improvements/make-it-possible-to-store-multiple-feature-maps-on-one-graph.md)
  - [test-particle-filter-like-resampling-of-hypothesis-space](future-work/learning-module-improvements/test-particle-filter-like-resampling-of-hypothesis-space.md)
  - [re-anchor-hypotheses](future-work/learning-module-improvements/re-anchor-hypotheses.md)
  - [less-dependency-on-first-observation](future-work/learning-module-improvements/less-dependency-on-first-observation.md)
  - [deal-with-incomplete-models](future-work/learning-module-improvements/deal-with-incomplete-models.md)
  - [implement-test-gnns-to-model-object-behaviors-states](future-work/learning-module-improvements/implement-test-gnns-to-model-object-behaviors-states.md)
  - [deal-with-moving-objects](future-work/learning-module-improvements/deal-with-moving-objects.md)
  - [support-scale-invariance](future-work/learning-module-improvements/support-scale-invariance.md)
  - [improve-handling-of-symmetry](future-work/learning-module-improvements/improve-handling-of-symmetry.md)
  - [use-better-priors-for-hypothesis-initialization](future-work/learning-module-improvements/use-better-priors-for-hypothesis-initialization.md)
- [motor-system-improvements](future-work/motor-system-improvements.md)
  - [implement-switching-between-learning-and-inference-focused-policies](future-work/motor-system-improvements/implement-switching-between-learning-and-inference-focused-policies.md)
  - [bottom-up-exploration-policy-for-surface-agent](future-work/motor-system-improvements/bottom-up-exploration-policy-for-surface-agent.md)
  - [model-based-exploration-policy](future-work/motor-system-improvements/model-based-exploration-policy.md)
  - [implement-efficient-saccades-driven-by-model-free-and-model-based-signals](future-work/motor-system-improvements/implement-efficient-saccades-driven-by-model-free-and-model-based-signals.md)
  - [learn-policy-using-rl](future-work/motor-system-improvements/learn-policy-using-rl.md)
  - [decompose-goals-into-subgoals-communicate](future-work/motor-system-improvements/decompose-goals-into-subgoals-communicate.md)
  - [interpret-goal-states-in-motor-system-switch-policies](future-work/motor-system-improvements/interpret-goal-states-in-motor-system-switch-policies.md)
  - [reuse-hypothesis-testing-policy-target-points](future-work/motor-system-improvements/reuse-hypothesis-testing-policy-target-points.md)
  - [implement-a-simple-cross-modal-policy-for-sensory-guidance](future-work/motor-system-improvements/implement-a-simple-cross-modal-policy-for-sensory-guidance.md)
  - [model-based-policy-to-recognize-an-object-before-moving-on-to-a-new-object](future-work/motor-system-improvements/model-based-policy-to-recognize-an-object-before-moving-on-to-a-new-object.md)
  - [policy-to-quickly-move-to-a-new-object](future-work/motor-system-improvements/policy-to-quickly-move-to-a-new-object.md)
- [voting-improvements](future-work/voting-improvements.md)
  - [use-pose-for-voting](future-work/voting-improvements/use-pose-for-voting.md)
  - [outline-routing-protocol-attention](future-work/voting-improvements/outline-routing-protocol-attention.md)
  - [generalize-voting-to-associative-connections](future-work/voting-improvements/generalize-voting-to-associative-connections.md)
  - [can-we-change-the-cmp-to-use-displacements-instead-of-locations](future-work/voting-improvements/can-we-change-the-cmp-to-use-displacements-instead-of-locations.md)
- [cmp-hierarchy-improvements](future-work/cmp-hierarchy-improvements.md)
  - [figure-out-performance-measure-and-supervision-in-heterarchy](future-work/cmp-hierarchy-improvements/figure-out-performance-measure-and-supervision-in-heterarchy.md)
  - [add-top-down-connections](future-work/cmp-hierarchy-improvements/add-top-down-connections.md)
  - [run-analyze-experiments-with-2lms-in-heterarchy-testbed](future-work/cmp-hierarchy-improvements/run-analyze-experiments-with-2lms-in-heterarchy-testbed.md)
  - [run-analyze-experiments-in-multiobject-environment-looking-at-scene-graphs](future-work/cmp-hierarchy-improvements/run-analyze-experiments-in-multiobject-environment-looking-at-scene-graphs.md)
  - [test-learning-at-different-speeds-depending-on-level-in-hierarchy](future-work/cmp-hierarchy-improvements/test-learning-at-different-speeds-depending-on-level-in-hierarchy.md)
  - [send-similarity-encoding-object-id-to-next-level-test](future-work/cmp-hierarchy-improvements/send-similarity-encoding-object-id-to-next-level-test.md)
- [environment-improvements](future-work/environment-improvements.md)
  - [make-dataset-to-test-compositional-objects](future-work/environment-improvements/make-dataset-to-test-compositional-objects.md)
  - [set-up-environment-that-allows-for-object-manipulation](future-work/environment-improvements/set-up-environment-that-allows-for-object-manipulation.md)
  - [set-up-object-manipulation-benchmark-tasks-and-evaluation-measures](future-work/environment-improvements/set-up-object-manipulation-benchmark-tasks-and-evaluation-measures.md)
  - [create-dataset-and-metrics-to-evaluate-categories-and-generalization](future-work/environment-improvements/create-dataset-and-metrics-to-evaluate-categories-and-generalization.md)
  - [create-dataset-and-metrics-to-test-new-feature-morphology-pairs](future-work/environment-improvements/create-dataset-and-metrics-to-test-new-feature-morphology-pairs.md)
- [framework-improvements](future-work/framework-improvements.md)
  - [add-infrastructure-for-multiple-agents-that-move-independently](future-work/framework-improvements/add-infrastructure-for-multiple-agents-that-move-independently.md)
  - [automate-benchmark-experiments-analysis](future-work/framework-improvements/automate-benchmark-experiments-analysis.md)
  - [add-more-wandb-logging-for-learning-unsupervised](future-work/framework-improvements/add-more-wandb-logging-for-learning-unsupervised.md)
  - [add-gpu-support-for-monty](future-work/framework-improvements/add-gpu-support-for-monty.md)
  - [use-state-class-inside-of-lms](future-work/framework-improvements/use-state-class-inside-of-lms.md)
  - [make-configs-easier-to-use](future-work/framework-improvements/make-configs-easier-to-use.md)
  - [find-faster-alternative-to-kdtree-search](future-work/framework-improvements/find-faster-alternative-to-kdtree-search.md)
- [oss-communication-improvements](future-work/oss-communication-improvements.md)
  - [organize-start-podcast-series](future-work/oss-communication-improvements/organize-start-podcast-series.md)
  - [make-more-condensed-videos-about-the-project-monty](future-work/oss-communication-improvements/make-more-condensed-videos-about-the-project-monty.md)
<!-- vale on -->
