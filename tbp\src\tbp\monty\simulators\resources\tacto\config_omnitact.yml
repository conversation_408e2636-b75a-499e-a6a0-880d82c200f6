# Copyright (c) Facebook, Inc. and its affiliates.

# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

sensor:
  name: "omnitact" 
  # By default:
  # - Sensor (camera) is placed towards x-axis
  # - Sensor origin is the same as .stl/.obj origin

  camera:
    cam0:
        position: [0.03, 0, 0] # Camera position
        orientation: [-90, 0, 90] # Euler angles, "xyz", in degrees; e.g. [0, 0, 0]: towards negative z-axis; [90, 0, -90]: towards x-axis
        yfov: 70 # Vertical field of view in degrees
        znear: 0.001 # Distance to the near clipping plane, in meters
        lightIDList: [0, 1, 2, 3, 4, 5, 6] # Select light ID list for rendering (OpenGL has max limit of 8 lights)
  
    cam1:
        position: [0.027, 0, -0.005] # Camera position
        orientation: [0, 0, 0] # Euler angles, "xyz", in degrees; e.g. [0, 0, 0]: towards negative z-axis; [90, 0, -90]: towards x-axis
        yfov: 70 # Vertical field of view in degrees
        znear: 0.001 # Distance to the near clipping plane, in meters
        lightIDList: [0, 1, 2, 5, 6, 10] # Select light ID list for rendering (OpenGL has max limit of 8 lights)

    cam2:
        position: [0.028, 0.005, 0] # Camera position
        orientation: [90, 0, 0] # Euler angles, "xyz", in degrees; e.g. [0, 0, 0]: towards negative z-axis; [90, 0, -90]: towards x-axis
        yfov: 70 # Vertical field of view in degrees
        znear: 0.001 # Distance to the near clipping plane, in meters
        lightIDList: [0, 1, 2, 6, 3, 7] # Select light ID list for rendering (OpenGL has max limit of 8 lights)
  
    cam3:
        position: [0.027, 0, 0.005] # Camera position
        orientation: [180, 0, 0] # Euler angles, "xyz", in degrees; e.g. [0, 0, 0]: towards negative z-axis; [90, 0, -90]: towards x-axis
        yfov: 70 # Vertical field of view in degrees
        znear: 0.001 # Distance to the near clipping plane, in meters
        lightIDList: [0, 1, 2, 3, 4, 8] # Select light ID list for rendering (OpenGL has max limit of 8 lights)
  
    cam4:
        position: [0.028, -0.005, 0] # Camera position
        orientation: [270, 0, 0] # Euler angles, "xyz", in degrees; e.g. [0, 0, 0]: towards negative z-axis; [90, 0, -90]: towards x-axis
        yfov: 70 # Vertical field of view in degrees
        znear: 0.001 # Distance to the near clipping plane, in meters
        lightIDList: [0, 1, 2, 4, 5, 9] # Select light ID list for rendering (OpenGL has max limit of 8 lights)
  
  gel:
    origin: [0.022, 0, 0.015] # Center coordinate of the gel, in meters
    width: 0.02 # Width of the gel, y-axis, in meters
    height: 0.03 # Height of the gel, z-axis, in meters
    curvature: True  # Model the gel as curve? True/False
    curvatureMax: 0.005  # Deformation of the gel due to convexity
    R: 0.1 # Radius of curved gel
    countW: 100 # Number of samples for horizontal direction; higher the finer details
    mesh: "omnitact.stl"

  lights:
    # Light position & properties. 
    spot: False # pyrender.SpotLight if True else pyrender.PointLight

    origin: [0, 0, 0] # center of the light plane, in meters

    # Light position can be expressed in:
    # - polar coordinates: r and theta. (in y-z plane), and x coordinate of the plane
    # - cartesian coordinates: xyz
    # Only one of the xyz or rtheta is required.
    polar: True # True: apply polar coordinates; False: apply cartesian coordinates;    
    # xyz: # cartesian coordinates
    coords: [[0, 0.01732, 0.01], [0, -0.01732, 0.01], [0, 0, -0.02]]
    # xrtheta: # polar coordinates in y-z plane
    xs: [0.030, 0.030, 0.030, 0.028, 0.028, 0.028, 0.028, 0.020, 0.022, 0.020, 0.022] # x coordinate of the y-z plane
    rs: [0.003, 0.003, 0.003, 0.005, 0.005, 0.005, 0.005, 0.005, 0.005, 0.005, 0.005] # r in polar coordinates
    thetas: [240, 0, 120, 45, 135, 225, 315, 0, 90, 180, 270] # theta in polar coordinates, in degrees
    
    colors: [[0, 0, 1], [0, 1, 0], [1, 0, 0], [0, 0, 1], [1, 0, 0], [0, 0, 1], [1, 0, 0], [0, 1, 0], [0, 1, 0], [0, 1, 0], [0, 1, 0]] # R G B color
    intensities: [0.15, 0.15, 0.15, 0.10, 0.15, 0.10, 0.15, 0.01, 0.01, 0.01, 0.01] # light intensity

  noise:
    # color:
    mean: 0 
    std: 0

  force:
    enable: True # flag for enable force feedback. When enabled, the larger normal force is, the closer object is adjusted to the sensor. 
    range_force: [0, 100] # dynamic range of forces used to simulate the elastomer deformation
    max_deformation: 0.001 # max pose depth adjustment, in meters

