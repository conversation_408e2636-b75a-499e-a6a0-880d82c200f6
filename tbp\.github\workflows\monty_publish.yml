name: <PERSON>lish

on:
  workflow_dispatch:
  workflow_run:
    workflows: [<PERSON>]
    types:
      - completed
    branches:
      - main

jobs:

  linux64_conda_monty_publish:
    name: linux64-conda-monty-publish
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    runs-on:
      group: tbp.monty
      labels: tbp-linux-x64-ubuntu2204-2core
    steps:
      - name: Checkout tbp.monty
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          path: tbp.monty
      - name: Version updated
        id: version_updated
        if: ${{ github.event_name != 'workflow_dispatch' }}
        uses: ./tbp.monty/.github/actions/version_updated
        with:
          git_sha: ${{ github.sha }}
          working_directory: tbp.monty
      - name: Install miniconda
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        run: |
          if [ ! -d ~/miniconda ]
          then
            wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O ~/miniconda.sh
            bash ~/miniconda.sh -b -p ~/miniconda
            rm ~/miniconda.sh
          fi
      - name: Install anaconda-client and conda-build
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          conda install anaconda-client conda-build
      - name: Capture expected output location
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        id: expected_output_location
        working-directory: tbp.monty
        run: |
          echo "target_platform:" > recipe/conda_build_config.yaml
          echo "  - linux-64" >> recipe/conda_build_config.yaml
          export PATH="$HOME/miniconda/bin:$PATH"
          echo "path=$(conda build recipe --output)" >> $GITHUB_OUTPUT
      - name: Build conda package
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          conda build recipe
      - name: Upload conda package
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        working-directory: tbp.monty
        env:
          ANACONDA_TOKEN: ${{ secrets.CONDA_TBP_REPOSITORY_TOKEN }}
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          anaconda --token $ANACONDA_TOKEN upload ${{ steps.expected_output_location.outputs.path }}

  osx64_conda_monty_publish:
    name: osx64-conda-monty-publish
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    runs-on: macos-latest
    steps:
      - name: Checkout tbp.monty
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          path: tbp.monty
      - name: Version updated
        id: version_updated
        if: ${{ github.event_name != 'workflow_dispatch' }}
        uses: ./tbp.monty/.github/actions/version_updated
        with:
          git_sha: ${{ github.sha }}
          working_directory: tbp.monty
      - name: Install miniconda
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        run: |
          if [ ! -d ~/miniconda ]
          then
            wget https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-arm64.sh -O ~/miniconda.sh
            bash ~/miniconda.sh -b -p ~/miniconda
            rm ~/miniconda.sh
          fi
      - name: Install anaconda-client and conda-build
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          conda install anaconda-client conda-build
      - name: Capture expected output location
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        id: expected_output_location
        working-directory: tbp.monty
        run: |
          echo "target_platform:" > recipe/conda_build_config.yaml
          echo "  - osx-64" >> recipe/conda_build_config.yaml
          export PATH="$HOME/miniconda/bin:$PATH"
          echo "path=$(conda build recipe --output)" >> $GITHUB_OUTPUT
      - name: Build conda package
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          conda build recipe
      - name: Upload conda package
        if: ${{ steps.version_updated.outputs.version_updated == 'true' || github.event_name == 'workflow_dispatch' }}
        working-directory: tbp.monty
        env:
          ANACONDA_TOKEN: ${{ secrets.CONDA_TBP_REPOSITORY_TOKEN }}
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          anaconda --token $ANACONDA_TOKEN upload ${{ steps.expected_output_location.outputs.path }}

  tag_version_monty_publish:
    name: tag-version-monty-publish
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on:
      group: tbp.monty
      labels: tbp-linux-x64-ubuntu2204-2core
    permissions:
      contents: write
    steps:
      - name: Checkout tbp.monty
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          path: tbp.monty
      - name: Version updated
        id: version_updated
        uses: ./tbp.monty/.github/actions/version_updated
        with:
          git_sha: ${{ github.sha }}
          working_directory: tbp.monty
      - name: Tag version
        if: ${{ steps.version_updated.outputs.version_updated == 'true' }}
        working-directory: tbp.monty
        run: |
          git checkout ${{ github.sha }}
          git tag v${{ steps.version_updated.outputs.version }}
          git push origin v${{ steps.version_updated.outputs.version }}
