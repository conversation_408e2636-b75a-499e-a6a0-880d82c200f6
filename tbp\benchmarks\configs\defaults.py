'''修改
代码内容描述：
    1. 默认参数设置
        default_all_noise_params：定义了一系列特征的噪声参数，包括旋转角度、高斯噪声的标准差等
        default_sensor_features：列举了默认的传感器特征列表，如位置向量、完全定义的姿态、在物体上、HSV颜色空间、主曲率的对数等
        default_all_noisy_sensor_module：配置了默认的噪声传感器模块，使用FeatureChangeSM类，并设置了相应的参数，如传感器模块ID、特征列表、是否保存原始观测值、特征变化的阈值和噪声参数
        default_feature_weights：为不同特征设置了权重，特别指出在不同光照条件下，饱和度和亮度可能变化较大，因此它们的权重较低
        default_tolerance_values和default_tolerances：分别为不同的特征设置了容忍度值，用于在特征匹配时判断相似性
    2. 证据学习模块配置
        default_evidence_lm_config：定义了证据图学习模块（EvidenceGraphLM）的配置，包括最大匹配距离、容忍度、特征权重、X百分比阈值、最大邻居数量、证据更新阈值、最大图尺寸、模型体素数量每维等
        配置了目标状态生成器（EvidenceGoalStateGenerator），设置了目标容忍度、步数因子、最小后目标成功步数、X百分比缩放因子、期望对象距离等参数
    3. 预训练模型目录
        设置了预训练模型的目录路径，通过环境变量MONTY_MODELS获取，并指定了特定版本的预训练模型文件夹（如pretrained_ycb_v10）
    4. 最小评估步骤
        定义了min_eval_steps变量，设置为20，用于控制模型评估的最小步数，以避免过早过拟合

    注：可以做消融及交叉验证实验
    （EvidenceGraphLM的作用域）配置了 EvidenceGraphLM 的各种参数，如 max_match_distance、tolerances、feature_weights 等，这些参数用于初始化和配置 EvidenceGraphLM 实例
    只构建了实例，不受新方法影响
'''

import os

import numpy as np

from tbp.monty.frameworks.models.evidence_matching import EvidenceGraphLM
from tbp.monty.frameworks.models.goal_state_generation import EvidenceGoalStateGenerator
from tbp.monty.frameworks.models.sensor_modules import FeatureChangeSM

default_all_noise_params = {
    "features": {
        "pose_vectors": 2,  # rotate by random degrees along xyz
        "hsv": 0.1,  # add gaussian noise with 0.1 std
        "principal_curvatures_log": 0.1,
        "pose_fully_defined": 0.01,  # flip bool in 1% of cases
    },
    "location": 0.002,  # add gaussian noise with 0.002 std
}

default_sensor_features = [
    "pose_vectors",
    "pose_fully_defined",
    "on_object",
    "hsv",
    "principal_curvatures_log",
]

default_all_noisy_sensor_module = dict(
    sensor_module_class=FeatureChangeSM,
    sensor_module_args=dict(
        sensor_module_id="patch",
        features=default_sensor_features,
        save_raw_obs=False,
        delta_thresholds={
            "on_object": 0,
            "distance": 0.01,
        },
        noise_params=default_all_noise_params,
    ),
)

# Everything is weighted 1, except for saturation and value which are not used.
default_feature_weights = {
    "patch": {
        # Weighting saturation and value less since these might change under different
        # lighting conditions. In the future we can extract better features in the SM
        # such as relative value changes.
        "hsv": np.array([1, 0.5, 0.5]),
    }
}

default_tolerance_values = {
    "hsv": np.array([0.1, 0.2, 0.2]),
    "principal_curvatures_log": np.ones(2),
}

default_tolerances = {
    "patch": default_tolerance_values
}  # features where weight is not specified default weight to 1

default_evidence_lm_config = dict(
    learning_module_class=EvidenceGraphLM,
    learning_module_args=dict(
        # mmd of 0.015 get higher performance but slower run time
        max_match_distance=0.01,  # =1cm
        tolerances=default_tolerances,
        feature_weights=default_feature_weights,
        # smaller threshold reduces runtime but also performance
        x_percent_threshold=20,
        # Using a smaller max_nneighbors (5 instead of 10) makes runtime faster,
        # but reduces performance a bit
        max_nneighbors=10,
        # Use this to update all hypotheses at every step as previously
        # evidence_update_threshold="all",
        # Use this to update all hypotheses with evidence > 80% of max evidence (faster)
        evidence_update_threshold="80%",
        # use_multithreading=False,
        # NOTE: Currently not used when loading pretrained graphs.
        max_graph_size=0.3,  # 30cm
        num_model_voxels_per_dim=100,
        gsg_class=EvidenceGoalStateGenerator,
        gsg_args=dict(
            goal_tolerances=dict(
                location=0.015,  # distance in meters
            ),  # Tolerance(s) when determining goal-state success
            elapsed_steps_factor=10,  # Factor that considers the number of elapsed
            # steps as a possible condition for initiating a hypothesis-testing goal
            # state; should be set to an integer reflecting a number of steps
            min_post_goal_success_steps=5,  # Number of necessary steps for a hypothesis
            # goal-state to be considered
            x_percent_scale_factor=0.75,  # Scale x-percent threshold to decide
            # when we should focus on pose rather than determining object ID; should
            # be bounded between 0:1.0; "mod" for modifier
            desired_object_distance=0.03,  # Distance from the object to the
            # agent that is considered "close enough" to the object
        ),
    ),
)

default_evidence_1lm_config = dict(learning_module_0=default_evidence_lm_config)

# NOTE: maybe lower once we have better policies
# Is not really nescessary for good performance but makes sure we don't just overfit
# on the first few points.
min_eval_steps = 20

monty_models_dir = os.getenv("MONTY_MODELS")

# v6 : Using TLS for point-normal estimation
# v7 : Updated for State class support + using new feature names like pose_vectors
# v8 : Using separate graph per input channel
# v9 : Using models trained on 14 unique rotations
# v10 : Using models trained without the semantic sensor
pretrained_dir = os.path.expanduser(
    os.path.join(monty_models_dir, "pretrained_ycb_v10")
)
