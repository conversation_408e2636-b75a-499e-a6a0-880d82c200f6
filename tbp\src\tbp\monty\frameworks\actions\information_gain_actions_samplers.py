# Copyright 2025 Thousand Brains Project
# Copyright 2024 Numenta Inc.
#
# Copyright may exist in Contributors' modifications
# and/or contributions to the work.
#
# Use of this source code is governed by the MIT
# license that can be found in the LICENSE file or at
# https://opensource.org/licenses/MIT .
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import List, Type, cast
import numpy as np
from numpy.random import Generator, default_rng

from tbp.monty.frameworks.actions.actions import (
    Action,
    LookDown,
    LookUp,
    MoveForward,
    MoveTangentially,
    OrientHorizontal,
    OrientVertical,
    QuaternionWXYZ,
    SetAgentPitch,
    SetAgentPose,
    SetSensorPitch,
    SetSensorPose,
    SetSensorRotation,
    SetYaw,
    TurnLeft,
    TurnRight,
    VectorXYZ,
)
from tbp.monty.frameworks.actions.information_gain_actions import (
    InformationGainAction,
    InformationGainMoveForward,
    InformationGainTurnLeft,
    InformationGainTurnRight,
    InformationGainLookDown,
    InformationGainLookUp,
    InformationGainMoveTangentially,
    InformationGainOrientHorizontal,
    InformationGainOrientVertical,
    InformationGainSetAgentPitch,
    InformationGainSetAgentPose,
    InformationGainSetSensorPitch,
    InformationGainSetSensorPose,
    InformationGainSetSensorRotation,
    InformationGainSetYaw,
)

__all__ = [
    "ActionSampler",
    "InformationGainActionSampler",
    "ConstantSampler",
    "UniformlyDistributedSampler",
]


class ActionSampler(ABC):
    """Declares the interface for an abstract Action factory.

    Used to generate Actions by sampling from a set of available action types.
    """

    def __init__(self, rng: Generator = None, actions: List[Type[Action]] = None):
        self.rng = rng if rng is not None else default_rng()
        self._actions = actions if actions is not None else []
        self._action_names = [action.action_name() for action in self._actions]
        self._method_names = [
            f"sample_{action_name}" for action_name in self._action_names
        ]

    @abstractmethod
    def sample_look_down(self, agent_id: str) -> LookDown:
        pass

    # 其他采样方法保持不变 ...


class InformationGainActionSampler(ActionSampler):
    """An Action factory that creates actions based on information gain."""

    def __init__(self, rng: Generator = None, actions: List[Type[Action]] = None):
        super().__init__(rng=rng, actions=actions)
        self.information_gain_range = (0.1, 1.0)  # 默认的信息增益范围

    def sample_information_gain_action(self, agent_id: str) -> InformationGainAction:
        """Sample an action based on information gain."""
        action_name = self.rng.choice(self._action_names)
        action_class_name = action_name.replace("_", "")
        info_gain_class = globals().get(f"InformationGain{action_class_name}")

        if info_gain_class:
            action = info_gain_class.sample(agent_id, self)
            # 这里可以添加逻辑来动态计算 information_gain，而不仅仅是随机值
            return action
        else:
            raise ValueError(f"No information gain class found for action: {action_name}")

    def sample_look_down(self, agent_id: str) -> InformationGainLookDown:
        rotation_degrees = self.rng.uniform(low=5.0, high=15.0)
        information_gain = self.rng.uniform(*self.information_gain_range)
        return InformationGainLookDown(
            agent_id=agent_id,
            action=LookDown(agent_id, rotation_degrees=rotation_degrees),
            information_gain=information_gain
        )

    def sample_look_up(self, agent_id: str) -> InformationGainLookUp:
        rotation_degrees = self.rng.uniform(low=5.0, high=15.0)
        information_gain = self.rng.uniform(*self.information_gain_range)
        return InformationGainLookUp(
            agent_id=agent_id,
            action=LookUp(agent_id, rotation_degrees=rotation_degrees),
            information_gain=information_gain
        )

    def sample_move_forward(self, agent_id: str) -> InformationGainMoveForward:
        distance = self.rng.uniform(low=0.01, high=0.1)
        information_gain = self.rng.uniform(*self.information_gain_range)
        return InformationGainMoveForward(
            agent_id=agent_id,
            action=MoveForward(agent_id, distance=distance),
            information_gain=information_gain
        )

    def sample_move_tangentially(self, agent_id: str) -> InformationGainMoveTangentially:
        distance = self.rng.uniform(low=0.01, high=0.1)
        direction = [self.rng.random() for _ in range(3)]
        information_gain = self.rng.uniform(*self.information_gain_range)
        return InformationGainMoveTangentially(
            agent_id=agent_id,
            action=MoveTangentially(agent_id, distance=distance, direction=direction),
            information_gain=information_gain
        )

    # 其他动作的采样方法可以按照类似的模式进行调整 ...


class ConstantSampler(ActionSampler):
    """An Action factory using constant, prespecified action parameters."""

    def __init__(
        self,
        absolute_degrees: float = 0.0,
        actions: List[Type[Action]] = None,
        direction: VectorXYZ = None,
        location: VectorXYZ = None,
        rng: Generator = None,
        rotation_degrees: float = 5.0,
        rotation_quat: QuaternionWXYZ = None,
        translation_distance: float = 0.004,
        **kwargs,
    ):
        super().__init__(actions=actions, rng=rng)
        self.absolute_degrees = absolute_degrees
        self.direction = direction if direction is not None else [0.0, 0.0, 0.0]
        self.location = location if location is not None else [0.0, 0.0, 0.0]
        self.rotation_degrees = rotation_degrees
        self.rotation_quat = rotation_quat if rotation_quat is not None else np.array([1.0, 0.0, 0.0, 0.0])
        self.translation_distance = translation_distance

    # 保持其他方法不变 ...
    # 这里可以根据需要调整，以支持信息增益相关的采样方法


class UniformlyDistributedSampler(ActionSampler):
    """An Action factory using uniformly distributed action creation parameters."""

    def __init__(
        self,
        actions: List[Type[Action]] = None,
        max_absolute_degrees: float = 360.0,
        min_absolute_degrees: float = 0.0,
        max_rotation_degrees: float = 20.0,
        min_rotation_degrees: float = 0.0,
        max_translation: float = 0.05,
        min_translation: float = 0.05,
        rng: Generator = None,
        **kwargs,
    ):
        super().__init__(actions=actions, rng=rng)
        self.max_absolute_degrees = max_absolute_degrees
        self.min_absolute_degrees = min_absolute_degrees
        self.max_rotation_degrees = max_rotation_degrees
        self.min_rotation_degrees = min_rotation_degrees
        self.max_translation = max_translation
        self.min_translation = min_translation

    # 保持其他方法不变 ...
    # 这里可以根据需要调整，以支持信息增益相关的采样方法