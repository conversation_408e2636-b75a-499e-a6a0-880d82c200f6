![](docs/figures/overview/logo.png)

# Welcome to the Monty Repository!

*An open-source, sensorimotor learning system following the principles of the neocortex.*

[![](https://github.com/thousandbrainsproject/tbp.monty/actions/workflows/monty.yml/badge.svg)](https://github.com/thousandbrainsproject/tbp.monty/actions/workflows/monty.yml)

This repository contains the first implementation of a **sensorimotor learning system** from the **Thousand Brains Project at Numenta**. We lovingly call it **<PERSON>** after <PERSON>, who proposed cortical columns as a repeating functional unit across the neocortex.

This is an open-source project by Numenta, partially funded by the Gates Foundation.

Please find our [**full documentation** here](https://thousandbrainsproject.readme.io/)

Our [**API documentation** here](https://api-monty.thousandbrains.org).

# Getting Started

You can find detailed instructions on how to install the project requirements and how to get started [here](https://thousandbrainsproject.readme.io/docs/getting-started)

# Current Performance
We regularly evaluate this system against a set of sensorimotor tasks, summarized in the **[benchmark experiments](./benchmarks/configs/)**. Any time a functional change is made to the code, these experiments are rerun, and results are updated.

You can find our current performance on these benchmarks as well as an explanation of them [here](https://thousandbrainsproject.readme.io/docs/benchmark-experiments).


# Contributing

Are you interested in contributing? Check out our tips and guidelines [here](https://thousandbrainsproject.readme.io/docs/contributing).

Before contributing, please sign our Contributor License Agreement (CLA). You can find the CLA and guidelines [here]( https://thousandbrainsproject.readme.io/docs/contributor-license-agreement).

# Disclaimer
This is not production-ready code. It is an **early beta version** that is under active development. This early beta version is functional but evolving. Expect frequent changes as we develop core features.

You can find a list of the systems **current capabilities and application criteria** [here](https://thousandbrainsproject.readme.io/docs/application-criteria).

You can find our **project road map** and details on the next features we are working on [here](https://thousandbrainsproject.readme.io/docs/project-roadmap).

# More Information and Updates
As mentioned above, we have extensive **documentation** of this project [here](https://thousandbrainsproject.readme.io/).

[![](docs/figures/overview/docs_screenshot.png)](https://thousandbrainsproject.readme.io/)

We also publish our meeting recordings on **YouTube** on the [Thousand Brains Project channel](https://www.youtube.com/@thousandbrainsproject).

[![](docs/figures/overview/youtube_screenshot.png)](https://www.youtube.com/@thousandbrainsproject)

If you want to use this code, contribute to it, ask questions or propose ideas, please consider joining [our discourse channel](https://thousandbrains.discourse.group/).

[![](docs/figures/overview/discourse_screenshot.png)](https://thousandbrains.discourse.group/)

If you would like to receive updates, follow us on [Bluesky](https://bsky.app/profile/1000brainsproj.bsky.social) or [Twitter](https://x.com/1000brainsproj) or [LinkedIn](https://www.linkedin.com/company/thousand-brains-project/).

If you have further questions or suggestions for collaborations, don't hesitate to contact us directly at **<EMAIL>**.

# Citing the Project
If you're writing a publication that references the Thousand Brains Project, please cite our TBP white paper:
```
@misc{thousandbrainsproject2024,
      title={The Thousand Brains Project: A New Paradigm for Sensorimotor Intelligence},
      author={Viviane Clay and Niels Leadholm and Jeff Hawkins},
      year={2024},
      eprint={2412.18354},
      archivePrefix={arXiv},
      primaryClass={cs.AI},
      url={https://arxiv.org/abs/2412.18354},
}
```

# License

The MIT License. See the [LICENSE](LICENSE) for details.
