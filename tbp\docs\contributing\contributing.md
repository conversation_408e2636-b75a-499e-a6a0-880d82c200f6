---
title: Ways to Contribute
---

Welcome, and thank you for your interest in contributing to <PERSON>!

We appreciate all of your contributions. Below, you will find a list of ways to get involved and help create AI based on principles of the neocortex.


# Contribute To Our Code

There are many ways in which you can contribute to the code. For some suggestions, see the [Contributing Code Guide](ways-to-contribute-to-code.md).

<PERSON> integrates code changes using GitHub Pull Requests. For details on how <PERSON> uses Pull Requests, please consult the [Contributing Pull Requests](ways-to-contribute-to-code.md) guide.

# Document

This project has many aspects and we try to document them all here in sufficient detail. However, you are the ultimate judge of whether this documentation is sufficient and what kind of information is missing. We try to update the documentation based on questions asked on our communication channels but we are always happy to get more help with this. 

Also, if you are contributing to our code, it helps everyone else to include a corresponding update to the documentation in your PR.

Please see our [guide on contributing documentation](documentation.md).

# <PERSON>tor

As a Monty user or contributor, you can help others become familiar with different aspects of <PERSON>. We are always looking for new approaches to ease the introduction to Monty and its concepts. See the [Contributing Tutorials](contributing-tutorials.md) guide on how you could add easy-to-follow tutorials for other users. You can also become active on our forum and help answer questions from others.

# Discuss

You can find the researchers, developers, and users of <PERSON> on the [Thousand Brains Forum](https://thousandbrains.discourse.group/). Please join us there for active discussion on all things Monty and Thousand Brains.

# Test Our Approach

The vision of this project is to implement a generally intelligent system that can solve a wide variety of sensorimotor tasks (at a minimum, any task the neocortex can solve/humans can perform with ease). To evaluate this, we are continually looking for test beds in which we can assess the system's capabilities. If you have an idea of how to test an important capability or you have an existing benchmark on which you want to compare our algorithm, please consider contributing these. 

# Report an Issue

If you encounter any unexpected behavior, please consider creating a [new Bug Report issue](https://github.com/thousandbrainsproject/tbp.monty/issues/new?template=01_bug_report.yml)  if it has not yet been reported.

# Comment on an Issue

We value your feedback and insights. If you have suggestions, ideas, or questions, feel free to comment on existing issues. Engaging in discussions helps shape Monty's direction and ensures that the community's needs and concerns are considered. For further details, please refer to the [Commenting on Issues Guide](comment-on-issues.md).

# Review a Pull Request

Reviewing Pull Requests is a great way to contribute to the Monty project and get familiar with the code. By participating in the review process, you help ensure the quality, security, and functionality of the Monty codebase. For details on conducting a review, please refer to the [Pull Request Review Guide](pull-requests/reviewing-a-pull-request.md)

# Propose New Functionality

We look forward to your ideas. For smaller changes, consider creating a [new Feature Request issue](https://github.com/thousandbrainsproject/tbp.monty/issues/new?template=02_feature_request.yml) and [Submit a Pull Request](pull-requests.md) (see below). For substantial changes, we have a [Request For Comments (RFC) process](request-for-comments-rfc.md) designed to let Core Maintainers understand and comment on your idea before starting implementation. If you are unsure, go ahead and create a [new Feature Request issue](https://github.com/thousandbrainsproject/tbp.monty/issues/new?template=02_feature_request.yml), and if it can benefit from an RFC, Maintainers will comment and let you know.

# Share Your Creations

We love seeing how you use Monty. If you created something interesting with it, whether a project, research paper, application, or blog post, share it with us.

- **Showcase Your Projects**: Submit your projects to be featured on our [Showcase Page](../community/project-showcase.md). This is a great way to highlight your work and inspire others.
- **Write a Blog Post**: Share your experience and insights by writing a blog post. Please share your post with the community on our [Discourse server](https://thousandbrains.discourse.group/).
- **Publish a Paper**: If you use our Monty implementation or ideas from the Thousand Brains Theory in your next publication, we would like to feature you on our [TBP-based papers](../community/tbp-based-papers.md) list and increase the visibility of your research.
- **Present at Community Events**: We host regular webinars and community meetups. If you are interested in presenting your project or research, please get in touch with us at [<EMAIL>](mailto:<EMAIL>).
- **Social Media**: Share your creations on social media using the hashtag `#1000brainsproject`. Follow us on [X](https://x.com/1000brainsproj), [Bluesky](https://bsky.app/profile/1000brainsproj.bsky.social) or [LinkedIn](https://www.linkedin.com/company/thousand-brains-project/), and subscribe to our [YouTube Channel](https://www.youtube.com/@thousandbrainsproject), or our [email list](https://thousandbrains.org/#newsletter).

# Promote Monty

Thank you for being a member of our community. By using Monty, you are already promoting Monty and the Thousand Brains Project. If you like our project, we are happy to see you mention us in your social media posts or privately to friends and colleagues.

If you want to discuss further opportunities, such as mentioning us in a blog post or newspaper article or recording an interview with us, don't hesitate to contact [<EMAIL>](mailto:<EMAIL>).

# Partner With Us

If you are a research lab, government institution, or company and you think a closer collaboration with our team could be mutually beneficial, please reach out to us at [<EMAIL>](mailto:<EMAIL>).