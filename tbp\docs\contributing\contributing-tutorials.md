---
title: Contributing Tutorials
---
> 📘 This page is about contributing Tutorials
> 
> See [here](../how-to-use-monty/tutorials.md) for current tutorials.

Tutorials are a great way for people to get hands-on-experience with our code and to learn about our approach. They should contain a mix of working code, text explaining the code, and images visualizing the concepts explained and any results from executing the code. They should be an easy-to-follow-along resource for people who are new to this project. They should not require someone to have read the rest of the documentation but can link to other documentation pages for further reading.

We deeply appreciate people who take the time to write tutorials for others. Especially if you have just come to this project recently you may be the best person to explain the code to other new-comers as you are coming to it with a fresh mind and remember the concepts you struggled with. 

If you like to contribute a tutorial on a specific topic, the best place to start is to have a look at our [existing tutorials](../how-to-use-monty/tutorials.md) to get an idea of how you can structure it. Then you can create a new .md file inside the [tutorials folder](../how-to-use-monty/tutorials/) and write your tutorial. Please make sure to include working code so that people can just copy it into a Python file and follow along. Also include visuals wherever possible. See our [contributing documentation guide](documentation.md) for more details. Finally, don't forget to add your tutorial and a short description to the list of tutorials [here](../how-to-use-monty/tutorials.md).