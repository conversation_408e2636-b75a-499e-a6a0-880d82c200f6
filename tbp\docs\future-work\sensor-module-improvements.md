---
title: Sensor Module Improvements
description: Improvements we would like to add to the sensor modules.
---

These are the things we would like to implement:

- [Extract better features](sensor-module-improvements/extract-better-features.md) (predefined, classic CV, ANNs, contrastive learning,...) #noise #accuracy #numsteps
- [Detect local and global flow](sensor-module-improvements/detect-local-and-global-flow.md) #realworld #dynamic

!snippet[../snippets/contributing-tasks.md]