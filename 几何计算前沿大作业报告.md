# 几何计算前沿大作业报告

## 基于信息增益的主动探索与概率性内部建模的MontyPlus方案


---

## 摘要

本大作业基于Jeff Hawkins的千脑智能理论，对Numenta公司开源的Monty感觉运动学习框架进行了深入研究与改进。通过复现测试发现，原始Monty系统在探索策略和内部表示方面存在显著局限：探索策略被动且效率低下，内部表示过于确定性，难以适应现实环境中的不确定性。

针对这些问题，本作业提出了两项核心改进：
1. **基于信息增益驱动的主动探索策略** - 通过实时评估动作对模型不确定性的减少程度，优先执行信息增益最大的动作
2. **引入模糊性和概率元素的内部表示机制** - 使用概率分布建模节点特征，用存在概率描述边连接关系

实验基于YCB数据集的10个物体进行验证。结果表明，改进后的MontyPlus系统在保持100%识别正确率的前提下，Used MLH指标从1%降至0%，平均探索步数减少2.23%，显著提升了系统的感知效率和鲁棒性。

**关键词：** 信息增益，主动探索，概率建模，不确定性表示，Monty系统，世界模型

---

## 1. 引言与背景

### 1.1 研究背景

当前主流人工智能主要基于大语言模型，本质上是统计推断系统。这与人类智能的核心——基于对世界的认知建模和预测能力——存在本质区别。Jeff Hawkins提出的千脑智能理论基于新皮质柱的参考系建模，为实现媲美人类智能的人工智能提供了理论基础。

### 1.2 千脑计划与Monty系统

Thousand Brains Project (TBP)是一个用于感觉运动学习系统的开源框架，遵循与人脑相同的原理。其第一个实现称为"Monty"，指的是Vernon Mountcastle，他提出了哺乳动物新皮层的柱状组织理论。

TBP与其他AI技术的关键区别在于：
- **具身的感觉运动学习为基础** - 通过与环境的持续互动进行学习
- **使用参考系学习结构化模型** - 在参考系中表示位置和旋转，学习类似CAD模型的结构化世界模型

### 1.3 问题识别

通过系统复现测试发现，Monty存在以下局限：

1. **探索策略问题**：
   - 主要依赖固定动作序列或随机策略
   - 缺乏对当前建模状态的感知与反馈机制
   - 导致大量冗余或无效观察操作

2. **内部表示问题**：
   - 采用过于确定性的建模方式
   - 使用刚性规则刻画物体特征和连接关系
   - 缺乏对噪声、遮挡及环境不确定性的容错处理

---

## 2. 相关工作

### 2.1 主动感知与信息驱动探索

主动感知(Active Perception)的基本思想是"感知是为行动服务的"，智能体应基于当前任务目标与内部状态，选择性地执行观察行为，从而最大化信息收益、最小化不确定性。

信息增益(Information Gain, IG)作为衡量观测行为价值的指标，广泛用于视觉SLAM、目标搜索、医学影像等任务中。Greedy-IG策略因其计算简单、无需训练而适合嵌入式系统和实时决策。

### 2.2 概率图建模与不确定性表示

概率图(Probabilistic Graphs)理论提供了一种用概率节点和概率边描述世界结构不确定性的方式，已应用于认知图谱、语义建图、Bayesian networks等领域。

具体如Probabilistic Roadmaps、Gaussian Graphical Models和Graph Neural Networks with Uncertainty等，均强调结构中的模糊性对提升系统鲁棒性的重要作用。

---

## 3. 核心方法与原理

### 3.1 基于信息增益驱动的主动探索策略

#### 3.1.1 核心思路

每次探索决策不再盲目或固定，而是根据当前建模状态，实时评估所有可选动作对内部模型不确定性减少的预期贡献。系统选择预期信息增益最大的动作执行，以最大化知识增长速率。

#### 3.1.2 关键定义

- **内部模型熵H(S)**: 当前Monty内部建模图中刻画不确定性程度的量化指标，基于节点特征方差进行估算
- **动作a∈A**: 可执行的探索动作集合，包括不同的观察方向、角度、位置切换等
- **信息增益**: IG(a) = H(S) - E[H(S'|a)]

其中，H(S)为当前模型的熵，E[H(S'|a)]为执行动作a后模型的预期熵。

#### 3.1.3 动作选择算法

```
Algorithm 1: 信息增益驱动的动作选择策略
Input: 当前建模状态S，候选动作集合A
Output: 最优动作a*

1. 初始化最大信息增益 IG_max ← -∞
2. for each a ∈ A do
3.     估计当前模型熵 H_cur ← H(S)
4.     模拟执行动作a后的模型状态 S' ← Simulate(S, a)
5.     估计后验模型熵 H_post ← H(S')
6.     计算信息增益 IG ← H_cur - H_post
7.     if IG > IG_max then
8.         IG_max ← IG
9.         a* ← a
10.    end if
11. end for
12. return a*
```

#### 3.1.4 动作扩展

系统从传统的四个观测方向扩展至六个维度：
- 传统动作：LookUp, LookDown, TurnLeft, TurnRight
- 新增动作：MoveForward(前向位移), MoveTangentially(切向环绕)

这些动作模拟了人类触觉感知的精细化操作，能够全面覆盖目标的三维结构。

### 3.2 引入模糊性和概率元素的内部表示机制

#### 3.2.1 核心思路

传统Monty采用确定性图结构，节点特征和边连接关系均以硬性0/1二值表示。本改进引入概率性建模机制，以自然刻画感知过程中的不确定性。

#### 3.2.2 概率节点与概率边定义

**概率节点(Probabilistic Node)**:
- 特征分布：N(μᵢ, σᵢ²) - 使用高斯分布建模节点属性
- 其中μᵢ为特征均值向量，σᵢ²为方差矩阵

**概率边(Probabilistic Edge)**:
- 存在概率：P(eᵢⱼ) ∈ [0,1] - 描述节点i和j之间连接关系的可信度

#### 3.2.3 概率建模过程

**1. 节点属性的贝叶斯更新**

设某节点的特征为一维连续变量，其先验为高斯分布：
```
p(x) = N(μ₀, σ₀²)
```

新观测值为x_obs，观测似然分布为：
```
p(x_obs|x) = N(x, σ_obs²)
```

则后验分布仍为高斯分布，其均值与方差更新为：
```
μ₁ = (σ_obs²·μ₀ + σ₀²·x_obs) / (σ₀² + σ_obs²)
σ₁² = (σ₀²·σ_obs²) / (σ₀² + σ_obs²)
```

**2. 边连接概率的Sigmoid更新**

设两个节点的连接存在概率为P(e)，初始值为0.5。每次新观测若同时观测到两节点，记共现次数为c，总更新轮数为n，则使用Sigmoid函数更新：
```
P(e) = σ(α·(c/n - 0.5))
```

其中σ是Sigmoid函数，α是控制更新敏感度的系数。

#### 3.2.4 目标识别推理过程

采用概率图建模后，目标识别转化为概率图匹配问题：

1. **节点匹配**：使用KL散度衡量节点特征分布之间的相似性
2. **边匹配**：比较对应节点对之间边存在概率的一致性
3. **整体匹配评分**：综合节点与边匹配结果，计算总匹配概率
4. **识别决策**：选择得分最高的记忆图对应的对象作为识别结果

---

## 4. 实验设计与结果

### 4.1 实验设置

**数据集**: YCB Dataset中10种常见物体(杯子、香蕉、盒子等)
**实验配置**: randrot_10distinctobj_surf_agent - 识别随机旋转角度的物体，使用表面代理
**对比组别**:
- Baseline: 原始Monty系统(随机探索+确定性建模)
- 完整改进组: MontyPlus(信息增益探索+概率建模)

### 4.2 评估指标

| 指标 | 说明 |
|------|------|
| 正确率Correct(%) | 识别准确率 |
| Used MLH(%) | 超时后使用最可能假设的比例 |
| 达到匹配时的步数 | 完成识别所需的平均步数 |
| 旋转误差(degrees) | 物体方向估计误差 |
| 运行时间(s) | 总体运行时间 |

### 4.3 实验结果

| 指标 | 基准实验 | 完整改进组 | 变化 |
|------|----------|------------|------|
| 正确率Correct(%) | 100.00 | 100.00 | 保持 |
| Used MLH(%) | 1.00 | 0.00 | ↓100% |
| 达到匹配时的步数 | 28.25 | 27.62 | ↓2.23% |
| 旋转误差(degrees) | 18.33 | 31.64 | ↑72.6% |
| 运行时间(s) | 2637 | 2636 | ↓0.04% |
| 帧运行时间(s) | 26.38 | 26.37 | ↓0.04% |

### 4.4 可视化分析

通过"马克杯"对象识别任务的可视化分析(第1、10、20、30、40步)，可以观察到：

- **第1步**: 初始建模状态较为模糊，红色高置信度区域稀疏
- **第10步**: 系统开始聚焦于目标物体主要轮廓的关键区域
- **第20-30步**: 成功捕捉到马克杯的主体结构，如杯沿、侧壁等位置
- **第40步**: 模型基本完成建模，红色区域清晰勾勒出马克杯的边界与空间分布

---

## 5. 结果分析与讨论

### 5.1 主要成果

1. **Used MLH显著改善**: 从1%降至0%，表明系统能在规定步数内完成高置信度匹配，无需依赖"猜测式"输出

2. **探索效率提升**: 平均匹配步数减少2.23%，运行时间略有下降，体现了主动探索策略的优势

3. **识别准确率保持**: 在提升效率的同时，保持了100%的识别正确率

### 5.2 改进机制分析

**主动探索策略的优势**:
- 避免重复观测和无效区域探索
- 集中资源对关键区域进行高效感知
- 加快知识图谱构建速度

**概率性建模的优势**:
- 自然刻画感知数据中的不确定性
- 在面对遮挡、噪声时保持模型稳定性
- 通过贝叶斯更新逐步提高模型置信度

### 5.3 局限性分析

**旋转误差增加**: 从18.33°增至31.64°，可能原因：
- 概率匹配机制在增加容错能力的同时，影响了方向感知的精确度
- 软性匹配策略对精确几何信息的敏感性降低

---

## 6. 总结与展望

### 6.1 主要贡献

本大作业成功实现了对Monty系统的两项核心改进：

1. **信息增益驱动的主动探索策略**: 通过实时评估动作价值，显著提升了探索效率
2. **概率性内部表示机制**: 通过引入不确定性建模，增强了系统的鲁棒性

实验结果验证了改进方案的有效性，特别是Used MLH指标的显著改善，表明系统在有限时间内完成高置信度建模的能力得到了实质性提升。

### 6.2 技术创新点

- **轻量化信息增益估算**: 避免复杂全局计算，适合实时决策
- **概率图匹配机制**: 结合KL散度与方差差距，实现软性匹配
- **模块化设计**: 支持灵活切换不同策略，便于实验验证

### 6.3 未来工作方向

1. **旋转误差优化**: 研究在保持概率建模优势的同时，提高几何精度的方法
2. **多模态融合**: 扩展到视觉-触觉等多传感器融合场景
3. **动态环境适应**: 研究在动态变化环境中的建模与识别能力
4. **计算效率优化**: 进一步优化算法复杂度，适应更大规模场景

### 6.4 实际应用前景

MontyPlus方案为具身智能体的高效感知与鲁棒推理提供了新的理论与技术参考，在机器人导航、工业检测、医疗诊断等领域具有广阔的应用前景。

---

## 参考文献

[1] Hawkins, J., et al. "A Framework for Intelligence and Cortical Function Based on Grid Cells in the Neocortex." Frontiers in Neural Circuits, 2019.

[2] Mountcastle, V. B. "The columnar organization of the neocortex." Brain, 1997.

[3] Bajcsy, R. "Active perception." Proceedings of the IEEE, 1988.

[4] MacKay, D. J. C. "Information-based objective functions for active data selection." Neural computation, 1992.

[5] Koller, D., & Friedman, N. "Probabilistic graphical models: principles and techniques." MIT press, 2009.

[6] Pearl, J. "Probabilistic reasoning in intelligent systems: networks of plausible inference." Morgan Kaufmann, 1988.

[7] Kullback, S., & Leibler, R. A. "On information and sufficiency." The annals of mathematical statistics, 1951.

[8] Lauritzen, S. L. "Graphical models." Oxford University Press, 1996.

[9] Gal, Y., & Ghahramani, Z. "Dropout as a Bayesian approximation: Representing model uncertainty in deep learning." ICML, 2016.

[10] Gibson, J. J. "The ecological approach to visual perception." Houghton Mifflin, 1979.

---
