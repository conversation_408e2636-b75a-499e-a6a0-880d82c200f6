{"columns": ["primary_target_object", "mean_objects_per_graph", "monty_steps", "monty_matching_steps", "primary_target_rotation_euler", "primary_performance", "symmetry_evidence", "individual_ts_rotation_error", "detected_rotation", "goal_states_attempted", "detected_location", "mean_graphs_per_object", "goal_state_achieved", "stepwise_performance", "individual_ts_reached_at_step", "individual_ts_performance", "highest_evidence", "result", "time", "stepwise_target_object", "detected_scale", "num_steps", "num_possible_matches", "most_likely_location", "most_likely_rotation", "TFNP", "primary_target_rotation_quat", "location_rel_body", "lm_id", "most_likely_object", "primary_target_position", "rotation_error", "primary_target_scale"], "data": [["mug", 1.0, 145, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.04827869784446947, 1.5255843390296748, 0.0006595213846287335], 1.0, 3.0, "correct", 21, "correct", 32.19389246122554, "mug", 3.1412711143493652, "mug", 1.0, 21, 1, [0.04827869784446947, 1.5255843390296748, 0.0006595213846287335], [-1.2613639698555097e-09, -1.414141657471365e-07, -3.4677215954426507e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.048278657670249914, 1.5255842113998312, 0.000659535022124064], "LM_0", "mug", [0.0, 1.5, 0.0], 0.0, 1.0], ["bowl", 1.0, 171, 28, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.01709874844364522, 1.5262877823110497, 0.07252894416912768], 1.0, 3.0, "confused", 28, "correct", 39.93795051765593, "bowl", 3.1029160022735596, "mug", 1.0, 28, 1, [0.01709874844364522, 1.5262877823110497, 0.07252894416912768], [6.978751307343297e-07, 1.7226730182351677e-07, -2.8359716024226857e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.017099367796117256, 1.526288022269123, 0.07252900867473087], "LM_0", "bowl", [0.0, 1.5, 0.0], 0.0, 1.0], ["potted_meat_can", 1.0, 92, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [0.0466503971397864, 1.4634979587491566, -0.012595344189527992], 1.0, 2.0, "confused", 21, "correct", 24.933970799296688, "potted_meat_can", 5.943916082382202, "mug", 1.0, 21, 1, [0.0466503971397864, 1.4634979587491566, -0.012595344189527992], [1.5923807169492286e-07, -3.818497617252743e-08, 1.9577323012240975e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.046650397961418635, 1.4634979150237282, -0.012595343481950974], "LM_0", "potted_meat_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["master_chef_can", 1.0, 134, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.04670856835808608, 1.4648853329989062, -0.017471676128368516], 1.0, 1.0, "confused", 21, "correct", 31.665217320797964, "master_chef_can", 2.686673164367676, "mug", 1.0, 21, 1, [-0.04670856835808608, 1.4648853329989062, -0.017471676128368516], [-9.732044985379409e-09, 1.4512756511549494e-08, 5.2308631053121705e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.04670310315014935, 1.4648881846948136, -0.01747181344030455], "LM_0", "master_chef_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["i_cups", 1.0, 175, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.042700593155055026, 1.537517373946107, -0.01144203696830286], 1.0, 4.0, "confused", 26, "correct", 40.302988965298226, "i_cups", 2.8326590061187744, "mug", 1.0, 26, 1, [-0.042700593155055026, 1.537517373946107, -0.01144203696830286], [3.150893572322168e-08, -2.7146504928383972e-08, -7.2058910034345045e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.04269878762702425, 1.5375154339290544, -0.011442031927366833], "LM_0", "i_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["spoon", 1.0, 142, 24, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.08115347375403746, 1.500694834429485, 0.03913170907242402], 1.0, 0.0, "confused", 24, "correct", 44.85396477738898, "spoon", 3.629119873046875, "mug", 1.0, 24, 1, [0.08115347375403746, 1.500694834429485, 0.03913170907242402], [3.238950197556162e-07, -1.1112985279220786e-09, 8.710127520444519e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.08114656764585267, 1.5007467485951698, 0.03918786097125007], "LM_0", "spoon", [0.0, 1.5, 0.0], 0.0, 1.0], ["b_cups", 1.0, 186, 27, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.029921242585189665, 1.530788189106696, 5.652351681443299e-05], 1.0, 4.0, "confused", 27, "correct", 39.7510521633753, "b_cups", 2.9814329147338867, "mug", 1.0, 27, 1, [0.029921242585189665, 1.530788189106696, 5.652351681443299e-05], [-4.3854034740478e-08, 1.4506789790967068e-08, 2.2723548510264056e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.029921574269422477, 1.5307884479924598, 5.65324073414739e-05], "LM_0", "b_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["pitcher_base", 1.0, 143, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.06926268455539607, 1.5904807014133693, 0.052190912693183676], 1.0, 3.0, "confused", 21, "correct", 32.74147845721651, "pitcher_base", 2.4874958992004395, "mug", 1.0, 21, 1, [-0.06926268455539607, 1.5904807014133693, 0.052190912693183676], [2.758725620563841e-08, -1.035721933136768e-07, -1.0227456002932006e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.06926462090286849, 1.5904820742147538, 0.05219117172008219], "LM_0", "pitcher_base", [0.0, 1.5, 0.0], 0.0, 1.0], ["knife", 1.0, 120, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.09668441981628999, 1.510719869793297, 0.00447514658180902], 1.0, 0.0, "confused", 21, "correct", 34.67410902521489, "knife", 2.2306649684906006, "mug", 1.0, 21, 1, [0.09668441981628999, 1.510719869793297, 0.00447514658180902], [-1.6369901593387637e-07, 1.2261876182367141e-06, -1.534748308835367e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.09669261846134418, 1.5107198938022095, 0.004474822159162554], "LM_0", "knife", [0.0, 1.5, 0.0], 0.0, 1.0], ["b_marbles", 1.0, 148, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.007847781340527781, 1.493517350414534, -0.014162004251139663], 1.0, 4.0, "confused", 26, "correct", 34.82293547201873, "b_marbles", 3.4743850231170654, "mug", 1.0, 26, 1, [-0.007847781340527781, 1.493517350414534, -0.014162004251139663], [-2.739911952083711e-10, 2.7320659380522187e-08, -2.000562264121761e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.007848392379279081, 1.49351651526853, -0.014162021583061426], "LM_0", "b_marbles", [0.0, 1.5, 0.0], 0.0, 1.0], ["h_cups", 1.0, 224, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.04473852656499761, 1.5342375734331517, 0.008428707402263815], 1.0, 4.0, "confused", 26, "correct", 42.478306590819734, "h_cups", 3.2311108112335205, "mug", 1.0, 26, 1, [-0.04473852656499761, 1.5342375734331517, 0.008428707402263815], [-2.4937908845209843e-09, -1.9323905244887e-09, -1.3092378512213604e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.044738551764022534, 1.5342375949939515, 0.008428708146116026], "LM_0", "h_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["strawberry", 1.0, 96, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.022483041979164658, 1.4941509263136845, 0.014537135200170186], 1.0, 3.0, "confused", 21, "correct", 34.17244963005974, "strawberry", 3.0171961784362793, "mug", 1.0, 21, 1, [-0.022483041979164658, 1.4941509263136845, 0.014537135200170186], [3.069171329337746e-07, 2.05738428791522e-07, 7.879674806093617e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.022480342583121683, 1.4941534238552878, 0.014536356578650333], "LM_0", "strawberry", [0.0, 1.5, 0.0], 0.0, 1.0], ["power_drill", 1.0, 96, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.06077884527306399, 1.5006505694236727, 0.012870232226401934], 1.0, 3.0, "confused", 21, "correct", 27.461190211770532, "power_drill", 2.1366989612579346, "mug", 1.0, 21, 1, [0.06077884527306399, 1.5006505694236727, 0.012870232226401934], [-5.141601114603027e-08, -4.60764666704249e-08, -6.153363247354684e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.0607783771826915, 1.5006516276213273, 0.012870252524684465], "LM_0", "power_drill", [0.0, 1.5, 0.0], 0.0, 1.0], ["padlock", 1.0, 143, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.025292629277046834, 1.5125522919067147, -0.006990440650448338], 1.0, 2.0, "confused", 21, "correct", 27.853982107152238, "padlock", 2.7421212196350098, "mug", 1.0, 21, 1, [0.025292629277046834, 1.5125522919067147, -0.006990440650448338], [3.1581362644556584e-07, -2.2243500503623926e-07, -6.641737488498684e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.025292606670351884, 1.5125523384269077, -0.006990445975906282], "LM_0", "padlock", [0.0, 1.5, 0.0], 0.0, 1.0], ["golf_ball", 1.0, 164, 31, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.020561978042439668, 1.5021215708859097, -0.0053966076227389215], 1.0, 3.0, "confused", 31, "correct", 40.15903012897385, "golf_ball", 3.4924240112304688, "mug", 1.0, 31, 1, [-0.020561978042439668, 1.5021215708859097, -0.0053966076227389215], [3.756775589794819e-09, 1.0791634756424439e-08, -3.995064025605598e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.020560822505702507, 1.5021220861254372, -0.005396609621132874], "LM_0", "golf_ball", [0.0, 1.5, 0.0], 0.0, 1.0], ["hammer", 1.0, 140, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.08936322089907862, 1.51641757013629, -0.0698195857937624], 1.0, 3.0, "confused", 21, "correct", 32.11904099562834, "hammer", 2.5822737216949463, "mug", 1.0, 21, 1, [-0.08936322089907862, 1.51641757013629, -0.0698195857937624], [2.8081628726758928e-08, 4.786865834455469e-08, 3.184652947777561e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.08936299573781488, 1.5164153812126449, -0.06981923905831437], "LM_0", "hammer", [0.0, 1.5, 0.0], 0.0, 1.0], ["softball", 1.0, 96, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.02380926064348559, 1.4916661767939705, -0.04108013907011449], 1.0, 2.0, "confused", 21, "correct", 30.913145983784357, "softball", 2.2146260738372803, "mug", 1.0, 21, 1, [0.02380926064348559, 1.4916661767939705, -0.04108013907011449], [1.3763687260527922e-08, 5.185173058324508e-08, -2.286077889700552e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.023809492301238182, 1.4916664761063327, -0.041080143688566155], "LM_0", "softball", [0.0, 1.5, 0.0], 0.0, 1.0], ["orange", 1.0, 268, 30, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.010086676395520362, 1.5317144119614134, -0.011577700108721051], 1.0, 4.0, "confused", 30, "correct", 40.488834200881485, "orange", 4.081480026245117, "mug", 1.0, 30, 1, [-0.010086676395520362, 1.5317144119614134, -0.011577700108721051], [1.2717523419955188e-08, -6.937184149052432e-09, -2.3049135434469525e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.010087424674388638, 1.5317160535103995, -0.011577657866651093], "LM_0", "orange", [0.0, 1.5, 0.0], 0.0, 1.0], ["c_lego_duplo", 1.0, 172, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.008967959137758573, 1.5108152084227153, -0.021279408716235505], 1.0, 3.0, "confused", 21, "correct", 25.5012843838844, "c_lego_duplo", 2.8903932571411133, "mug", 1.0, 21, 1, [0.008967959137758573, 1.5108152084227153, -0.021279408716235505], [-1.2614421786175082e-07, 2.5534116371506678e-08, -3.9970177339689945e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.00896796010932069, 1.5108152240037913, -0.021279409379506165], "LM_0", "c_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["c_toy_airplane", 1.0, 220, 49, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 7, [-0.001395697747141717, 1.478431184136395, 0.015033776569308405], 1.0, 7.0, "confused", 49, "correct", 77.36548115262285, "c_toy_airplane", 4.102306127548218, "mug", 1.0, 49, 1, [-0.001395697747141717, 1.478431184136395, 0.015033776569308405], [1.8762509628470994e-08, 4.756299431744732e-08, 3.6807548572767705e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.0013958466886639358, 1.4784311823554086, 0.015033785062225604], "LM_0", "c_toy_airplane", [0.0, 1.5, 0.0], 0.0, 1.0], ["b_lego_duplo", 1.0, 202, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.015661252795231652, 1.4805078469078181, 0.002746625705774469], 1.0, 4.0, "confused", 26, "correct", 35.61927747949522, "b_lego_duplo", 4.951666831970215, "mug", 1.0, 26, 1, [-0.015661252795231652, 1.4805078469078181, 0.002746625705774469], [-1.6888438061761374e-08, -7.657479278875984e-08, -1.4952429343304704e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.015661252657065985, 1.4805078724291, 0.0027466257564161397], "LM_0", "b_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["banana", 1.0, 120, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.04999610932316649, 1.4926414909632164, -0.08664602004554531], 1.0, 2.0, "confused", 21, "correct", 35.0843388480251, "banana", 2.522300958633423, "mug", 1.0, 21, 1, [0.04999610932316649, 1.4926414909632164, -0.08664602004554531], [7.212638700917754e-08, -4.298314060943333e-07, -8.665352505319452e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.04999636392422889, 1.4926427326428324, -0.08664617105846416], "LM_0", "banana", [0.0, 1.5, 0.0], 0.0, 1.0], ["nine_hole_peg_test", 1.0, 189, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [0.0682927168309078, 1.5092309757674087, 0.024522235732481114], 1.0, 2.0, "confused", 21, "correct", 28.89686457843024, "nine_hole_peg_test", 3.3018808364868164, "mug", 1.0, 21, 1, [0.0682927168309078, 1.5092309757674087, 0.024522235732481114], [1.2288010302865685e-07, 5.545917931405463e-08, -2.1705431498744936e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.06829271960677807, 1.5092310143980514, 0.024522233403470463], "LM_0", "nine_hole_peg_test", [0.0, 1.5, 0.0], 0.0, 1.0], ["tomato_soup_can", 1.0, 120, 24, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.02178593246148498, 1.4595707462146077, 0.025571353419951516], 1.0, 3.0, "confused", 24, "correct", 30.52393790334865, "tomato_soup_can", 4.8863208293914795, "mug", 1.0, 24, 1, [-0.02178593246148498, 1.4595707462146077, 0.025571353419951516], [9.940338626840203e-06, 1.0206910629953862e-05, -0.00012654160219840383], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.021786044485101727, 1.4595706636613341, 0.0255713402053138], "LM_0", "tomato_soup_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["baseball", 1.0, 332, 55, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 8, [-0.0013941509784767222, 1.5007086479617526, 0.03645743906096772], 1.0, 8.0, "confused", 55, "correct", 68.45322973317656, "baseball", 5.9697349071502686, "mug", 1.0, 55, 1, [-0.0013941509784767222, 1.5007086479617526, 0.03645743906096772], [-1.4231639448092587e-07, 5.6411132052435386e-08, 2.869372163189743e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.001395912139444873, 1.5007095122921121, 0.036457242748678634], "LM_0", "baseball", [0.0, 1.5, 0.0], 0.0, 1.0], ["g_cups", 1.0, 227, 29, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.03020814764104972, 1.4949624345337917, -0.021989609104783328], 1.0, 4.0, "confused", 29, "correct", 44.48066690958218, "g_cups", 3.7081892490386963, "mug", 1.0, 29, 1, [-0.03020814764104972, 1.4949624345337917, -0.021989609104783328], [-1.461074131721693e-09, 2.2093150594943655e-09, 1.0428786855703858e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.03020814975685856, 1.4949623541868144, -0.021989608505187185], "LM_0", "g_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["gelatin_box", 1.0, 139, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.0242177959350136, 1.4854867515376784, -0.03513005337949673], 1.0, 2.0, "confused", 21, "correct", 33.840996086191105, "gelatin_box", 2.682337760925293, "mug", 1.0, 21, 1, [0.0242177959350136, 1.4854867515376784, -0.03513005337949673], [4.8946342199837105e-08, -5.70719495374937e-08, 1.1486151335007983e-11], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.024218102396946856, 1.4854862471179302, -0.035130154683703695], "LM_0", "gelatin_box", [0.0, 1.5, 0.0], 0.0, 1.0], ["lemon", 1.0, 104, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.023987830645856602, 1.4828287742192205, 0.006343702378537796], 1.0, 3.0, "confused", 21, "correct", 30.337422686136602, "lemon", 2.738637685775757, "mug", 1.0, 21, 1, [-0.023987830645856602, 1.4828287742192205, 0.006343702378537796], [-1.0911125239494148e-07, -2.794013219916522e-07, -2.1080659515179813e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.02397364858699426, 1.4828361027200831, 0.006341299067296411], "LM_0", "lemon", [0.0, 1.5, 0.0], 0.0, 1.0], ["plum", 1.0, 444, 66, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 10, [-0.020132559413568264, 1.4790838929651904, 0.009745738836979077], 1.0, 10.0, "confused", 66, "correct", 93.83029986464999, "plum", 8.298624992370605, "mug", 1.0, 66, 1, [-0.020132559413568264, 1.4790838929651904, 0.009745738836979077], [-1.2626560780473395e-07, -9.089344640920897e-08, 1.604731065777067e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.02014041122217691, 1.4790847579672548, 0.009746393211508294], "LM_0", "plum", [0.0, 1.5, 0.0], 0.0, 1.0], ["racquetball", 1.0, 256, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.02601824599394718, 1.4905112969032095, -0.00336470047210706], 1.0, 4.0, "confused", 26, "correct", 34.56418499584817, "racquetball", 3.562255859375, "mug", 1.0, 26, 1, [-0.02601824599394718, 1.4905112969032095, -0.00336470047210706], [-8.9729868849713e-09, 1.6214467766101008e-10, 3.2924117494038723e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.02601823075771129, 1.4905113188739025, -0.003364700854350724], "LM_0", "racquetball", [0.0, 1.5, 0.0], 0.0, 1.0], ["plate", 1.0, 120, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.12730217438889505, 1.511376210164995, -0.025739881210696317], 1.0, 3.0, "confused", 21, "correct", 34.10138692558046, "plate", 2.340904951095581, "mug", 1.0, 21, 1, [0.12730217438889505, 1.511376210164995, -0.025739881210696317], [-1.384592939250937e-08, 2.5330344594174676e-08, 6.320530765161761e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.12731631726337145, 1.511367814641785, -0.025756866410615153], "LM_0", "plate", [0.0, 1.5, 0.0], 0.0, 1.0], ["pudding_box", 1.0, 111, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [0.05658951404616127, 1.518875272515805, -0.0004900572112048868], 1.0, 1.0, "confused", 21, "correct", 26.724298479012443, "pudding_box", 5.247843027114868, "mug", 1.0, 21, 1, [0.05658951404616127, 1.518875272515805, -0.0004900572112048868], [4.807383574930318e-05, -1.6852567057958376e-06, 9.20543606621566e-05], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.05658958738003382, 1.5188752749676189, -0.0004900949009547034], "LM_0", "pudding_box", [0.0, 1.5, 0.0], 0.0, 1.0], ["e_cups", 1.0, 230, 28, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.024126410023237344, 1.514196162326231, 0.019481499961677015], 1.0, 3.0, "confused", 28, "correct", 44.52505081171024, "e_cups", 3.5414340496063232, "mug", 1.0, 28, 1, [-0.024126410023237344, 1.514196162326231, 0.019481499961677015], [-1.226963295352981e-08, 1.3942190446132392e-08, -1.3488483762848794e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.02412639691499362, 1.5141960784386084, 0.019481499311588], "LM_0", "e_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["apple", 1.0, 192, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.008901138337193273, 1.5218218795701504, -0.03317250765756441], 1.0, 4.0, "confused", 26, "correct", 38.27046801078195, "apple", 3.1934280395507812, "mug", 1.0, 26, 1, [0.008901138337193273, 1.5218218795701504, -0.03317250765756441], [8.325699270173036e-09, -3.1767380158457604e-11, 3.577334635689302e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.00890036687526325, 1.5218210193950097, -0.03317252288238913], "LM_0", "apple", [0.0, 1.5, 0.0], 0.0, 1.0], ["j_cups", 1.0, 187, 27, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.01834492622437694, 1.5137056654943704, -0.03802277240133925], 1.0, 4.0, "confused", 27, "correct", 38.25178485897642, "j_cups", 2.9442641735076904, "mug", 1.0, 27, 1, [0.01834492622437694, 1.5137056654943704, -0.03802277240133925], [-3.07352254326985e-08, 5.293956933983669e-09, -6.373734588124007e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.01834485475645096, 1.5137055349584598, -0.038022772402432126], "LM_0", "j_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["foam_brick", 1.0, 188, 29, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.025773348735492526, 1.5119900354865423, -0.019900459848678966], 1.0, 4.0, "confused", 29, "correct", 37.61551783246367, "foam_brick", 5.610036849975586, "mug", 1.0, 29, 1, [0.025773348735492526, 1.5119900354865423, -0.019900459848678966], [-2.9848930023665315e-08, 2.1064966321957583e-08, -1.3163780007784034e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.025773348796873513, 1.511990049430586, -0.019900461473993172], "LM_0", "foam_brick", [0.0, 1.5, 0.0], 0.0, 1.0], ["large_marker", 1.0, 102, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.006219625676366891, 1.4946479237425423, -0.05054631826850897], 1.0, 3.0, "confused", 21, "correct", 29.051538311302593, "large_marker", 1.9118530750274658, "mug", 1.0, 21, 1, [0.006219625676366891, 1.4946479237425423, -0.05054631826850897], [-3.046577487236324e-08, -5.176630470116659e-07, 5.992637955277851e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.006193471737380048, 1.49469246091396, -0.05054006494669676], "LM_0", "large_marker", [0.0, 1.5, 0.0], 0.0, 1.0], ["peach", 1.0, 364, 47, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.002], 5, [-0.0116605146616824, 1.5272518321243649, -0.004403587150273924], 1.0, 5.0, "confused", 47, "correct", 63.48727133555072, "peach", 9.412637948989868, "mug", 1.0, 47, 1, [-0.0116605146616824, 1.5272518321243649, -0.004403587150273924], [-1.8532166940548457e-05, -7.87610069198251e-05, 0.0020872634954710147], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.011660418100068657, 1.5272513997662231, -0.004403605116993696], "LM_0", "peach", [0.0, 1.5, 0.0], 0.0, 1.0], ["phillips_screwdriver", 1.0, 120, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.07358189305260646, 1.5117030030950143, -0.04721880077665576], 1.0, 3.0, "confused", 21, "correct", 30.132343885014084, "phillips_screwdriver", 2.2597129344940186, "mug", 1.0, 21, 1, [0.07358189305260646, 1.5117030030950143, -0.04721880077665576], [-1.8822584884257116e-07, 2.4227544508361466e-07, -1.2562335736242214e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.07359490598836674, 1.5117562014543748, -0.047225929270875155], "LM_0", "phillips_screwdriver", [0.0, 1.5, 0.0], 0.0, 1.0], ["a_toy_airplane", 1.0, 92, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.013722894590835687, 1.5302182016480772, -0.12664735489539963], 1.0, 3.0, "confused", 21, "correct", 30.823183131299917, "a_toy_airplane", 1.7230520248413086, "mug", 1.0, 21, 1, [-0.013722894590835687, 1.5302182016480772, -0.12664735489539963], [4.242162964010779e-07, 4.616891394501819e-07, -4.039533099360415e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.013723930506280265, 1.5302513502230657, -0.12668851671942918], "LM_0", "a_toy_airplane", [0.0, 1.5, 0.0], 0.0, 1.0], ["e_lego_duplo", 1.0, 182, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [0.0005249305814857418, 1.5086285259618146, -0.036980900909997484], 1.0, 2.0, "confused", 21, "correct", 27.481394151484356, "e_lego_duplo", 4.2785749435424805, "mug", 1.0, 21, 1, [0.0005249305814857418, 1.5086285259618146, -0.036980900909997484], [-1.4453553695740785e-07, 3.516145056457526e-07, 4.0410121554219816e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.0005249298300300086, 1.5086285627564675, -0.036980901107723355], "LM_0", "e_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["sugar_box", 1.0, 131, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [0.015144435206448413, 1.566694882471776, 0.022835105868472337], 1.0, 2.0, "confused", 21, "correct", 28.515136037399806, "sugar_box", 4.118695974349976, "mug", 1.0, 21, 1, [0.015144435206448413, 1.566694882471776, 0.022835105868472337], [-6.193728148543687e-09, -9.100902776633336e-09, -2.761167441494966e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.015144434969368133, 1.566694853351155, 0.02283510522978194], "LM_0", "sugar_box", [0.0, 1.5, 0.0], 0.0, 1.0], ["a_colored_wood_blocks", 1.0, 95, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.06448276389810213, 1.5802306058432845, 0.006877681814289835], 1.0, 3.0, "confused", 21, "correct", 27.60932277460668, "a_colored_wood_blocks", 1.984273910522461, "mug", 1.0, 21, 1, [0.06448276389810213, 1.5802306058432845, 0.006877681814289835], [-5.671004375540414e-08, 8.020510334661975e-08, 2.9170327482716527e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.06448335178152571, 1.5802309889456472, 0.006877712421728496], "LM_0", "a_colored_wood_blocks", [0.0, 1.5, 0.0], 0.0, 1.0], ["c_cups", 1.0, 215, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 3, [0.021959196876338752, 1.4970765878466896, -0.008973479976334377], 1.0, 3.0, "confused", 26, "correct", 37.43123685935174, "c_cups", 3.4244868755340576, "mug", 1.0, 26, 1, [0.021959196876338752, 1.4970765878466896, -0.008973479976334377], [4.79145107922374e-09, 4.111142997937947e-08, 7.751774837814164e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.021959197902006913, 1.4970765748332882, -0.008973479933753428], "LM_0", "c_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["pear", 1.0, 143, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.0320628198151277, 1.5046820651474075, 0.008259561977227863], 1.0, 3.0, "confused", 21, "correct", 30.928362314296145, "pear", 2.371149778366089, "mug", 1.0, 21, 1, [-0.0320628198151277, 1.5046820651474075, 0.008259561977227863], [-7.059614395153523e-09, -4.7972815149263255e-09, 4.609885870790416e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.03205868121741111, 1.5047303272465269, 0.008261472138463153], "LM_0", "pear", [0.0, 1.5, 0.0], 0.0, 1.0], ["f_cups", 1.0, 132, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.021797440492872876, 1.4768186979970155, -0.006346577422487109], 1.0, 3.0, "confused", 21, "correct", 33.94150492429328, "f_cups", 2.557659149169922, "mug", 1.0, 21, 1, [-0.021797440492872876, 1.4768186979970155, -0.006346577422487109], [1.8054089923185844e-08, 2.2528135975399333e-08, 1.0809321361671394e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.021797523810922363, 1.4768186365132046, -0.006346578758270417], "LM_0", "f_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["wood_block", 1.0, 180, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 3, [0.001572744733378294, 1.4287364425565061, -0.04419742903969984], 1.0, 3.0, "confused", 26, "correct", 31.692463308938752, "wood_block", 4.240463018417358, "mug", 1.0, 26, 1, [0.001572744733378294, 1.4287364425565061, -0.04419742903969984], [3.693577982544113e-07, 6.767968462223592e-08, -1.1455330282166099e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.0015727416854576922, 1.4287363983427837, -0.0441974311118683], "LM_0", "wood_block", [0.0, 1.5, 0.0], 0.0, 1.0], ["d_lego_duplo", 1.0, 283, 33, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.022630136386442793, 1.4848955434587403, 0.011901941068608765], 1.0, 4.0, "confused", 33, "correct", 42.550994343440244, "d_lego_duplo", 6.2275049686431885, "mug", 1.0, 33, 1, [0.022630136386442793, 1.4848955434587403, 0.011901941068608765], [-2.1599752999900807e-06, -1.7772143149125892e-07, 2.18490081224626e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.022630138173965607, 1.4848955012394376, 0.011901942931729966], "LM_0", "d_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["b_toy_airplane", 1.0, 127, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.053462850648362696, 1.4954174295115343, -0.08384728465597016], 1.0, 3.0, "confused", 21, "correct", 31.33061777260099, "b_toy_airplane", 2.4868199825286865, "mug", 1.0, 21, 1, [-0.053462850648362696, 1.4954174295115343, -0.08384728465597016], [8.860265628584557e-08, -5.756774076234959e-07, 3.6437078875194225e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.05346293734890001, 1.4954213600433415, -0.08384961230613641], "LM_0", "b_toy_airplane", [0.0, 1.5, 0.0], 0.0, 1.0], ["b_colored_wood_blocks", 1.0, 160, 29, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 3, [0.01583639776838622, 1.4932127449009605, -0.0013951126024622385], 1.0, 3.0, "confused", 29, "correct", 36.874251356692916, "b_colored_wood_blocks", 5.379173994064331, "mug", 1.0, 29, 1, [0.01583639776838622, 1.4932127449009605, -0.0013951126024622385], [1.1319074929286233e-05, -2.546463142949168e-07, -1.734804828165303e-05], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.015836393543579134, 1.4932126845001843, -0.0013951159517239242], "LM_0", "b_colored_wood_blocks", [0.0, 1.5, 0.0], 0.0, 1.0], ["g_lego_duplo", 1.0, 115, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.024694935925308685, 1.4754508945069509, -0.14749286447833662], 1.0, 3.0, "confused", 21, "correct", 22.00373180432215, "g_lego_duplo", 1.7150089740753174, "mug", 1.0, 21, 1, [0.024694935925308685, 1.4754508945069509, -0.14749286447833662], [-2.6353850048330477e-07, -8.369984212507134e-07, -2.668130858890677e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.024690980000377317, 1.4754455455810715, -0.14748971771776215], "LM_0", "g_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["a_lego_duplo", 1.0, 140, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [-0.02965511015737373, 1.4930165138869118, 0.011883406658381125], 1.0, 2.0, "confused", 21, "correct", 30.66796677520314, "a_lego_duplo", 6.684530973434448, "mug", 1.0, 21, 1, [-0.02965511015737373, 1.4930165138869118, 0.011883406658381125], [4.4803274151770743e-10, 4.2535466088020487e-10, 1.281969664019101e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.02965511013707084, 1.4930165309775367, 0.011883406914776202], "LM_0", "a_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["mini_soccer_ball", 1.0, 111, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.028406648891900987, 1.4453198940796033, -0.025865602955566225], 1.0, 3.0, "confused", 21, "correct", 31.14902674705282, "mini_soccer_ball", 2.283013105392456, "mug", 1.0, 21, 1, [0.028406648891900987, 1.4453198940796033, -0.025865602955566225], [8.28683273995448e-08, 1.0260975746706594e-07, 5.259233835479154e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.028406678382164898, 1.445322011336761, -0.025865440544014403], "LM_0", "mini_soccer_ball", [0.0, 1.5, 0.0], 0.0, 1.0], ["medium_clamp", 1.0, 267, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.008196757427818242, 1.4989724402952513, 0.01691420868028582], 1.0, 1.0, "confused", 21, "correct", 36.437304122341736, "medium_clamp", 3.3204331398010254, "mug", 1.0, 21, 1, [-0.008196757427818242, 1.4989724402952513, 0.01691420868028582], [-2.566721470935756e-08, -2.421103234067718e-07, 1.1348102687734508e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.008196768572761423, 1.4989713949682995, 0.01691424625790631], "LM_0", "medium_clamp", [0.0, 1.5, 0.0], 0.0, 1.0], ["a_marbles", 1.0, 84, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.0008385735072723715, 1.471905452915518, -0.027798889919995334], 1.0, 3.0, "confused", 21, "correct", 31.226854801914573, "a_marbles", 2.008439064025879, "mug", 1.0, 21, 1, [-0.0008385735072723715, 1.471905452915518, -0.027798889919995334], [1.387917051290641e-07, -1.1661007573055676e-07, 4.252091525062251e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.0008363305514484075, 1.4719129034904983, -0.0277973803964281], "LM_0", "a_marbles", [0.0, 1.5, 0.0], 0.0, 1.0], ["extra_large_clamp", 1.0, 160, 23, [0, 0, 0], "correct", 0, 0.0909, [4.297, 357.836, 1.911], 2, [-0.050589249553175314, 1.4869648544128544, -0.02406575675164632], 1.0, 1.0, "confused", 23, "correct", 31.68014339873748, "extra_large_clamp", 2.9076569080352783, "mug", 1.0, 23, 1, [-0.050589249553175314, 1.4869648544128544, -0.02406575675164632], [4.297471712901697, -2.1644011013563675, 1.9105909471585405], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.04642853764058283, 1.4895094472553752, -0.02736683785930301], "LM_0", "extra_large_clamp", [0.0, 1.5, 0.0], 0.0909, 1.0], ["d_cups", 1.0, 207, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.013619373402337837, 1.5340806192635654, 0.027395432745833517], 1.0, 4.0, "confused", 26, "correct", 40.76337474644447, "d_cups", 2.965078830718994, "mug", 1.0, 26, 1, [0.013619373402337837, 1.5340806192635654, 0.027395432745833517], [-1.1186024097457567e-08, 2.838454219925301e-10, 2.4272754022858302e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.013619272903943713, 1.5340805609937538, 0.02739543323998253], "LM_0", "d_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["e_toy_airplane", 1.0, 99, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [0.000590468557303349, 1.5306763228719786, -0.005605576240353792], 1.0, 1.0, "confused", 21, "correct", 34.52816142363956, "e_toy_airplane", 2.6754541397094727, "mug", 1.0, 21, 1, [0.000590468557303349, 1.5306763228719786, -0.005605576240353792], [7.70445915618136e-09, 3.5625735214756576e-07, -2.076175056360565e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.0005884034200349967, 1.530676487523676, -0.0056058399299580425], "LM_0", "e_toy_airplane", [0.0, 1.5, 0.0], 0.0, 1.0], ["adjustable_wrench", 1.0, 95, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.0603677834662528, 1.4962811908320817, -0.07578768616123087], 1.0, 2.0, "confused", 21, "correct", 31.474524793619473, "adjustable_wrench", 2.2551019191741943, "mug", 1.0, 21, 1, [-0.0603677834662528, 1.4962811908320817, -0.07578768616123087], [-4.373339822683372e-07, -1.234025815360369e-06, -9.875240275947209e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.06075777783067664, 1.4953284754166085, -0.07563968492183148], "LM_0", "adjustable_wrench", [0.0, 1.5, 0.0], 0.0, 1.0], ["rubiks_cube", 1.0, 112, 22, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.02930559344083395, 1.5204339632141166, -0.005036854134239618], 1.0, 3.0, "confused", 22, "correct", 32.37461817601978, "rubiks_cube", 4.858566999435425, "mug", 1.0, 22, 1, [0.02930559344083395, 1.5204339632141166, -0.005036854134239618], [-6.709667558069438e-08, 1.1755898785866204e-07, -5.928232068782541e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.02930559299260976, 1.520434016676451, -0.005036851441924679], "LM_0", "rubiks_cube", [0.0, 1.5, 0.0], 0.0, 1.0], ["f_lego_duplo", 1.0, 311, 30, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.0016064534295260613, 1.5084366509694163, 0.016324150597512407], 1.0, 4.0, "confused", 30, "correct", 39.00383447137875, "f_lego_duplo", 5.821487903594971, "mug", 1.0, 30, 1, [-0.0016064534295260613, 1.5084366509694163, 0.016324150597512407], [-4.71963760598312e-07, -1.4310980850335577e-07, -2.5634127067004607e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.001606452840190905, 1.5084366383921222, 0.016324149916447147], "LM_0", "f_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["a_cups", 1.0, 215, 40, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 6, [-0.006845786341020503, 1.4973560600089886, 0.022451868728371092], 1.0, 6.0, "confused", 40, "correct", 61.27737668923544, "a_cups", 4.1615190505981445, "mug", 1.0, 40, 1, [-0.006845786341020503, 1.4973560600089886, 0.022451868728371092], [-2.385638231181478e-08, -3.042943530393444e-08, 6.144458183883027e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.00684580002015771, 1.4973559939512933, 0.022451868581220377], "LM_0", "a_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["skillet_lid", 1.0, 463, 66, [0, 0, 0], "confused", 0, null, [102.901, 1.895, 272.832], 9, [0.04774922335221679, 1.5038103534407417, -0.05245105966021918], 1.0, 3.0, "confused", 66, "confused", 15.325503842181295, "extra_large_clamp", 8.560459852218628, "mug", 1.0, 66, 1, [0.04774922335221679, 1.5038103534407417, -0.05245105966021918], [102.90125130007165, 1.8946119458547988, -87.16790236367773], "target_not_matched_(FN)", [1.0, 0.0, 0.0, 0.0], [0.04667621600455177, 1.5015561152206516, 0.01556694099762057], "LM_0", "extra_large_clamp", [0.0, 1.5, 0.0], null, 1.0], ["sponge", 1.0, 98, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.02986365842094188, 1.5024000531537536, -0.04753529501231149], 1.0, 3.0, "confused", 21, "correct", 31.4240374470404, "sponge", 2.5127768516540527, "mug", 1.0, 21, 1, [-0.02986365842094188, 1.5024000531537536, -0.04753529501231149], [-1.0021283878882292e-07, 3.520474554712121e-08, 7.2803219238675965e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.02986381864651801, 1.502400369450085, -0.04753535262595907], "LM_0", "sponge", [0.0, 1.5, 0.0], 0.0, 1.0], ["tennis_ball", 1.0, 372, 43, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 6, [-0.02116572581682372, 1.493515547957453, -0.025189892291222177], 1.0, 6.0, "confused", 43, "correct", 59.407255310975664, "tennis_ball", 5.937934160232544, "mug", 1.0, 43, 1, [-0.02116572581682372, 1.493515547957453, -0.025189892291222177], [8.23555070277191e-09, 8.39231717692506e-08, 3.731314733903993e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.021158390157550628, 1.4935274668989063, -0.02519018621834163], "LM_0", "tennis_ball", [0.0, 1.5, 0.0], 0.0, 1.0], ["spatula", 1.0, 115, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.12192923147303711, 1.4969987846057842, -0.06111466274807363], 1.0, 3.0, "confused", 21, "correct", 34.79965686190356, "spatula", 2.9373512268066406, "mug", 1.0, 21, 1, [-0.12192923147303711, 1.4969987846057842, -0.06111466274807363], [3.270665878086279e-08, 2.6525315882434823e-07, 5.395772504913877e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.12175552002858901, 1.496492982564294, -0.06099144101834385], "LM_0", "spatula", [0.0, 1.5, 0.0], 0.0, 1.0], ["d_toy_airplane", 1.0, 3061, 500, [0, 0, 0], "correct_mlh", 0, null, [null, null, null], 13, [null, null, null], 1.0, 11.0, "confused_mlh", null, "time_out", 770.7491363838127, "c_toy_airplane^e_toy_airplane^d_toy_airplane", 40.54767107963562, "mug", null, 500, 3, [-0.0061656997018358995, 1.5160834646003778, 0.0036585847187743407], [-7.107110341276853e-09, 9.787807171189012e-08, 3.9706202960508915e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.006162935772082101, 1.516082567118756, 0.003658789953301088], "LM_0", "d_toy_airplane", [0.0, 1.5, 0.0], 0.0, 1.0], ["chain", 1.0, 5000, 14, [0, 0, 0], "correct_mlh", 0, null, [null, null, null], 2, [null, null, null], 1.0, 2.0, "confused_mlh", null, "time_out", 16.25119281725015, "chain", 1.578585147857666, "mug", null, 14, 1, [-0.15879278733537833, 1.494758885118274, 0.02154421198825293], [-1.2424319258925178e-07, 4.390522666713327e-07, 1.3491317283133785e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.15141925235774265, 1.4891544788861557, 0.02612133570968665], "LM_0", "chain", [0.0, 1.5, 0.0], 0.0, 1.0], ["scissors", 1.0, 154, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.004112996591524591, 1.5046557836834022, 0.05410382059474984], 1.0, 1.0, "confused", 21, "correct", 32.018492725413864, "scissors", 2.296057939529419, "mug", 1.0, 21, 1, [-0.004112996591524591, 1.5046557836834022, 0.05410382059474984], [1.9729248048608046e-07, 2.0107247874050267e-07, -4.0382850067022937e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.004111261216923142, 1.5046550793909932, 0.05410703188751412], "LM_0", "scissors", [0.0, 1.5, 0.0], 0.0, 1.0], ["mustard_bottle", 1.0, 128, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.04669000455359485, 1.419508580462143, -0.01248534385125952], 1.0, 3.0, "confused", 21, "correct", 32.75971002257706, "mustard_bottle", 2.347003936767578, "mug", 1.0, 21, 1, [-0.04669000455359485, 1.419508580462143, -0.01248534385125952], [-5.712573753028095e-08, 4.0561905191513053e-07, 6.495054640409139e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.04669157815259967, 1.4195070507531236, -0.012485787169302697], "LM_0", "mustard_bottle", [0.0, 1.5, 0.0], 0.0, 1.0], ["bleach_cleanser", 1.0, 108, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.0014005658321301832, 1.6231980537024178, 0.00873108627304419], 1.0, 3.0, "confused", 21, "correct", 29.054244386929202, "bleach_cleanser", 2.1795570850372314, "mug", 1.0, 21, 1, [0.0014005658321301832, 1.6231980537024178, 0.00873108627304419], [1.1208552123932394e-07, 3.5619578933115134e-08, 1.4416263329270406e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.0013991339648871289, 1.6231972288383723, 0.008731127128962396], "LM_0", "bleach_cleanser", [0.0, 1.5, 0.0], 0.0, 1.0], ["tuna_fish_can", 1.0, 135, 28, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.02772779843858247, 1.5062521598505865, -0.03146191369069118], 1.0, 4.0, "confused", 28, "correct", 32.92291185265618, "tuna_fish_can", 3.1852948665618896, "mug", 1.0, 28, 1, [0.02772779843858247, 1.5062521598505865, -0.03146191369069118], [-4.205806027038188e-09, 9.487754914761617e-08, 1.5419872473725607e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.02772777592963985, 1.5062505608657086, -0.03146190731844463], "LM_0", "tuna_fish_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["cracker_box", 1.0, 122, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.03184019771699553, 1.5761458566348552, -0.006321286281927668], 1.0, 3.0, "confused", 21, "correct", 30.202159198624194, "cracker_box", 3.585531234741211, "mug", 1.0, 21, 1, [0.03184019771699553, 1.5761458566348552, -0.006321286281927668], [-1.2667574328654016e-07, -1.6994862657185495e-07, 3.966528113372399e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.031840198714671156, 1.5761458957956196, -0.00632128298722591], "LM_0", "cracker_box", [0.0, 1.5, 0.0], 0.0, 1.0], ["fork", 1.0, 106, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.0962974059814378, 1.4942711918364702, 0.011038192904899534], 1.0, 1.0, "confused", 21, "correct", 34.346303529159215, "fork", 2.7106549739837646, "mug", 1.0, 21, 1, [-0.0962974059814378, 1.4942711918364702, 0.011038192904899534], [3.459114004201396e-09, -1.5654893697663043e-07, 4.753650646128803e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.09635164056884264, 1.4944504492246633, 0.011020375376106938], "LM_0", "fork", [0.0, 1.5, 0.0], 0.0, 1.0], ["large_clamp", 1.0, 93, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.03953445869573176, 1.4892287668672668, 0.038657488049244404], 1.0, 3.0, "confused", 21, "correct", 29.69844553957842, "large_clamp", 2.161733865737915, "mug", 1.0, 21, 1, [0.03953445869573176, 1.4892287668672668, 0.038657488049244404], [2.2369694927138885e-07, 1.1902515105016999e-06, 8.096323919518863e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.03953448835571541, 1.4892284618245861, 0.03865756463441112], "LM_0", "large_clamp", [0.0, 1.5, 0.0], 0.0, 1.0], ["dice", 1.0, 84, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.008403778224524885, 1.5052308849203762, -0.00309542575117003], 1.0, 3.0, "confused", 21, "correct", 33.59601015642152, "dice", 2.334017038345337, "mug", 1.0, 21, 1, [0.008403778224524885, 1.5052308849203762, -0.00309542575117003], [2.5415967851851892e-09, 1.397519315372913e-07, -9.443684653472572e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.008401287497639156, 1.505226918269893, -0.003095559976135689], "LM_0", "dice", [0.0, 1.5, 0.0], 0.0, 1.0], ["flat_screwdriver", 1.0, 107, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.0741359830097426, 1.5115612333920456, 0.053381747711566424], 1.0, 0.0, "confused", 21, "correct", 34.35134276519298, "flat_screwdriver", 2.784311056137085, "mug", 1.0, 21, 1, [0.0741359830097426, 1.5115612333920456, 0.053381747711566424], [-2.332205989743868e-08, -3.0221635672153827e-07, 1.2298390445681771e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.0741365922516242, 1.511559674978936, 0.05338221980156817], "LM_0", "flat_screwdriver", [0.0, 1.5, 0.0], 0.0, 1.0], ["mug", 1.0, 192, 31, [0, 90, 0], "correct", 5, 0.1432, [178.87, 333.814, 179.95], 4, [0.028937824684812916, 1.4823926717832143, -0.007742167826543724], 1.0, 3.0, "correct", 31, "correct", 39.39496466554794, "mug", 3.922875165939331, "mug", 1.0, 31, 1, [0.028937824684812916, 1.4823926717832143, -0.007742167826543724], [178.86950411661493, -26.185509334751373, 179.95040381793928], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.03219230708524666, 1.4810735300433442, 0.03689071755658005], "LM_0", "mug", [0.0, 1.5, 0.0], 0.1432, 1.0], ["bowl", 1.0, 190, 21, [0, 90, 0], "correct", 0, 0.0, [92.115, 90.0, 92.115], 3, [-0.004365754347761016, 1.513278344833432, 0.0709927248402559], 1.0, 3.0, "confused", 21, "correct", 28.672922208457877, "bowl", 2.979689121246338, "mug", 1.0, 21, 1, [-0.004365754347761016, 1.513278344833432, 0.0709927248402559], [92.11476273035329, 89.99987913749152, 92.11476023172347], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.07099276007775276, 1.5132781633461567, 0.0043656846882122995], "LM_0", "bowl", [0.0, 1.5, 0.0], 0.0, 1.0], ["potted_meat_can", 1.0, 175, 35, [0, 90, 0], "correct", 5, 0.0354, [89.407, 89.895, 91.434], 4, [-0.04967507628961598, 1.5399709770035777, -0.010939672849114946], 1.0, 4.0, "confused", 35, "correct", 46.75950173629637, "potted_meat_can", 7.0193891525268555, "mug", 1.0, 35, 1, [-0.04967507628961598, 1.5399709770035777, -0.010939672849114946], [89.40652322393278, 89.89533671094456, 91.43374535780119], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.014340330455978332, 1.5400220116828711, 0.04988768781444888], "LM_0", "potted_meat_can", [0.0, 1.5, 0.0], 0.0354, 1.0], ["master_chef_can", 1.0, 142, 21, [0, 90, 0], "correct", 0, 0.0029, [291.204, 89.834, 291.188], 3, [0.025848645491903646, 1.5695288901712168, -0.042826473229302484], 1.0, 2.0, "confused", 21, "correct", 28.103485671888276, "master_chef_can", 2.745764970779419, "mug", 1.0, 21, 1, [0.025848645491903646, 1.5695288901712168, -0.042826473229302484], [-68.79617759898764, 89.8335958920239, -68.81184683602653], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.042619624037500914, 1.5694657304798985, -0.025850191360929184], "LM_0", "master_chef_can", [0.0, 1.5, 0.0], 0.0029, 1.0], ["i_cups", 1.0, 189, 29, [0, 90, 0], "correct", 5, 0.0, [83.691, 90.0, 83.691], 4, [-0.0009338500131422234, 1.525473247559814, -0.04025647892895254], 1.0, 3.0, "confused", 29, "correct", 37.66815043236525, "i_cups", 3.441817045211792, "mug", 1.0, 29, 1, [-0.0009338500131422234, 1.525473247559814, -0.04025647892895254], [83.69097186419334, 89.9997691003939, 83.69097452260026], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.04025656197560777, 1.5254734294758607, 0.0009338524602400457], "LM_0", "i_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["spoon", 1.0, 548, 99, [0, 90, 0], "correct", 0, 0.0007, [70.782, 89.961, 70.781], 7, [-0.07592821436216177, 1.5024502804105897, -0.021033254186083337], 1.0, 2.0, "confused", 99, "correct", 156.0378114989093, "spoon", 9.303412914276123, "mug", 1.0, 99, 1, [-0.07592821436216177, 1.5024502804105897, -0.021033254186083337], [70.78182370710701, 89.9614676020505, 70.78117403544177], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.020991622031539262, 1.502435338207805, 0.07592715150444818], "LM_0", "spoon", [0.0, 1.5, 0.0], 0.0007, 1.0], ["b_cups", 1.0, 276, 44, [0, 90, 0], "correct", 5, 0.0, [87.511, 89.999, 87.511], 6, [0.005053208338943134, 1.4960624129022069, -0.018373753769617054], 1.0, 6.0, "confused", 44, "correct", 62.08574244462751, "b_cups", 4.749306917190552, "mug", 1.0, 44, 1, [0.005053208338943134, 1.4960624129022069, -0.018373753769617054], [87.51138143258552, 89.99941217141937, 87.51138105649923], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.01837370103286829, 1.4960625416373103, -0.005053206216037583], "LM_0", "b_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["pitcher_base", 1.0, 162, 21, [0, 90, 0], "correct", 0, 0.0, [83.791, 90.0, 83.791], 2, [0.05865318677064275, 1.581298945206668, -0.05514852549477604], 1.0, 2.0, "confused", 21, "correct", 30.499433585906356, "pitcher_base", 4.207207918167114, "mug", 1.0, 21, 1, [0.05865318677064275, 1.581298945206668, -0.05514852549477604], [83.79106282613722, 89.99993638956599, 83.79101230012277], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.05514851448880738, 1.5812990511501122, -0.05865318216764966], "LM_0", "pitcher_base", [0.0, 1.5, 0.0], 0.0, 1.0], ["knife", 1.0, 2705, 500, [0, 90, 0], "correct_mlh", 0, null, [null, null, null], 16, [null, null, null], 1.0, 1.0, "confused_mlh", null, "time_out", 739.9891562876035, "spoon^knife^fork", 49.28910422325134, "mug", null, 500, 3, [0.10482130864566663, 1.5090404005486104, 0.0075060406615768335], [-165.6870564965501, 85.78025310297318, -170.05106183358194], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.0035888825247881225, 1.50762220657908, -0.10565611101879469], "LM_0", "knife", [0.0, 1.5, 0.0], 0.1059, 1.0], ["b_marbles", 1.0, 252, 42, [0, 90, 0], "correct", 5, 0.4721, [357.159, 0.044, 345.565], 6, [0.010107218677474768, 1.5138158575014429, 0.004504583930673626], 1.0, 2.0, "confused", 42, "correct", 51.0611593558825, "b_marbles", 5.122168064117432, "mug", 1.0, 42, 1, [0.010107218677474768, 1.5138158575014429, 0.004504583930673626], [-2.8406919600921485, 0.0440328928117608, -14.435104124819354], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.01306778681128808, 1.5102390368799, 0.003814521512044391], "LM_0", "b_marbles", [0.0, 1.5, 0.0], 0.4721, 1.0], ["h_cups", 1.0, 558, 67, [0, 90, 0], "correct", 5, 0.0753, [359.868, 1.0, 2.128], 9, [0.019154544714827318, 1.5109423476355928, -0.03287382688623493], 1.0, 8.0, "confused", 67, "correct", 90.96354591720261, "h_cups", 6.9747819900512695, "mug", 1.0, 67, 1, [0.019154544714827318, 1.5109423476355928, -0.03287382688623493], [-0.13194907681236143, 1.0003262898523884, 2.1275596574056617], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.018533323966593133, 1.5143526088114387, -0.0333120297111513], "LM_0", "h_cups", [0.0, 1.5, 0.0], 0.0753, 1.0], ["strawberry", 1.0, 208, 41, [0, 90, 0], "correct", 5, 0.129, [196.314, 78.356, 191.125], 5, [0.007354815055648451, 1.5172415219864408, 0.016803254343520424], 1.0, 5.0, "confused", 41, "correct", 44.563387476289634, "strawberry", 5.391801834106445, "mug", 1.0, 41, 1, [0.007354815055648451, 1.5172415219864408, 0.016803254343520424], [-163.68580834696968, 78.3555146700074, -168.87510580342686], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.013665322271707477, 1.512321178370196, -0.010370315540485266], "LM_0", "strawberry", [0.0, 1.5, 0.0], 0.129, 1.0], ["power_drill", 1.0, 124, 22, [0, 90, 0], "correct", 0, 0.1837, [188.867, 79.538, 187.704], 3, [-0.018731635361366458, 1.4843859260382424, 0.08886952253888722], 1.0, 3.0, "confused", 22, "correct", 14.809025626491346, "power_drill", 2.6196129322052, "mug", 1.0, 22, 1, [-0.018731635361366458, 1.4843859260382424, 0.08886952253888722], [-171.1328448291161, 79.53831832337002, -172.29604719062652], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.09287910042741307, 1.4835425720776718, 0.0030255532147271114], "LM_0", "power_drill", [0.0, 1.5, 0.0], 0.1837, 1.0], ["padlock", 1.0, 135, 26, [0, 90, 0], "correct", 5, 0.0506, [260.97, 88.897, 263.652], 4, [-0.028200486385885543, 1.5055693035986286, 0.002255067657767642], 1.0, 3.0, "confused", 26, "correct", 29.34747465912875, "padlock", 3.3153610229492188, "mug", 1.0, 26, 1, [-0.028200486385885543, 1.5055693035986286, 0.002255067657767642], [-99.0297264656893, 88.89683083029756, -96.34828834217952], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.003150474072910794, 1.5052151754286236, 0.028487114911334595], "LM_0", "padlock", [0.0, 1.5, 0.0], 0.0506, 1.0], ["golf_ball", 1.0, 204, 26, [0, 90, 0], "correct", 5, 0.3212, [169.072, 349.158, 188.239], 3, [-0.00652178472095411, 1.4860492522770927, 0.012842475822958254], 1.0, 3.0, "confused", 26, "correct", 30.75951969473951, "golf_ball", 3.488942861557007, "mug", 1.0, 26, 1, [-0.00652178472095411, 1.4860492522770927, 0.012842475822958254], [169.07234101596532, -10.842239359131566, -171.76067553814337], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.005547003417445106, 1.4871883199406468, -0.016068202490570324], "LM_0", "golf_ball", [0.0, 1.5, 0.0], 0.3212, 1.0], ["hammer", 1.0, 152, 21, [0, 90, 0], "correct", 0, 0.0002, [256.352, 89.99, 256.355], 2, [0.010292713100302081, 1.4855787805412959, -0.1433846227745571], 1.0, 2.0, "confused", 21, "correct", 32.62318760503298, "hammer", 2.7205560207366943, "mug", 1.0, 21, 1, [0.010292713100302081, 1.4855787805412959, -0.1433846227745571], [-103.64836245483772, 89.98964357204211, -103.64455440038242], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.14337457508822168, 1.485602407195025, -0.01029329843060321], "LM_0", "hammer", [0.0, 1.5, 0.0], 0.0002, 1.0], ["softball", 1.0, 148, 26, [0, 90, 0], "correct", 5, 0.0, [118.315, 0.236, 44.316], 3, [-0.02996897101028487, 1.488694308778657, 0.03679098431594534], 1.0, 3.0, "confused", 26, "correct", 31.509453534691243, "softball", 5.0316197872161865, "mug", 1.0, 26, 1, [-0.02996897101028487, 1.488694308778657, 0.03679098431594534], [118.31529951672664, 0.2360147621002133, 44.315767744861006], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.0034301729678659835, 1.459173257310465, -0.02703262280246901], "LM_0", "softball", [0.0, 1.5, 0.0], 0.0, 1.0], ["orange", 1.0, 212, 26, [0, 90, 0], "correct", 5, 0.2096, [131.993, 320.013, 251.0], 3, [0.015374165679765031, 1.4762316313134103, 0.02636389754657187], 1.0, 3.0, "confused", 26, "correct", 31.88734514294658, "orange", 3.5409128665924072, "mug", 1.0, 26, 1, [0.015374165679765031, 1.4762316313134103, 0.02636389754657187], [131.99264279497078, -39.98711605241087, -108.99977626298497], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.01726082884589254, 1.469451790679635, -0.01650251165541823], "LM_0", "orange", [0.0, 1.5, 0.0], 0.2096, 1.0], ["c_lego_duplo", 1.0, 298, 25, [0, 90, 0], "correct", 0, 0.0, [22.733, 90.0, 22.733], 3, [-0.011081326158167391, 1.5088324961130697, -0.01972422648146743], 1.0, 2.0, "confused", 25, "correct", 32.41835699878995, "c_lego_duplo", 5.268147945404053, "mug", 1.0, 25, 1, [-0.011081326158167391, 1.5088324961130697, -0.01972422648146743], [22.733031927569424, 89.99986313766117, 22.732893899691163], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.019724238629404246, 1.5088325387895505, 0.011081326798227684], "LM_0", "c_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["c_toy_airplane", 1.0, 294, 35, [0, 90, 0], "correct", 5, 0.1449, [2.005, 330.81, 358.635], 4, [-0.012152341554724248, 1.515932354147108, -0.00048153030555529274], 1.0, 4.0, "confused", 35, "correct", 48.621187476645055, "c_toy_airplane", 4.696943998336792, "mug", 1.0, 35, 1, [-0.012152341554724248, 1.515932354147108, -0.00048153030555529274], [2.005138747185932, -29.1895080386169, -1.3645203670016497], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.010601083831207364, 1.51476492237262, -0.005027338039061744], "LM_0", "c_toy_airplane", [0.0, 1.5, 0.0], 0.1449, 1.0], ["b_lego_duplo", 1.0, 217, 29, [0, 90, 0], "correct", 5, 0.0055, [0.742, 359.721, 4.307], 3, [0.0030093169223963014, 1.4805664399886005, -0.01687747961666205], 1.0, 3.0, "confused", 29, "correct", 31.67143333048875, "b_lego_duplo", 5.4619481563568115, "mug", 1.0, 29, 1, [0.0030093169223963014, 1.4805664399886005, -0.01687747961666205], [0.7418949384498739, -0.27863849779673416, 4.30653615580465], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.0045857652493433345, 1.481282124475212, -0.01691946971646054], "LM_0", "b_lego_duplo", [0.0, 1.5, 0.0], 0.0055, 1.0], ["banana", 1.0, 170, 27, [0, 90, 0], "correct", 0, 0.1127, [286.48699999999997, 83.929, 284.28499999999997], 4, [0.0490165152074019, 1.489143233915484, -0.07821934301399858], 1.0, 1.0, "confused", 27, "correct", 37.35288449871097, "banana", 3.240271806716919, "mug", 1.0, 27, 1, [0.0490165152074019, 1.489143233915484, -0.07821934301399858], [-73.51333923521412, 83.9290030628074, -75.71463044621309], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.07996169442307978, 1.482780406371956, -0.05051794716809403], "LM_0", "banana", [0.0, 1.5, 0.0], 0.1127, 1.0], ["nine_hole_peg_test", 1.0, 310, 31, [0, 90, 0], "correct", 5, 0.0465, [0.058, 359.865, 1.501], 4, [-0.02492311179029222, 1.49814766256828, 0.07062972440088476], 1.0, 2.0, "confused", 31, "correct", 29.86895063824069, "nine_hole_peg_test", 5.8092498779296875, "mug", 1.0, 31, 1, [-0.02492311179029222, 1.49814766256828, 0.07062972440088476], [0.05841730515037227, -0.13457226919845014, 1.5010655012812069], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.006724445176080949, 1.4975560649226465, 0.06709765942637537], "LM_0", "nine_hole_peg_test", [0.0, 1.5, 0.0], 0.0465, 1.0], ["tomato_soup_can", 1.0, 430, 61, [0, 90, 0], "correct", 5, 0.0001, [98.131, 89.995, 98.131], 9, [0.03170667221189312, 1.4663900114347805, -0.007214503584986662], 1.0, 9.0, "confused", 61, "correct", 83.28537091962134, "tomato_soup_can", 7.044994115829468, "mug", 1.0, 61, 1, [0.03170667221189312, 1.4663900114347805, -0.007214503584986662], [98.13058644338928, 89.9953993482748, 98.1305379731223], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.007211928604573086, 1.466390959546065, -0.03170669979786135], "LM_0", "tomato_soup_can", [0.0, 1.5, 0.0], 0.0001, 1.0], ["baseball", 1.0, 364, 65, [0, 90, 0], "correct", 5, 0.3783, [74.94, 68.07, 106.865], 9, [-0.02272568037762012, 1.4719128756081987, -0.009157903733245454], 1.0, 9.0, "confused", 65, "correct", 68.59801246243121, "baseball", 7.107577085494995, "mug", 1.0, 65, 1, [-0.02272568037762012, 1.4719128756081987, -0.009157903733245454], [74.9403307538076, 68.0696130119172, 106.86470028315931], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.00642041293824552, 1.46510960855565, 0.010321870974386178], "LM_0", "baseball", [0.0, 1.5, 0.0], 0.3783, 1.0], ["g_cups", 1.0, 258, 38, [0, 90, 0], "correct", 5, 0.0521, [180.052, 54.746, 180.87], 5, [0.026777862902086277, 1.4719433799501007, -0.023843949673532226], 1.0, 5.0, "confused", 38, "correct", 53.33789042339184, "g_cups", 4.503062963485718, "mug", 1.0, 38, 1, [0.026777862902086277, 1.4719433799501007, -0.023843949673532226], [-179.94799298452466, 54.74576750427102, -179.1295912228423], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.03350987651992712, 1.469700394333433, -0.00812312850070309], "LM_0", "g_cups", [0.0, 1.5, 0.0], 0.0521, 1.0], ["gelatin_box", 1.0, 175, 29, [0, 90, 0], "correct", 5, 0.0107, [283.876, 89.849, 283.284], 3, [-0.03135290269248684, 1.5028950597788742, 0.021791374723798867], 1.0, 3.0, "confused", 29, "correct", 33.87173685416348, "gelatin_box", 5.584539890289307, "mug", 1.0, 29, 1, [-0.03135290269248684, 1.5028950597788742, 0.021791374723798867], [-76.12359176490212, 89.84851012045083, -76.71638249784287], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.021454163558097983, 1.49832142720898, 0.03137748831076721], "LM_0", "gelatin_box", [0.0, 1.5, 0.0], 0.0107, 1.0], ["lemon", 1.0, 156, 26, [0, 90, 0], "correct", 5, 0.0172, [335.786, 89.721, 334.839], 4, [-0.011283555697544096, 1.52486088027308, 0.0008379680181278791], 1.0, 4.0, "confused", 26, "correct", 37.42130826032734, "lemon", 3.701244831085205, "mug", 1.0, 26, 1, [-0.011283555697544096, 1.52486088027308, 0.0008379680181278791], [-24.214125671745666, 89.72064100104134, -25.16132851817325], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.0007034424188004358, 1.524863287455328, 0.011093776329271506], "LM_0", "lemon", [0.0, 1.5, 0.0], 0.0172, 1.0], ["plum", 1.0, 172, 26, [0, 90, 0], "correct", 5, 0.2997, [168.902, 60.116, 159.781], 4, [-0.016906917273226442, 1.5035428945842773, 0.021478625361472325], 1.0, 4.0, "confused", 26, "correct", 30.11095778633805, "plum", 3.4144389629364014, "mug", 1.0, 26, 1, [-0.016906917273226442, 1.5035428945842773, 0.021478625361472325], [168.90177870236576, 60.11603702639688, 159.78109624705806], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.027003892489694103, 1.4964338009117206, 0.005984817170555516], "LM_0", "plum", [0.0, 1.5, 0.0], 0.2997, 1.0], ["racquetball", 1.0, 256, 26, [0, 90, 0], "correct", 5, 0.3894, [359.312, 0.37, 332.196], 4, [-0.016246382473072855, 1.4906449260661299, -0.02024660836331016], 1.0, 3.0, "confused", 26, "correct", 33.82142540735096, "racquetball", 5.3835649490356445, "mug", 1.0, 26, 1, [-0.016246382473072855, 1.4906449260661299, -0.02024660836331016], [-0.6876367325087317, 0.37001406277157406, -27.80427895769661], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.019255158386452476, 1.4989506980538323, -0.02018331305868085], "LM_0", "racquetball", [0.0, 1.5, 0.0], 0.3894, 1.0], ["plate", 1.0, 174, 28, [0, 90, 0], "correct", 5, 0.001, [272.948, 89.945, 272.948], 4, [-0.034979704742621576, 1.5006726918582038, -0.08800824280933837], 1.0, 2.0, "confused", 28, "correct", 37.99078485084493, "plate", 3.1046509742736816, "mug", 1.0, 28, 1, [-0.034979704742621576, 1.5006726918582038, -0.08800824280933837], [-87.05165697544597, 89.94531772959714, -87.05209030356897], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.08805667446465221, 1.5005882519196028, 0.034905797885548526], "LM_0", "plate", [0.0, 1.5, 0.0], 0.001, 1.0], ["pudding_box", 1.0, 100, 21, [0, 90, 0], "correct", 0, 0.0, [279.86400000000003, 90.0, 279.86400000000003], 2, [0.037035666737873806, 1.518204544659067, 0.02371086465533501], 1.0, 1.0, "confused", 21, "correct", 26.50617304520405, "pudding_box", 4.890620946884155, "mug", 1.0, 21, 1, [0.037035666737873806, 1.518204544659067, 0.02371086465533501], [-80.13642655760596, 89.99995175612268, -80.13644266533724], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.023710874484812287, 1.5182045563374518, -0.037035671572304814], "LM_0", "pudding_box", [0.0, 1.5, 0.0], 0.0, 1.0], ["e_cups", 1.0, 214, 32, [0, 90, 0], "correct", 5, 0.1159, [181.744, 52.804, 181.012], 4, [0.03233462832711176, 1.4971542345690079, 9.17871538986632e-05], 1.0, 4.0, "confused", 32, "correct", 38.20183286657729, "e_cups", 4.002956867218018, "mug", 1.0, 32, 1, [0.03233462832711176, 1.4971542345690079, 9.17871538986632e-05], [-178.2558933677997, 52.803890939313476, -178.98759468503667], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.020112881155377977, 1.4946252595960077, -0.025796443346921095], "LM_0", "e_cups", [0.0, 1.5, 0.0], 0.1159, 1.0], ["apple", 1.0, 200, 26, [0, 90, 0], "correct", 5, 0.0, [37.219, 89.998, 37.221], 4, [-0.029058755514741304, 1.5209876904951183, 0.021762689936313237], 1.0, 4.0, "confused", 26, "correct", 38.41266356124818, "apple", 5.974435091018677, "mug", 1.0, 26, 1, [-0.029058755514741304, 1.5209876904951183, 0.021762689936313237], [37.21946190304077, 89.99812031382255, 37.22088869381516], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.021762980892758438, 1.5209882580561023, 0.02905880771898691], "LM_0", "apple", [0.0, 1.5, 0.0], 0.0, 1.0], ["j_cups", 1.0, 172, 26, [0, 90, 0], "correct", 5, 0.1252, [359.113, 80.237, 11.891], 4, [-0.01639215105680949, 1.5150999050418181, -0.03611608608897624], 1.0, 4.0, "confused", 26, "correct", 32.01149183024338, "j_cups", 3.163952112197876, "mug", 1.0, 26, 1, [-0.01639215105680949, 1.5150999050418181, -0.03611608608897624], [-0.8867823760151062, 80.23745172173626, 11.89138278521109], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.040811165741846483, 1.5044000039737555, 0.00991662459620448], "LM_0", "j_cups", [0.0, 1.5, 0.0], 0.1252, 1.0], ["foam_brick", 1.0, 170, 21, [0, 90, 0], "correct", 0, 0.0, [118.81, 90.0, 118.81], 3, [-0.002317848726172169, 1.5136952446679244, 0.004502472212593443], 1.0, 3.0, "confused", 21, "correct", 28.58952811008436, "foam_brick", 4.598902702331543, "mug", 1.0, 21, 1, [-0.002317848726172169, 1.5136952446679244, 0.004502472212593443], [118.81018252810321, 89.99998200987295, 118.81016510290108], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.004502473350178927, 1.5136952307908185, 0.0023178495625942874], "LM_0", "foam_brick", [0.0, 1.5, 0.0], 0.0, 1.0], ["large_marker", 1.0, 316, 48, [0, 90, 0], "correct", 5, 0.0575, [264.258, 87.924, 261.702], 7, [-0.0017169599898140438, 1.4950102758851083, -0.060019963003199964], 1.0, 6.0, "confused", 48, "correct", 61.60015461480594, "large_marker", 5.860679864883423, "mug", 1.0, 48, 1, [-0.0017169599898140438, 1.4950102758851083, -0.060019963003199964], [-95.74215528623975, 87.92443999950814, -98.29825857191982], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.06020190688486292, 1.4976151598241982, 0.0020872876291159266], "LM_0", "large_marker", [0.0, 1.5, 0.0], 0.0575, 1.0], ["peach", 1.0, 208, 28, [0, 90, 0], "correct", 5, 0.2074, [193.165, 78.323, 195.356], 3, [-0.02106060732645196, 1.504322878218407, 0.020143927352277102], 1.0, 3.0, "confused", 28, "correct", 37.80341055492333, "peach", 4.183923006057739, "mug", 1.0, 28, 1, [-0.02106060732645196, 1.504322878218407, 0.020143927352277102], [-166.8351036810926, 78.32270039972158, -164.64422730752463], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.02530134222360266, 1.507616376856958, 0.016332017976286112], "LM_0", "peach", [0.0, 1.5, 0.0], 0.2074, 1.0], ["phillips_screwdriver", 1.0, 84, 21, [0, 90, 0], "correct", 0, 0.002, [249.15699999999998, 89.887, 249.12900000000002], 3, [-0.07768278741872625, 1.4906261886929146, 0.04328962514651638], 1.0, 3.0, "confused", 21, "correct", 29.780716342451505, "phillips_screwdriver", 1.633193016052246, "mug", 1.0, 21, 1, [-0.07768278741872625, 1.4906261886929146, 0.04328962514651638], [-110.84341504962096, 89.8874682372087, -110.87130243963186], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.04328470873290859, 1.4905068566781077, 0.07770520262319294], "LM_0", "phillips_screwdriver", [0.0, 1.5, 0.0], 0.002, 1.0], ["a_toy_airplane", 1.0, 151, 21, [0, 90, 0], "correct", 0, 0.0, [217.274, 90.0, 217.274], 2, [-0.11442459673855364, 1.5536137345623526, -0.02653765196830157], 1.0, 2.0, "confused", 21, "correct", 32.135505887299495, "a_toy_airplane", 4.280663967132568, "mug", 1.0, 21, 1, [-0.11442459673855364, 1.5536137345623526, -0.02653765196830157], [-142.72607574801353, 89.99994234024653, -142.72611444346165], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.02653758356834403, 1.553613675357858, 0.11442458530684475], "LM_0", "a_toy_airplane", [0.0, 1.5, 0.0], 0.0, 1.0], ["e_lego_duplo", 1.0, 307, 36, [0, 90, 0], "correct", 5, 0.0, [267.019, 90.0, 267.019], 5, [-2.422547736201635e-06, 1.4897846675186102, -0.037850026017662027], 1.0, 5.0, "confused", 36, "correct", 46.87082200737551, "e_lego_duplo", 4.21878719329834, "mug", 1.0, 36, 1, [-2.422547736201635e-06, 1.4897846675186102, -0.037850026017662027], [-92.98064044938675, 89.9999659131473, -92.98062447828455], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.037850032469745554, 1.4897846097480554, 2.424598876626244e-06], "LM_0", "e_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["sugar_box", 1.0, 216, 33, [0, 90, 0], "correct", 0, 0.2055, [119.614, 89.58, 107.85], 4, [-0.0006704619309209087, 1.5911510579119312, 0.017791981698680574], 1.0, 4.0, "confused", 33, "correct", 34.28010981782906, "sugar_box", 6.293686151504517, "mug", 1.0, 33, 1, [-0.0006704619309209087, 1.5911510579119312, 0.017791981698680574], [119.61439389965396, 89.58034684893164, 107.84958450472544], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.04211505467247491, 1.5870801369251097, 0.0014076324982898625], "LM_0", "sugar_box", [0.0, 1.5, 0.0], 0.2055, 1.0], ["a_colored_wood_blocks", 1.0, 111, 21, [0, 90, 0], "correct", 0, 0.0667, [14.98, 87.121, 12.465], 3, [-0.009493835220763523, 1.5401275928850848, 0.06556743270131839], 1.0, 3.0, "confused", 21, "correct", 24.936613851718604, "a_colored_wood_blocks", 2.114790916442871, "mug", 1.0, 21, 1, [-0.009493835220763523, 1.5401275928850848, 0.06556743270131839], [14.979797085931052, 87.12085101424154, 12.465229531068879], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.06905541978972109, 1.5359186072316109, 0.01311286943008427], "LM_0", "a_colored_wood_blocks", [0.0, 1.5, 0.0], 0.0667, 1.0], ["c_cups", 1.0, 272, 33, [0, 90, 0], "correct", 5, 0.062, [36.239, 89.932, 42.695], 4, [0.023525247609907814, 1.4717652717194663, -0.009506217859684087], 1.0, 4.0, "confused", 33, "correct", 39.628418780941175, "c_cups", 5.02894401550293, "mug", 1.0, 33, 1, [0.023525247609907814, 1.4717652717194663, -0.009506217859684087], [36.2390210054229, 89.93199290835565, 42.69514030507852], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.006521503712816812, 1.4731076177618307, -0.02351510078391918], "LM_0", "c_cups", [0.0, 1.5, 0.0], 0.062, 1.0], ["pear", 1.0, 180, 22, [0, 90, 0], "correct", 0, 0.0, [143.893, 90.0, 143.893], 2, [0.013838449021336094, 1.4858427785588004, -0.04591438198367564], 1.0, 2.0, "confused", 22, "correct", 29.482773770484002, "pear", 3.007659912109375, "mug", 1.0, 22, 1, [0.013838449021336094, 1.4858427785588004, -0.04591438198367564], [143.89291598478954, 89.9995373358151, 143.89301070008028], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.045914673202314356, 1.4858431208009606, -0.01383858370946994], "LM_0", "pear", [0.0, 1.5, 0.0], 0.0, 1.0], ["f_cups", 1.0, 262, 38, [0, 90, 0], "correct", 5, 0.1335, [359.447, 78.144, 10.953], 6, [-0.024778746983480374, 1.4875778341115986, -0.010435576027870892], 1.0, 4.0, "confused", 38, "correct", 44.14861544111882, "f_cups", 4.5263941287994385, "mug", 1.0, 38, 1, [-0.024778746983480374, 1.4875778341115986, -0.010435576027870892], [-0.5527562476623974, 78.1441966711375, 10.953085879517952], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.010304296752207134, 1.4843232165571252, 0.022147804261195603], "LM_0", "f_cups", [0.0, 1.5, 0.0], 0.1335, 1.0], ["wood_block", 1.0, 304, 41, [0, 90, 0], "correct", 5, 0.0, [0.0, 90.0, 0.0], 5, [0.035533762190171306, 1.5301036907211094, -0.03585884059400217], 1.0, 4.0, "confused", 41, "correct", 57.154507444501675, "wood_block", 6.584734916687012, "mug", 1.0, 41, 1, [0.035533762190171306, 1.5301036907211094, -0.03585884059400217], [-1.5001981519982156e-08, 89.99999879258172, 0.0], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.03585884032608457, 1.5301036556402956, -0.03553376347526073], "LM_0", "wood_block", [0.0, 1.5, 0.0], 0.0, 1.0], ["d_lego_duplo", 1.0, 808, 96, [0, 90, 0], "correct", 5, 0.0405, [272.553, 85.286, 268.214], 14, [-0.00313951596994779, 1.5212870868142012, 0.0025190238973867678], 1.0, 6.0, "confused", 96, "correct", 105.51514308906681, "d_lego_duplo", 17.72261691093445, "mug", 1.0, 96, 1, [-0.00313951596994779, 1.5212870868142012, 0.0025190238973867678], [-87.44705468077022, 85.28592725736497, -91.78602948579729], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.004554081326349836, 1.5174755583608721, 0.0010666224639512625], "LM_0", "d_lego_duplo", [0.0, 1.5, 0.0], 0.0405, 1.0], ["b_toy_airplane", 1.0, 120, 21, [0, 90, 0], "correct", 0, 0.0496, [130.276, 87.921, 128.337], 3, [0.027731841354065097, 1.522700241554688, -0.031418189844817535], 1.0, 3.0, "confused", 21, "correct", 24.445649759674055, "b_toy_airplane", 2.1537070274353027, "mug", 1.0, 21, 1, [0.027731841354065097, 1.522700241554688, -0.031418189844817535], [130.2763692440029, 87.92113756414841, 128.33651633933468], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.02707974485227929, 1.5238197236482718, -0.029330885542434744], "LM_0", "b_toy_airplane", [0.0, 1.5, 0.0], 0.0496, 1.0], ["b_colored_wood_blocks", 1.0, 287, 30, [0, 90, 0], "correct", 5, 0.154, [355.582, 359.443, 5.805], 4, [0.008404043344974802, 1.5110968519846495, 0.005262277440313505], 1.0, 3.0, "confused", 30, "correct", 35.73001340348236, "b_colored_wood_blocks", 5.762546062469482, "mug", 1.0, 30, 1, [0.008404043344974802, 1.5110968519846495, 0.005262277440313505], [-4.418130712702001, -0.5567653759367268, 5.805168485900664], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.00716367709499964, 1.512683559380503, 0.004492104405346066], "LM_0", "b_colored_wood_blocks", [0.0, 1.5, 0.0], 0.154, 1.0], ["g_lego_duplo", 1.0, 110, 21, [0, 90, 0], "correct", 0, 0.0, [0.0, 90.0, 0.0], 3, [0.021933966571430243, 1.484844828931825, -0.1440069911066829], 1.0, 3.0, "confused", 21, "correct", 28.94215131933344, "g_lego_duplo", 1.7551608085632324, "mug", 1.0, 21, 1, [0.021933966571430243, 1.484844828931825, -0.1440069911066829], [-5.445804062940969e-07, 89.9999982924527, 0.0], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.14400698895087255, 1.4848448481142078, -0.021933967438238704], "LM_0", "g_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["a_lego_duplo", 1.0, 170, 21, [0, 90, 0], "correct", 0, 0.0256, [231.782, 88.765, 232.574], 2, [0.010697250358336782, 1.507871298838618, 0.013374271656021279], 1.0, 1.0, "confused", 21, "correct", 22.763002582964567, "a_lego_duplo", 3.1900908946990967, "mug", 1.0, 21, 1, [0.010697250358336782, 1.507871298838618, 0.013374271656021279], [-128.21750269635984, 88.76496738227264, -127.42596473371052], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.0141620927014629, 1.5075789547717482, -0.011144551906976042], "LM_0", "a_lego_duplo", [0.0, 1.5, 0.0], 0.0256, 1.0], ["mini_soccer_ball", 1.0, 87, 21, [0, 90, 0], "correct", 0, 0.0, [126.692, 89.999, 126.692], 3, [-0.0024892570981715676, 1.4920686972505923, -0.06336897405140585], 1.0, 2.0, "confused", 21, "correct", 32.01432393674924, "mini_soccer_ball", 2.4283127784729004, "mug", 1.0, 21, 1, [-0.0024892570981715676, 1.4920686972505923, -0.06336897405140585], [126.69214676943378, 89.99889737158135, 126.69216862954755], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.06336828256899608, 1.492067612440332, 0.0024893582278541202], "LM_0", "mini_soccer_ball", [0.0, 1.5, 0.0], 0.0, 1.0], ["medium_clamp", 1.0, 105, 21, [0, 90, 0], "correct", 0, 0.0237, [166.781, 88.645, 166.818], 3, [0.030034389176464414, 1.5068813544966517, -0.030605254995633997], 1.0, 3.0, "confused", 21, "correct", 33.71480225869402, "medium_clamp", 2.1016037464141846, "mug", 1.0, 21, 1, [0.030034389176464414, 1.5068813544966517, -0.030605254995633997], [166.7812559835299, 88.6453384532687, 166.8177266527836], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.031737001004268364, 1.506933836090301, -0.03037555445302149], "LM_0", "medium_clamp", [0.0, 1.5, 0.0], 0.0237, 1.0], ["a_marbles", 1.0, 204, 45, [0, 90, 0], "correct", 0, 0.2474, [151.831, 81.109, 140.781], 6, [-0.03492374570669085, 1.483335259083181, 0.008532892469846051], 1.0, 5.0, "confused", 45, "correct", 20.01645404528171, "a_marbles", 4.509300947189331, "mug", 1.0, 45, 1, [-0.03492374570669085, 1.483335259083181, 0.008532892469846051], [151.83067172595693, 81.10876714930806, 140.78147841809704], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.01323750792752702, 1.4867568111943053, 0.031123423501945583], "LM_0", "a_marbles", [0.0, 1.5, 0.0], 0.2474, 1.0], ["extra_large_clamp", 1.0, 103, 21, [0, 90, 0], "correct", 0, 0.0, [153.597, 90.0, 153.597], 3, [0.06826229126323091, 1.5136215710930951, -0.06455704520036142], 1.0, 3.0, "confused", 21, "correct", 34.69438202436867, "extra_large_clamp", 2.6254870891571045, "mug", 1.0, 21, 1, [0.06826229126323091, 1.5136215710930951, -0.06455704520036142], [153.5970990042273, 89.99972524004075, 153.59721682986458], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.06455753570676949, 1.513621929822564, -0.06826282110409466], "LM_0", "extra_large_clamp", [0.0, 1.5, 0.0], 0.0, 1.0], ["d_cups", 1.0, 361, 32, [0, 90, 0], "correct", 5, 0.216, [0.447, 332.934, 358.285], 5, [-0.012891728567782368, 1.523365135954408, -0.027897468368357176], 1.0, 1.0, "confused", 32, "correct", 39.385275883070605, "d_cups", 4.269343852996826, "mug", 1.0, 32, 1, [-0.012891728567782368, 1.523365135954408, -0.027897468368357176], [0.4472802590578916, -27.065811828921703, -1.714844713503066], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.001499213274548519, 1.5201290061172963, -0.03025133874771173], "LM_0", "d_cups", [0.0, 1.5, 0.0], 0.216, 1.0], ["e_toy_airplane", 1.0, 3095, 500, [0, 90, 0], "correct_mlh", 0, null, [null, null, null], 31, [null, null, null], 1.0, 27.0, "confused_mlh", null, "time_out", 719.7007397807029, "e_toy_airplane^d_toy_airplane", 37.994304895401, "mug", null, 500, 2, [0.015162454238417757, 1.4787439168804355, 0.005323831074180718], [-3.626094180680307, 79.03943621556998, -4.499816956727931], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.006969710841078586, 1.479636885783135, -0.01355182521662741], "LM_0", "e_toy_airplane", [0.0, 1.5, 0.0], 0.1919, 1.0], ["adjustable_wrench", 1.0, 87, 21, [0, 90, 0], "correct", 0, 0.0166, [93.064, 89.213, 92.525], 3, [0.007760959766400077, 1.5038565775021144, 0.07379437371662939], 1.0, 0.0, "confused", 21, "correct", 37.58449602558094, "adjustable_wrench", 2.8731210231781006, "mug", 1.0, 21, 1, [0.007760959766400077, 1.5038565775021144, 0.07379437371662939], [93.06369581627956, 89.21310549639013, 92.5252630456505], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.07321228218991511, 1.505066389711381, -0.008278512738851752], "LM_0", "adjustable_wrench", [0.0, 1.5, 0.0], 0.0166, 1.0], ["rubiks_cube", 1.0, 144, 26, [0, 90, 0], "correct", 5, 0.0, [0.0, 90.0, 0.0], 3, [-0.014887628227662122, 1.5108281548980356, 0.03271546109019619], 1.0, 3.0, "confused", 26, "correct", 36.698258241912754, "rubiks_cube", 5.634631872177124, "mug", 1.0, 26, 1, [-0.014887628227662122, 1.5108281548980356, 0.03271546109019619], [-2.7544397000376325e-07, 89.99999879258172, 0.0], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.032715460700956135, 1.5108282053735687, 0.01488763022376267], "LM_0", "rubiks_cube", [0.0, 1.5, 0.0], 0.0, 1.0], ["f_lego_duplo", 1.0, 110, 21, [0, 90, 0], "correct", 0, 0.0858, [85.82, 89.257, 90.678], 3, [0.01604360686410737, 1.5047874853540728, -0.0005501091461034367], 1.0, 3.0, "confused", 21, "correct", 26.301472817033797, "f_lego_duplo", 4.002974987030029, "mug", 1.0, 21, 1, [0.01604360686410737, 1.5047874853540728, -0.0005501091461034367], [85.81975938068403, 89.2567656483935, 90.67811784747484], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.0012907308339345867, 1.5065763588620786, -0.015922894002785327], "LM_0", "f_lego_duplo", [0.0, 1.5, 0.0], 0.0858, 1.0], ["a_cups", 1.0, 227, 33, [0, 90, 0], "correct", 5, 0.0017, [199.113, 89.951, 199.032], 5, [0.016214196162220855, 1.4920055997905464, 0.013688372368161634], 1.0, 4.0, "confused", 33, "correct", 49.632576943163365, "a_cups", 4.0045671463012695, "mug", 1.0, 33, 1, [0.016214196162220855, 1.4920055997905464, 0.013688372368161634], [-160.88726067154022, 89.95067499734984, -160.96844819548235], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.01367252103600981, 1.4919017962820245, -0.016219812784650945], "LM_0", "a_cups", [0.0, 1.5, 0.0], 0.0017, 1.0], ["skillet_lid", 1.0, 104, 21, [0, 90, 0], "correct", 0, 0.0752, [57.505, 86.04, 59.203], 3, [-0.009917031567053269, 1.5413747515648788, 0.009596108323382802], 1.0, 0.0, "confused", 21, "correct", 27.20374947259719, "skillet_lid", 4.363016128540039, "mug", 1.0, 21, 1, [-0.009917031567053269, 1.5413747515648788, 0.009596108323382802], [57.50466124225521, 86.04039151240154, 59.20285026717086], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.006821875885578111, 1.5377672990202471, 0.011374546375136754], "LM_0", "skillet_lid", [0.0, 1.5, 0.0], 0.0752, 1.0], ["sponge", 1.0, 191, 21, [0, 90, 0], "correct", 0, 0.0, [91.003, 89.998, 91.003], 3, [-0.036092658566536144, 1.4966309716946284, -0.05296945485297272], 1.0, 3.0, "confused", 21, "correct", 29.818737583815995, "sponge", 3.08499813079834, "mug", 1.0, 21, 1, [-0.036092658566536144, 1.4966309716946284, -0.05296945485297272], [91.00340607107744, 89.99828393812071, 91.0034462510096], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.05296952871013453, 1.496629329368917, 0.036092726050172774], "LM_0", "sponge", [0.0, 1.5, 0.0], 0.0, 1.0], ["tennis_ball", 1.0, 436, 60, [0, 90, 0], "correct", 5, 0.1481, [224.771, 89.067, 233.203], 8, [-0.027494003747013437, 1.4858845807802874, -0.011809325068931288], 1.0, 8.0, "confused", 60, "correct", 72.84404733189521, "tennis_ball", 10.069050788879395, "mug", 1.0, 60, 1, [-0.027494003747013437, 1.4858845807802874, -0.011809325068931288], [-135.2287059396263, 89.0665021192713, -126.7965191774788], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.00964521972049072, 1.4836845203742874, 0.027802620737753737], "LM_0", "tennis_ball", [0.0, 1.5, 0.0], 0.1481, 1.0], ["spatula", 1.0, 232, 35, [0, 90, 0], "correct", 0, 0.0317, [354.958, 88.267, 355.509], 4, [-0.12883584229009495, 1.4914406069375352, -0.05179315821142573], 1.0, 1.0, "confused", 35, "correct", 41.84864162702787, "spatula", 7.0090789794921875, "mug", 1.0, 35, 1, [-0.12883584229009495, 1.4914406069375352, -0.05179315821142573], [-5.042023496118196, 88.26711331455016, -4.491077196865152], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.05576252867151507, 1.4915149184728307, 0.12796998579639235], "LM_0", "spatula", [0.0, 1.5, 0.0], 0.0317, 1.0], ["d_toy_airplane", 1.0, 3554, 500, [0, 90, 0], "correct_mlh", 0, null, [null, null, null], 16, [null, null, null], 1.0, 14.0, "confused_mlh", null, "time_out", 738.1563260658058, "c_toy_airplane^e_toy_airplane^d_toy_airplane", 43.84693908691406, "mug", null, 500, 3, [0.011543035982512266, 1.471996782138965, 0.011197021888450457], [-1.2223886144158636, 4.196605289215754, -1.8637507927230006], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.01086498642842364, 1.4717042196902284, 0.010803970782116815], "LM_0", "d_toy_airplane", [0.0, 1.5, 0.0], 1.4976, 1.0], ["chain", 1.0, 98, 21, [0, 90, 0], "correct", 0, 0.0, [268.294, 89.998, 268.293], 3, [0.14396315456099731, 1.5067896756186636, 0.061766459548871604], 1.0, 2.0, "confused", 21, "correct", 32.1854345775391, "chain", 2.0056939125061035, "mug", 1.0, 21, 1, [0.14396315456099731, 1.5067896756186636, 0.061766459548871604], [-91.70645810357944, 89.99845384680609, -91.70710541595369], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.06173858108468826, 1.5067884067476378, -0.1439862088553696], "LM_0", "chain", [0.0, 1.5, 0.0], 0.0, 1.0], ["scissors", 1.0, 251, 35, [0, 90, 0], "correct", 0, 0.1793, [285.21, 84.784, 294.06399999999996], 5, [0.011422639854602254, 1.4955928651899837, 0.06871758915151482], 1.0, 1.0, "confused", 35, "correct", 20.148582255187975, "scissors", 3.7539849281311035, "mug", 1.0, 35, 1, [0.011422639854602254, 1.4955928651899837, 0.06871758915151482], [-74.7895328492246, 84.78425826938759, -65.93590514198945], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.06937386901075263, 1.5051959415068314, -0.008874761555173732], "LM_0", "scissors", [0.0, 1.5, 0.0], 0.1793, 1.0], ["mustard_bottle", 1.0, 156, 26, [0, 90, 0], "correct", 5, 0.0, [141.215, 90.0, 141.215], 4, [-0.03850939557130777, 1.476567314908708, -0.010973463644396742], 1.0, 4.0, "confused", 26, "correct", 40.68440023157507, "mustard_bottle", 3.0568180084228516, "mug", 1.0, 26, 1, [-0.03850939557130777, 1.476567314908708, -0.010973463644396742], [141.21549120535235, 89.99979875919458, 141.21540833360623], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.010973502609551492, 1.4765671806297802, 0.038509419812119486], "LM_0", "mustard_bottle", [0.0, 1.5, 0.0], 0.0, 1.0], ["bleach_cleanser", 1.0, 196, 21, [0, 90, 0], "correct", 0, 0.0677, [156.833, 87.951, 153.54], 3, [-0.004243181025165764, 1.6232255923641274, -0.007247805584439278], 1.0, 3.0, "confused", 21, "correct", 32.021366936138214, "bleach_cleanser", 2.824070692062378, "mug", 1.0, 21, 1, [-0.004243181025165764, 1.6232255923641274, -0.007247805584439278], [156.83280243961156, 87.95118502901356, 153.5403149180818], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.0011027358996296658, 1.6203020607130674, 0.006810415063010042], "LM_0", "bleach_cleanser", [0.0, 1.5, 0.0], 0.0677, 1.0], ["tuna_fish_can", 1.0, 100, 21, [0, 90, 0], "correct", 0, 0.0, [88.254, 90.0, 88.254], 3, [0.03529584630108493, 1.5041613348349883, -0.022548433392989033], 1.0, 2.0, "confused", 21, "correct", 32.40495208165898, "tuna_fish_can", 2.4733729362487793, "mug", 1.0, 21, 1, [0.03529584630108493, 1.5041613348349883, -0.022548433392989033], [88.2536789146146, 89.99972486887589, 88.25368032872346], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.02254841592912414, 1.5041612424333768, -0.03529584345473092], "LM_0", "tuna_fish_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["cracker_box", 1.0, 178, 24, [0, 90, 0], "correct", 0, 0.024, [213.627, 89.977, 215.002], 3, [0.005680183808876679, 1.584510713670849, -0.07627883967268993], 1.0, 3.0, "confused", 24, "correct", 28.393136409618425, "cracker_box", 4.878755807876587, "mug", 1.0, 24, 1, [0.005680183808876679, 1.584510713670849, -0.07627883967268993], [-146.37310398781602, 89.9769233255999, -144.99839984619706], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.07679655357624061, 1.5863528903674788, -0.005710242067892701], "LM_0", "cracker_box", [0.0, 1.5, 0.0], 0.024, 1.0], ["fork", 1.0, 2201, 369, [0, 90, 0], "correct", 0, 0.1759, [188.168, 85.745, 179.032], 10, [-0.040940871207018475, 1.4990707963784324, -0.009703496418003674], 1.0, 3.0, "confused", 369, "correct", 480.7852282527783, "fork", 32.99751806259155, "mug", 1.0, 369, 1, [-0.040940871207018475, 1.4990707963784324, -0.009703496418003674], [-171.83166640173582, 85.7453926468439, 179.03236892315007], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [-0.0014155763973731994, 1.5007916276694775, 0.040519949909007584], "LM_0", "fork", [0.0, 1.5, 0.0], 0.1759, 1.0], ["large_clamp", 1.0, 98, 21, [0, 90, 0], "correct", 0, 0.0023, [285.406, 89.87, 285.423], 3, [0.021522171990087914, 1.485545128925043, 0.08375392425766752], 1.0, 3.0, "confused", 21, "correct", 30.966792887449653, "large_clamp", 2.470216989517212, "mug", 1.0, 21, 1, [0.021522171990087914, 1.485545128925043, 0.08375392425766752], [-74.59438522050543, 89.87028518225516, -74.57710819775328], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.08384055057561429, 1.4853578143132318, -0.02154258916188527], "LM_0", "large_clamp", [0.0, 1.5, 0.0], 0.0023, 1.0], ["dice", 1.0, 389, 48, [0, 90, 0], "correct", 5, 0.0543, [12.698, 88.159, 9.963], 7, [0.00825273410914063, 1.5060413214393225, 5.751844475545718e-05], 1.0, 3.0, "confused", 48, "correct", 59.0639699454175, "dice", 6.316447019577026, "mug", 1.0, 48, 1, [0.00825273410914063, 1.5060413214393225, 5.751844475545718e-05], [12.69761681003972, 88.15946653873908, 9.963218192382453], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.0017624680771757431, 1.5060396399403022, -0.008140121325612544], "LM_0", "dice", [0.0, 1.5, 0.0], 0.0543, 1.0], ["flat_screwdriver", 1.0, 2646, 500, [0, 90, 0], "correct_mlh", 0, null, [null, null, null], 7, [null, null, null], 1.0, 0.0, "confused_mlh", null, "time_out", 642.6991158859978, "phillips_screwdriver^flat_screwdriver", 37.34083390235901, "mug", null, 500, 2, [0.012024198073278893, 1.5095580371918238, 0.011543911608769846], [116.45995530922302, 82.18583785108073, 111.62544474009921], "target_in_possible_matches_(TP)", [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], [0.011768554242617756, 1.5089689557418122, -0.010823262145742644], "LM_0", "flat_screwdriver", [0.0, 1.5, 0.0], 0.1603, 1.0], ["mug", 1.0, 139, 21, [0, 180, 0], "correct", 0, 0.0, [180.0, 0.0, 180.0], 2, [-0.043895788748715546, 1.5024074750195011, -0.023931081052364606], 1.0, 2.0, "correct", 21, "correct", 27.51192102592338, "mug", 3.094468832015991, "mug", 1.0, 21, 1, [-0.043895788748715546, 1.5024074750195011, -0.023931081052364606], [179.99999982597504, -3.606182106713204e-07, 179.9999997244867], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.04389489099397802, 1.502407553142626, 0.023930800425848137], "LM_0", "mug", [0.0, 1.5, 0.0], 0.0, 1.0], ["bowl", 1.0, 273, 41, [0, 180, 0], "correct", 5, 0.0292, [356.738, 359.072, 356.706], 5, [0.0022586788059242584, 1.5125110159933262, -0.07123637011458707], 1.0, 3.0, "confused", 41, "correct", 45.67697616317765, "bowl", 15.590857028961182, "mug", 1.0, 41, 1, [0.0022586788059242584, 1.5125110159933262, -0.07123637011458707], [-3.2618292076765605, -0.9280994545432044, -3.294096001022798], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.005502085320083611, 1.5080366620008203, -0.06948384021253748], "LM_0", "bowl", [0.0, 1.5, 0.0], 0.0292, 1.0], ["potted_meat_can", 1.0, 226, 35, [0, 180, 0], "correct", 5, 0.0446, [179.532, 358.888, 185.81], 4, [0.037954763870764324, 1.4987514371541415, 0.017146687491992833], 1.0, 2.0, "confused", 35, "correct", 42.20722212267542, "potted_meat_can", 8.568625211715698, "mug", 1.0, 35, 1, [0.037954763870764324, 1.4987514371541415, 0.017146687491992833], [179.53221394834773, -1.1116670187575715, -174.18963867232756], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.045338143211169926, 1.489590426995171, -0.01596542069071069], "LM_0", "potted_meat_can", [0.0, 1.5, 0.0], 0.0446, 1.0], ["master_chef_can", 1.0, 109, 21, [0, 180, 0], "correct", 0, 0.0, [180.0, 0.0, 180.0], 3, [0.04914597425747266, 1.4989277702412551, -0.006388285056039945], 1.0, 2.0, "confused", 21, "correct", 28.872969106954574, "master_chef_can", 2.194608211517334, "mug", 1.0, 21, 1, [0.04914597425747266, 1.4989277702412551, -0.006388285056039945], [-179.99999984915928, -2.4519901094681593e-08, 179.99999999762585], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.04914707515010707, 1.4989166957170572, 0.006387178982389477], "LM_0", "master_chef_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["i_cups", 1.0, 237, 28, [0, 180, 0], "correct", 5, 0.0, [180.0, 0.0, 180.0], 3, [0.026174560306501893, 1.5252237965479418, -0.03068981997918721], 1.0, 2.0, "confused", 28, "correct", 39.30647919927084, "i_cups", 5.192557096481323, "mug", 1.0, 28, 1, [0.026174560306501893, 1.5252237965479418, -0.03068981997918721], [179.9999987261266, 5.620702695297277e-07, -179.99993903755185], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.02617460963684769, 1.5252237712283585, 0.030689821740251918], "LM_0", "i_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["spoon", 1.0, 1072, 166, [0, 180, 0], "correct", 0, 0.0181, [179.369, 359.366, 180.526], 14, [-0.05050402161289536, 1.5041772494134018, 0.001974013960312079], 1.0, 5.0, "confused", 166, "correct", 214.06714420209713, "spoon", 17.667530059814453, "mug", 1.0, 166, 1, [-0.05050402161289536, 1.5041772494134018, 0.001974013960312079], [179.36901856921477, -0.6337180583025374, -179.47362901725415], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.055705908568873024, 1.505271399951412, -0.0011209945719794874], "LM_0", "spoon", [0.0, 1.5, 0.0], 0.0181, 1.0], ["b_cups", 1.0, 231, 26, [0, 180, 0], "correct", 5, 0.0306, [180.204, 320.154, 179.892], 3, [0.006544740729626038, 1.4946860416475458, -0.01951612864937247], 1.0, 3.0, "confused", 26, "correct", 33.58980857541897, "b_cups", 3.62218976020813, "mug", 1.0, 26, 1, [0.006544740729626038, 1.4946860416475458, -0.01951612864937247], [-179.79625788683134, -39.84626865462304, 179.89230453004842], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.00737572062497501, 1.4950108643328768, 0.019218061249995973], "LM_0", "b_cups", [0.0, 1.5, 0.0], 0.0306, 1.0], ["pitcher_base", 1.0, 243, 26, [0, 180, 0], "correct", 5, 0.0262, [0.407, 354.556, 1.759], 3, [0.007961109494678847, 1.495242938396999, 0.06387835669659156], 1.0, 1.0, "confused", 26, "correct", 35.741281796850785, "pitcher_base", 3.360049247741699, "mug", 1.0, 26, 1, [0.007961109494678847, 1.495242938396999, 0.06387835669659156], [0.4069102551132972, -5.444210270050311, 1.7594266841507136], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.020348741801847356, 1.500842121746481, 0.05102204838035598], "LM_0", "pitcher_base", [0.0, 1.5, 0.0], 0.0262, 1.0], ["knife", 1.0, 322, 39, [0, 180, 0], "confused", 0, null, [30.078, 16.957, 191.855], 4, [0.025838453741124028, 1.4927128923343513, 0.008420303461390631], 1.0, 0.0, "confused", 39, "confused", 37.23513811045279, "spoon", 5.104066848754883, "mug", 1.0, 39, 1, [0.025838453741124028, 1.4927128923343513, 0.008420303461390631], [30.078145954838334, 16.95721385505289, -168.14522588028117], "target_not_matched_(FN)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.036039792315541594, 1.5083198783048486, -0.004985973565112954], "LM_0", "spoon", [0.0, 1.5, 0.0], null, 1.0], ["b_marbles", 1.0, 1472, 226, [0, 180, 0], "correct", 5, 0.2559, [158.813, 332.76800000000003, 228.208], 34, [0.009846649373853997, 1.4926385903678236, 0.011286195079837219], 1.0, 28.0, "confused", 226, "correct", 262.1497389494333, "b_marbles", 25.907155990600586, "mug", 1.0, 226, 1, [0.009846649373853997, 1.4926385903678236, 0.011286195079837219], [158.8128017080732, -27.232224800412666, -131.7920339778793], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.008806375723588307, 1.4867892464098325, -0.00708648929503162], "LM_0", "b_marbles", [0.0, 1.5, 0.0], 0.2559, 1.0], ["h_cups", 1.0, 180, 26, [0, 180, 0], "correct", 5, 0.0989, [179.856, 355.455, 176.629], 3, [-0.030086103660367278, 1.4979985938087932, -0.020193730929089215], 1.0, 3.0, "confused", 26, "correct", 37.77924411868858, "h_cups", 3.391503095626831, "mug", 1.0, 26, 1, [-0.030086103660367278, 1.4979985938087932, -0.020193730929089215], [179.85606154267413, -4.545448935950146, 176.62874212678273], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.03292595622063656, 1.497117313202917, 0.01784469221034693], "LM_0", "h_cups", [0.0, 1.5, 0.0], 0.0989, 1.0], ["strawberry", 1.0, 104, 21, [0, 180, 0], "correct", 0, 0.0, [180.0, 0.0, 180.0], 2, [-0.017191070173533777, 1.5010360789886317, 0.019464469463175416], 1.0, 2.0, "confused", 21, "correct", 28.796567173823036, "strawberry", 3.11696195602417, "mug", 1.0, 21, 1, [-0.017191070173533777, 1.5010360789886317, 0.019464469463175416], [179.9999994076725, -3.742161760453873e-08, -179.99999988033142], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.017302539821253744, 1.5009865953006267, -0.01949991326447236], "LM_0", "strawberry", [0.0, 1.5, 0.0], 0.0, 1.0], ["power_drill", 1.0, 123, 21, [0, 180, 0], "correct", 0, 0.0, [180.0, 0.0, 180.0], 3, [-0.07629002080651769, 1.5187558872119176, -0.0701606646178477], 1.0, 3.0, "confused", 21, "correct", 29.187948851709283, "power_drill", 4.94717812538147, "mug", 1.0, 21, 1, [-0.07629002080651769, 1.5187558872119176, -0.0701606646178477], [179.99999998477608, -2.0201076781597188e-08, 179.99999995688808], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.07629001981019767, 1.5187558693291932, 0.07016066442065783], "LM_0", "power_drill", [0.0, 1.5, 0.0], 0.0, 1.0], ["padlock", 1.0, 5000, 7, [0, 180, 0], "correct_mlh", 0, null, [null, null, null], 0, [null, null, null], 1.0, 0.0, "confused_mlh", null, "time_out", 8.438553658452937, "padlock", 1.0627129077911377, "mug", null, 7, 1, [-0.008737452298498469, 1.5106452033713313, -0.031043358948899428], [179.41339659346593, -3.80987516830248, 176.73319983049433], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.007473649923241718, 1.510964243666527, 0.029895402158394646], "LM_0", "padlock", [0.0, 1.5, 0.0], 0.0884, 1.0], ["golf_ball", 1.0, 236, 26, [0, 180, 0], "correct", 5, 0.122, [3.761, 352.809, 350.112], 3, [0.021893234492268886, 1.4936750742859715, -0.00491379488858618], 1.0, 3.0, "confused", 26, "correct", 31.906339376660643, "golf_ball", 4.106780052185059, "mug", 1.0, 26, 1, [0.021893234492268886, 1.4936750742859715, -0.00491379488858618], [3.761202306788909, -7.190915158824156, -9.8875553530008], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.01878520881075006, 1.490276844007814, -0.0023141099068023164], "LM_0", "golf_ball", [0.0, 1.5, 0.0], 0.122, 1.0], ["hammer", 1.0, 126, 21, [0, 180, 0], "correct", 0, 0.0544, [179.079, 0.14, 182.976], 3, [-0.015817112858185354, 1.5067068809906785, -0.10468848304900949], 1.0, 0.0, "confused", 21, "correct", 29.387298553247167, "hammer", 3.6314542293548584, "mug", 1.0, 21, 1, [-0.015817112858185354, 1.5067068809906785, -0.10468848304900949], [179.07853279230227, 0.13966171998727703, -177.02414651015926], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.015482064682733363, 1.506210327278146, 0.10498141971859586], "LM_0", "hammer", [0.0, 1.5, 0.0], 0.0544, 1.0], ["softball", 1.0, 132, 27, [0, 180, 0], "correct", 5, 0.2044, [159.928, 10.14, 190.287], 3, [-0.012419763773571426, 1.4567285512876837, -0.02155103594039326], 1.0, 2.0, "confused", 27, "correct", 31.308400603258953, "softball", 3.2835609912872314, "mug", 1.0, 27, 1, [-0.012419763773571426, 1.4567285512876837, -0.02155103594039326], [159.92783629948704, 10.139558931999517, -169.7132056313353], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.019691881972687293, 1.457099223652869, 0.00728930010103023], "LM_0", "softball", [0.0, 1.5, 0.0], 0.2044, 1.0], ["orange", 1.0, 184, 26, [0, 180, 0], "correct", 5, 0.2713, [94.322, 284.98, 265.218], 3, [-0.00237577475950591, 1.529930658337953, -0.016245835186012244], 1.0, 3.0, "confused", 26, "correct", 31.65223258263363, "orange", 3.7170979976654053, "mug", 1.0, 26, 1, [-0.00237577475950591, 1.529930658337953, -0.016245835186012244], [94.3221909414408, -75.02010131462472, -94.78155065692009], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.011583866774284184, 1.5324337668335224, 0.007895884820502498], "LM_0", "orange", [0.0, 1.5, 0.0], 0.2713, 1.0], ["c_lego_duplo", 1.0, 397, 35, [0, 180, 0], "correct", 5, 0.0056, [180.172, 359.787, 180.168], 5, [0.01159016090265667, 1.494307659960788, 0.03166831180150463], 1.0, 5.0, "confused", 35, "correct", 37.20181120340312, "c_lego_duplo", 9.421653032302856, "mug", 1.0, 35, 1, [0.01159016090265667, 1.494307659960788, 0.03166831180150463], [-179.82753261970362, -0.21258294125714933, -179.83176288696095], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.011539079522702835, 1.4913194884409233, -0.0316190354103371], "LM_0", "c_lego_duplo", [0.0, 1.5, 0.0], 0.0056, 1.0], ["c_toy_airplane", 1.0, 168, 28, [0, 180, 0], "correct", 5, 0.3171, [182.45, 66.468, 181.286], 3, [-0.00950282991542734, 1.4678151525925416, -0.0035673815281713544], 1.0, 3.0, "confused", 28, "correct", 38.297553515876174, "c_toy_airplane", 3.1192829608917236, "mug", 1.0, 28, 1, [-0.00950282991542734, 1.4678151525925416, -0.0035673815281713544], [-177.54971077039986, 66.46794861814314, -178.71351279238377], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.00020995167573774516, 1.4669106815625124, 0.010524539708196744], "LM_0", "c_toy_airplane", [0.0, 1.5, 0.0], 0.3171, 1.0], ["b_lego_duplo", 1.0, 179, 26, [0, 180, 0], "correct", 5, 0.1655, [181.7, 359.994, 170.669], 3, [0.0015260699574721963, 1.5188297055804127, 0.015642338146990252], 1.0, 2.0, "confused", 26, "correct", 29.29677262995447, "b_lego_duplo", 8.205953121185303, "mug", 1.0, 26, 1, [0.0015260699574721963, 1.5188297055804127, 0.015642338146990252], [-178.29986217204555, -0.006437812180865887, 170.66905337513424], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.002869530331089165, 1.51031996774009, -0.01646854214518964], "LM_0", "b_lego_duplo", [0.0, 1.5, 0.0], 0.1655, 1.0], ["banana", 1.0, 198, 21, [0, 180, 0], "correct", 0, 0.0968, [176.59, 355.909, 181.666], 3, [0.04849513689827599, 1.4868795228593878, -0.08555003408050203], 1.0, 3.0, "confused", 21, "correct", 22.216688935458965, "banana", 5.268736839294434, "mug", 1.0, 21, 1, [0.04849513689827599, 1.4868795228593878, -0.08555003408050203], [176.58969493237197, -4.091323456311906, -178.33418756774168], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.04413680725892388, 1.4846959799753998, 0.08643783875135587], "LM_0", "banana", [0.0, 1.5, 0.0], 0.0968, 1.0], ["nine_hole_peg_test", 1.0, 271, 26, [0, 180, 0], "correct", 5, 0.0476, [181.562, 52.337, 2.132], 3, [-0.0821318696695806, 1.5099590992187581, -0.04109714414193519], 1.0, 2.0, "confused", 26, "correct", 32.9565225498792, "nine_hole_peg_test", 4.802043199539185, "mug", 1.0, 26, 1, [-0.0821318696695806, 1.5099590992187581, -0.04109714414193519], [-178.4380192871901, 52.337031033446095, 2.1322401437057406], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.026562946381259254, 1.4856722642422497, 0.09436510152761236], "LM_0", "nine_hole_peg_test", [0.0, 1.5, 0.0], 0.0476, 1.0], ["tomato_soup_can", 1.0, 168, 28, [0, 180, 0], "correct", 5, 0.0209, [179.723, 359.317, 180.946], 4, [0.010205450455516334, 1.4489695505862366, 0.03008600224748249], 1.0, 0.0, "confused", 28, "correct", 38.21010360577435, "tomato_soup_can", 3.9964840412139893, "mug", 1.0, 28, 1, [0.010205450455516334, 1.4489695505862366, 0.03008600224748249], [179.72334066886964, -0.6828890320233584, -179.05430176119071], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.011609704963638464, 1.4515427283358968, -0.030098443904751693], "LM_0", "tomato_soup_can", [0.0, 1.5, 0.0], 0.0209, 1.0], ["baseball", 1.0, 304, 42, [0, 180, 0], "correct", 5, 0.2239, [170.711, 7.274, 184.788], 5, [-0.024366678156219807, 1.5224943718881883, -0.008169854999700415], 1.0, 5.0, "confused", 42, "correct", 46.79321203240345, "baseball", 5.14940881729126, "mug", 1.0, 42, 1, [-0.024366678156219807, 1.5224943718881883, -0.008169854999700415], [170.7110437788974, 7.273500038136469, -175.21205879897977], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.023573486369566632, 1.5231954807807486, 0.015031038212694796], "LM_0", "baseball", [0.0, 1.5, 0.0], 0.2239, 1.0], ["g_cups", 1.0, 132, 26, [0, 180, 0], "correct", 5, 0.2826, [0.041, 32.645, 359.628], 3, [-0.03648504161025374, 1.5216366385412223, -0.015222599546590184], 1.0, 3.0, "confused", 26, "correct", 34.97089086862343, "g_cups", 3.045257806777954, "mug", 1.0, 26, 1, [-0.03648504161025374, 1.5216366385412223, -0.015222599546590184], [0.04067085386217624, 32.64508776824231, -0.37177824933394366], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [-0.03714232137255949, 1.5232559930479657, 0.0069472572935350765], "LM_0", "g_cups", [0.0, 1.5, 0.0], 0.2826, 1.0], ["gelatin_box", 1.0, 1405, 227, [0, 180, 0], "correct", 5, 0.0692, [177.725, 0.168, 183.242], 31, [-0.043912874668285494, 1.4923973374173078, -0.018136191299836783], 1.0, 28.0, "confused", 227, "correct", 256.91152535276257, "gelatin_box", 31.871700286865234, "mug", 1.0, 227, 1, [-0.043912874668285494, 1.4923973374173078, -0.018136191299836783], [177.72517816861838, 0.16772841739397476, -176.7575074919716], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.04125384721278212, 1.4919922546845834, 0.018544111880229558], "LM_0", "gelatin_box", [0.0, 1.5, 0.0], 0.0692, 1.0], ["lemon", 1.0, 208, 31, [0, 180, 0], "correct", 5, 0.3865, [148.594, 0.663, 148.257], 4, [-0.016144116461306774, 1.4751421979609223, 0.006492719112928695], 1.0, 4.0, "confused", 31, "correct", 35.55780126578758, "lemon", 4.117107391357422, "mug", 1.0, 31, 1, [-0.016144116461306774, 1.4751421979609223, 0.006492719112928695], [148.5937898409169, 0.6629811263486448, 148.2570225726682], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.0022857100697085157, 1.4773013359296172, -0.016385408012861785], "LM_0", "lemon", [0.0, 1.5, 0.0], 0.3865, 1.0], ["plum", 1.0, 232, 35, [0, 180, 0], "correct", 5, 0.1896, [179.503, 356.157, 190.168], 5, [-0.025466861183331732, 1.5072248575338718, -0.0015694448694245026], 1.0, 5.0, "confused", 35, "correct", 45.053181765951216, "plum", 3.9237470626831055, "mug", 1.0, 35, 1, [-0.025466861183331732, 1.5072248575338718, -0.0015694448694245026], [179.5029588712854, -3.8431656385126733, -169.83221801567566], "target_in_possible_matches_(TP)", [6.123233995736766e-17, 0.0, 1.0, 0.0], [0.022492346168121978, 1.5117323248541685, -0.00010384290922427599], "LM_0", "plum", [0.0, 1.5, 0.0], 0.1896, 1.0]]}