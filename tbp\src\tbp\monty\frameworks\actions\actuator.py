# Copyright 2025 Thousand Brains Project
# Copyright 2024 Numenta Inc.
#
# Copyright may exist in Contributors' modifications
# and/or contributions to the work.
#
# Use of this source code is governed by the MIT
# license that can be found in the LICENSE file or at
# https://opensource.org/licenses/MIT.

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from tbp.monty.frameworks.actions.actions import (
        LookDown,
        LookUp,
        MoveForward,
        MoveTangentially,
        OrientHorizontal,
        OrientVertical,
        SetAgentPitch,
        SetAgentPose,
        SetSensorPitch,
        SetSensorPose,
        SetSensorRotation,
        SetYaw,
        TurnLeft,
        TurnRight,
    )

__all__ = [
    "Actuator",
]


class Actuator(ABC):
    """An actuator that can execute actions.

    An actuator is responsible for executing actions generated by the MotorSystem.
    An environment implementation will pass the actuator to the action's act() method
    and the action will select the appropriate actuate method to call.

    For example, the LookDown action will call actuate_look_down() on the actuator.
    """

    @abstractmethod
    def actuate_look_down(self, action: LookDown) -> None:
        pass

    @abstractmethod
    def actuate_look_up(self, action: LookUp) -> None:
        pass

    @abstractmethod
    def actuate_move_forward(self, action: MoveForward) -> None:
        pass

    @abstractmethod
    def actuate_move_tangentially(self, action: MoveTangentially) -> None:
        pass

    @abstractmethod
    def actuate_orient_horizontal(self, action: OrientHorizontal) -> None:
        pass

    @abstractmethod
    def actuate_orient_vertical(self, action: OrientVertical) -> None:
        pass

    @abstractmethod
    def actuate_set_agent_pitch(self, action: SetAgentPitch) -> None:
        pass

    @abstractmethod
    def actuate_set_agent_pose(self, action: SetAgentPose) -> None:
        pass

    @abstractmethod
    def actuate_set_sensor_pitch(self, action: SetSensorPitch) -> None:
        pass

    @abstractmethod
    def actuate_set_sensor_pose(self, action: SetSensorPose) -> None:
        pass

    @abstractmethod
    def actuate_set_sensor_rotation(self, action: SetSensorRotation) -> None:
        pass

    @abstractmethod
    def actuate_set_yaw(self, action: SetYaw) -> None:
        pass

    @abstractmethod
    def actuate_turn_left(self, action: TurnLeft) -> None:
        pass

    @abstractmethod
    def actuate_turn_right(self, action: TurnRight) -> None:
        pass
