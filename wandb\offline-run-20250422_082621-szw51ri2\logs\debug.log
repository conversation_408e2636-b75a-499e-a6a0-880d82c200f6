2025-04-22 08:26:21,162 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Current SDK version is 0.16.6
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Configure stats pid to 6024
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Loading settings from /Users/<USER>/.config/wandb/settings
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Loading settings from /Users/<USER>/Downloads/tbp.monty-main/benchmarks/wandb/settings
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Loading settings from environment variables: {'root_dir': '/Users/<USER>/tbp/logs/wandb'}
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Applying setup settings: {'_disable_service': False}
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Inferring run settings from compute environment: {'program_relpath': 'run.py', 'program_abspath': '/Users/<USER>/Downloads/tbp.monty-main/benchmarks/run.py', 'program': 'run.py'}
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Applying login settings: {}
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_setup.py:_flush():76] Applying login settings: {'mode': 'offline'}
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_init.py:_log_setup():521] Logging user logs to /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250422_082621-szw51ri2/logs/debug.log
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_init.py:_log_setup():522] Logging internal logs to /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250422_082621-szw51ri2/logs/debug-internal.log
2025-04-22 08:26:21,163 INFO    MainThread:6024 [wandb_init.py:init():561] calling init triggers
2025-04-22 08:26:21,165 INFO    MainThread:6024 [wandb_init.py:init():568] wandb.init called with sweep_config: {}
config: {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 5000, 'n_train_epochs': 3, 'n_eval_epochs': 3, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_77obj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/logs/projects/evidence_eval_runs/logs/base_77obj_surf_agent', 'run_name': 'base_77obj_surf_agent', 'resume_wandb_run': False, 'wandb_id': 'szw51ri2', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': False}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 5, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'delta_thresholds': {'on_object': 0, 'n_steps': 20, 'hsv': [0.1, 0.1, 0.1], 'pose_vectors': [0.7853981633974483, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [2, 2], 'distance': 0.01}, 'surf_agent_sm': True, 'save_raw_obs': False}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': False}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x15e3a1fa0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x15e3af0a0>], 'rng': RandomState(MT19937) at 0x15E300340}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'master_chef_can', 'i_cups', 'spoon', 'b_cups', 'pitcher_base', 'knife', 'b_marbles', 'h_cups', 'strawberry', 'power_drill', 'padlock', 'golf_ball', 'hammer', 'softball', 'orange', 'c_lego_duplo', 'c_toy_airplane', 'b_lego_duplo', 'banana', 'nine_hole_peg_test', 'tomato_soup_can', 'baseball', 'g_cups', 'gelatin_box', 'lemon', 'plum', 'racquetball', 'plate', 'pudding_box', 'e_cups', 'apple', 'j_cups', 'foam_brick', 'large_marker', 'peach', 'phillips_screwdriver', 'a_toy_airplane', 'e_lego_duplo', 'sugar_box', 'a_colored_wood_blocks', 'c_cups', 'pear', 'f_cups', 'wood_block', 'd_lego_duplo', 'b_toy_airplane', 'b_colored_wood_blocks', 'g_lego_duplo', 'a_lego_duplo', 'mini_soccer_ball', 'medium_clamp', 'a_marbles', 'extra_large_clamp', 'd_cups', 'e_toy_airplane', 'adjustable_wrench', 'rubiks_cube', 'f_lego_duplo', 'a_cups', 'skillet_lid', 'sponge', 'tennis_ball', 'spatula', 'd_toy_airplane', 'chain', 'scissors', 'mustard_bottle', 'bleach_cleanser', 'tuna_fish_can', 'cracker_box', 'fork', 'large_clamp', 'dice', 'flat_screwdriver'], 'object_init_sampler': PredefinedObjectInitializer with params: 
	 positions: [[0.0, 1.5, 0.0]]
	 rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0])]
	 change every episode: None}}
2025-04-22 08:26:21,165 INFO    MainThread:6024 [wandb_init.py:init():611] starting backend
2025-04-22 08:26:21,165 INFO    MainThread:6024 [wandb_init.py:init():615] setting up manager
2025-04-22 08:26:21,173 INFO    MainThread:6024 [wandb_init.py:init():623] backend started and connected
2025-04-22 08:26:21,178 INFO    MainThread:6024 [wandb_init.py:init():715] updated telemetry
2025-04-22 08:26:21,178 INFO    MainThread:6024 [wandb_init.py:init():748] communicating run to backend with 90.0 second timeout
2025-04-22 08:26:21,184 INFO    MainThread:6024 [wandb_init.py:init():799] starting run threads in backend
2025-04-22 08:26:23,314 INFO    MainThread:6024 [wandb_init.py:init():842] run started, returning control to user process
