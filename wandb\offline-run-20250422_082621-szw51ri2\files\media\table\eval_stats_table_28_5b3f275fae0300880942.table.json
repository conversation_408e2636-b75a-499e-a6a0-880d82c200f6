{"columns": ["primary_target_object", "mean_objects_per_graph", "monty_steps", "monty_matching_steps", "primary_target_rotation_euler", "primary_performance", "symmetry_evidence", "individual_ts_rotation_error", "detected_rotation", "goal_states_attempted", "detected_location", "mean_graphs_per_object", "goal_state_achieved", "stepwise_performance", "individual_ts_reached_at_step", "individual_ts_performance", "highest_evidence", "result", "time", "stepwise_target_object", "detected_scale", "num_steps", "num_possible_matches", "most_likely_location", "most_likely_rotation", "TFNP", "primary_target_rotation_quat", "location_rel_body", "lm_id", "most_likely_object", "primary_target_position", "rotation_error", "primary_target_scale"], "data": [["mug", 1.0, 145, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.04827869784446947, 1.5255843390296748, 0.0006595213846287335], 1.0, 3, "correct", 21, "correct", 32.19389246122554, "mug", 3.1412711143493652, "mug", 1.0, 21, 1, [0.04827869784446947, 1.5255843390296748, 0.0006595213846287335], [-1.2613639698555097e-09, -1.414141657471365e-07, -3.4677215954426507e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.048278657670249914, 1.5255842113998312, 0.000659535022124064], "LM_0", "mug", [0.0, 1.5, 0.0], 0.0, 1.0], ["bowl", 1.0, 171, 28, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.01709874844364522, 1.5262877823110497, 0.07252894416912768], 1.0, 3, "confused", 28, "correct", 39.93795051765593, "bowl", 3.1029160022735596, "mug", 1.0, 28, 1, [0.01709874844364522, 1.5262877823110497, 0.07252894416912768], [6.978751307343297e-07, 1.7226730182351677e-07, -2.8359716024226857e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.017099367796117256, 1.526288022269123, 0.07252900867473087], "LM_0", "bowl", [0.0, 1.5, 0.0], 0.0, 1.0], ["potted_meat_can", 1.0, 92, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [0.0466503971397864, 1.4634979587491566, -0.012595344189527992], 1.0, 2, "confused", 21, "correct", 24.933970799296688, "potted_meat_can", 5.943916082382202, "mug", 1.0, 21, 1, [0.0466503971397864, 1.4634979587491566, -0.012595344189527992], [1.5923807169492286e-07, -3.818497617252743e-08, 1.9577323012240975e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.046650397961418635, 1.4634979150237282, -0.012595343481950974], "LM_0", "potted_meat_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["master_chef_can", 1.0, 134, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.04670856835808608, 1.4648853329989062, -0.017471676128368516], 1.0, 1, "confused", 21, "correct", 31.665217320797964, "master_chef_can", 2.686673164367676, "mug", 1.0, 21, 1, [-0.04670856835808608, 1.4648853329989062, -0.017471676128368516], [-9.732044985379409e-09, 1.4512756511549494e-08, 5.2308631053121705e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.04670310315014935, 1.4648881846948136, -0.01747181344030455], "LM_0", "master_chef_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["i_cups", 1.0, 175, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.042700593155055026, 1.537517373946107, -0.01144203696830286], 1.0, 4, "confused", 26, "correct", 40.302988965298226, "i_cups", 2.8326590061187744, "mug", 1.0, 26, 1, [-0.042700593155055026, 1.537517373946107, -0.01144203696830286], [3.150893572322168e-08, -2.7146504928383972e-08, -7.2058910034345045e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.04269878762702425, 1.5375154339290544, -0.011442031927366833], "LM_0", "i_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["spoon", 1.0, 142, 24, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.08115347375403746, 1.500694834429485, 0.03913170907242402], 1.0, 0, "confused", 24, "correct", 44.85396477738898, "spoon", 3.629119873046875, "mug", 1.0, 24, 1, [0.08115347375403746, 1.500694834429485, 0.03913170907242402], [3.238950197556162e-07, -1.1112985279220786e-09, 8.710127520444519e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.08114656764585267, 1.5007467485951698, 0.03918786097125007], "LM_0", "spoon", [0.0, 1.5, 0.0], 0.0, 1.0], ["b_cups", 1.0, 186, 27, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [0.029921242585189665, 1.530788189106696, 5.652351681443299e-05], 1.0, 4, "confused", 27, "correct", 39.7510521633753, "b_cups", 2.9814329147338867, "mug", 1.0, 27, 1, [0.029921242585189665, 1.530788189106696, 5.652351681443299e-05], [-4.3854034740478e-08, 1.4506789790967068e-08, 2.2723548510264056e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.029921574269422477, 1.5307884479924598, 5.65324073414739e-05], "LM_0", "b_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["pitcher_base", 1.0, 143, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.06926268455539607, 1.5904807014133693, 0.052190912693183676], 1.0, 3, "confused", 21, "correct", 32.74147845721651, "pitcher_base", 2.4874958992004395, "mug", 1.0, 21, 1, [-0.06926268455539607, 1.5904807014133693, 0.052190912693183676], [2.758725620563841e-08, -1.035721933136768e-07, -1.0227456002932006e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.06926462090286849, 1.5904820742147538, 0.05219117172008219], "LM_0", "pitcher_base", [0.0, 1.5, 0.0], 0.0, 1.0], ["knife", 1.0, 120, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.09668441981628999, 1.510719869793297, 0.00447514658180902], 1.0, 0, "confused", 21, "correct", 34.67410902521489, "knife", 2.2306649684906006, "mug", 1.0, 21, 1, [0.09668441981628999, 1.510719869793297, 0.00447514658180902], [-1.6369901593387637e-07, 1.2261876182367141e-06, -1.534748308835367e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.09669261846134418, 1.5107198938022095, 0.004474822159162554], "LM_0", "knife", [0.0, 1.5, 0.0], 0.0, 1.0], ["b_marbles", 1.0, 148, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.007847781340527781, 1.493517350414534, -0.014162004251139663], 1.0, 4, "confused", 26, "correct", 34.82293547201873, "b_marbles", 3.4743850231170654, "mug", 1.0, 26, 1, [-0.007847781340527781, 1.493517350414534, -0.014162004251139663], [-2.739911952083711e-10, 2.7320659380522187e-08, -2.000562264121761e-09], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.007848392379279081, 1.49351651526853, -0.014162021583061426], "LM_0", "b_marbles", [0.0, 1.5, 0.0], 0.0, 1.0], ["h_cups", 1.0, 224, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.04473852656499761, 1.5342375734331517, 0.008428707402263815], 1.0, 4, "confused", 26, "correct", 42.478306590819734, "h_cups", 3.2311108112335205, "mug", 1.0, 26, 1, [-0.04473852656499761, 1.5342375734331517, 0.008428707402263815], [-2.4937908845209843e-09, -1.9323905244887e-09, -1.3092378512213604e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.044738551764022534, 1.5342375949939515, 0.008428708146116026], "LM_0", "h_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["strawberry", 1.0, 96, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.022483041979164658, 1.4941509263136845, 0.014537135200170186], 1.0, 3, "confused", 21, "correct", 34.17244963005974, "strawberry", 3.0171961784362793, "mug", 1.0, 21, 1, [-0.022483041979164658, 1.4941509263136845, 0.014537135200170186], [3.069171329337746e-07, 2.05738428791522e-07, 7.879674806093617e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.022480342583121683, 1.4941534238552878, 0.014536356578650333], "LM_0", "strawberry", [0.0, 1.5, 0.0], 0.0, 1.0], ["power_drill", 1.0, 96, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.06077884527306399, 1.5006505694236727, 0.012870232226401934], 1.0, 3, "confused", 21, "correct", 27.461190211770532, "power_drill", 2.1366989612579346, "mug", 1.0, 21, 1, [0.06077884527306399, 1.5006505694236727, 0.012870232226401934], [-5.141601114603027e-08, -4.60764666704249e-08, -6.153363247354684e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.0607783771826915, 1.5006516276213273, 0.012870252524684465], "LM_0", "power_drill", [0.0, 1.5, 0.0], 0.0, 1.0], ["padlock", 1.0, 143, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.025292629277046834, 1.5125522919067147, -0.006990440650448338], 1.0, 2, "confused", 21, "correct", 27.853982107152238, "padlock", 2.7421212196350098, "mug", 1.0, 21, 1, [0.025292629277046834, 1.5125522919067147, -0.006990440650448338], [3.1581362644556584e-07, -2.2243500503623926e-07, -6.641737488498684e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.025292606670351884, 1.5125523384269077, -0.006990445975906282], "LM_0", "padlock", [0.0, 1.5, 0.0], 0.0, 1.0], ["golf_ball", 1.0, 164, 31, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.020561978042439668, 1.5021215708859097, -0.0053966076227389215], 1.0, 3, "confused", 31, "correct", 40.15903012897385, "golf_ball", 3.4924240112304688, "mug", 1.0, 31, 1, [-0.020561978042439668, 1.5021215708859097, -0.0053966076227389215], [3.756775589794819e-09, 1.0791634756424439e-08, -3.995064025605598e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.020560822505702507, 1.5021220861254372, -0.005396609621132874], "LM_0", "golf_ball", [0.0, 1.5, 0.0], 0.0, 1.0], ["hammer", 1.0, 140, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.08936322089907862, 1.51641757013629, -0.0698195857937624], 1.0, 3, "confused", 21, "correct", 32.11904099562834, "hammer", 2.5822737216949463, "mug", 1.0, 21, 1, [-0.08936322089907862, 1.51641757013629, -0.0698195857937624], [2.8081628726758928e-08, 4.786865834455469e-08, 3.184652947777561e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.08936299573781488, 1.5164153812126449, -0.06981923905831437], "LM_0", "hammer", [0.0, 1.5, 0.0], 0.0, 1.0], ["softball", 1.0, 96, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.02380926064348559, 1.4916661767939705, -0.04108013907011449], 1.0, 2, "confused", 21, "correct", 30.913145983784357, "softball", 2.2146260738372803, "mug", 1.0, 21, 1, [0.02380926064348559, 1.4916661767939705, -0.04108013907011449], [1.3763687260527922e-08, 5.185173058324508e-08, -2.286077889700552e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.023809492301238182, 1.4916664761063327, -0.041080143688566155], "LM_0", "softball", [0.0, 1.5, 0.0], 0.0, 1.0], ["orange", 1.0, 268, 30, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.010086676395520362, 1.5317144119614134, -0.011577700108721051], 1.0, 4, "confused", 30, "correct", 40.488834200881485, "orange", 4.081480026245117, "mug", 1.0, 30, 1, [-0.010086676395520362, 1.5317144119614134, -0.011577700108721051], [1.2717523419955188e-08, -6.937184149052432e-09, -2.3049135434469525e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.010087424674388638, 1.5317160535103995, -0.011577657866651093], "LM_0", "orange", [0.0, 1.5, 0.0], 0.0, 1.0], ["c_lego_duplo", 1.0, 172, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.008967959137758573, 1.5108152084227153, -0.021279408716235505], 1.0, 3, "confused", 21, "correct", 25.5012843838844, "c_lego_duplo", 2.8903932571411133, "mug", 1.0, 21, 1, [0.008967959137758573, 1.5108152084227153, -0.021279408716235505], [-1.2614421786175082e-07, 2.5534116371506678e-08, -3.9970177339689945e-06], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.00896796010932069, 1.5108152240037913, -0.021279409379506165], "LM_0", "c_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["c_toy_airplane", 1.0, 220, 49, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 7, [-0.001395697747141717, 1.478431184136395, 0.015033776569308405], 1.0, 7, "confused", 49, "correct", 77.36548115262285, "c_toy_airplane", 4.102306127548218, "mug", 1.0, 49, 1, [-0.001395697747141717, 1.478431184136395, 0.015033776569308405], [1.8762509628470994e-08, 4.756299431744732e-08, 3.6807548572767705e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.0013958466886639358, 1.4784311823554086, 0.015033785062225604], "LM_0", "c_toy_airplane", [0.0, 1.5, 0.0], 0.0, 1.0], ["b_lego_duplo", 1.0, 202, 26, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.015661252795231652, 1.4805078469078181, 0.002746625705774469], 1.0, 4, "confused", 26, "correct", 35.61927747949522, "b_lego_duplo", 4.951666831970215, "mug", 1.0, 26, 1, [-0.015661252795231652, 1.4805078469078181, 0.002746625705774469], [-1.6888438061761374e-08, -7.657479278875984e-08, -1.4952429343304704e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.015661252657065985, 1.4805078724291, 0.0027466257564161397], "LM_0", "b_lego_duplo", [0.0, 1.5, 0.0], 0.0, 1.0], ["banana", 1.0, 120, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.04999610932316649, 1.4926414909632164, -0.08664602004554531], 1.0, 2, "confused", 21, "correct", 35.0843388480251, "banana", 2.522300958633423, "mug", 1.0, 21, 1, [0.04999610932316649, 1.4926414909632164, -0.08664602004554531], [7.212638700917754e-08, -4.298314060943333e-07, -8.665352505319452e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.04999636392422889, 1.4926427326428324, -0.08664617105846416], "LM_0", "banana", [0.0, 1.5, 0.0], 0.0, 1.0], ["nine_hole_peg_test", 1.0, 189, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 2, [0.0682927168309078, 1.5092309757674087, 0.024522235732481114], 1.0, 2, "confused", 21, "correct", 28.89686457843024, "nine_hole_peg_test", 3.3018808364868164, "mug", 1.0, 21, 1, [0.0682927168309078, 1.5092309757674087, 0.024522235732481114], [1.2288010302865685e-07, 5.545917931405463e-08, -2.1705431498744936e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.06829271960677807, 1.5092310143980514, 0.024522233403470463], "LM_0", "nine_hole_peg_test", [0.0, 1.5, 0.0], 0.0, 1.0], ["tomato_soup_can", 1.0, 120, 24, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.02178593246148498, 1.4595707462146077, 0.025571353419951516], 1.0, 3, "confused", 24, "correct", 30.52393790334865, "tomato_soup_can", 4.8863208293914795, "mug", 1.0, 24, 1, [-0.02178593246148498, 1.4595707462146077, 0.025571353419951516], [9.940338626840203e-06, 1.0206910629953862e-05, -0.00012654160219840383], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.021786044485101727, 1.4595706636613341, 0.0255713402053138], "LM_0", "tomato_soup_can", [0.0, 1.5, 0.0], 0.0, 1.0], ["baseball", 1.0, 332, 55, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 8, [-0.0013941509784767222, 1.5007086479617526, 0.03645743906096772], 1.0, 8, "confused", 55, "correct", 68.45322973317656, "baseball", 5.9697349071502686, "mug", 1.0, 55, 1, [-0.0013941509784767222, 1.5007086479617526, 0.03645743906096772], [-1.4231639448092587e-07, 5.6411132052435386e-08, 2.869372163189743e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.001395912139444873, 1.5007095122921121, 0.036457242748678634], "LM_0", "baseball", [0.0, 1.5, 0.0], 0.0, 1.0], ["g_cups", 1.0, 227, 29, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 4, [-0.03020814764104972, 1.4949624345337917, -0.021989609104783328], 1.0, 4, "confused", 29, "correct", 44.48066690958218, "g_cups", 3.7081892490386963, "mug", 1.0, 29, 1, [-0.03020814764104972, 1.4949624345337917, -0.021989609104783328], [-1.461074131721693e-09, 2.2093150594943655e-09, 1.0428786855703858e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.03020814975685856, 1.4949623541868144, -0.021989608505187185], "LM_0", "g_cups", [0.0, 1.5, 0.0], 0.0, 1.0], ["gelatin_box", 1.0, 139, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [0.0242177959350136, 1.4854867515376784, -0.03513005337949673], 1.0, 2, "confused", 21, "correct", 33.840996086191105, "gelatin_box", 2.682337760925293, "mug", 1.0, 21, 1, [0.0242177959350136, 1.4854867515376784, -0.03513005337949673], [4.8946342199837105e-08, -5.70719495374937e-08, 1.1486151335007983e-11], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [0.024218102396946856, 1.4854862471179302, -0.035130154683703695], "LM_0", "gelatin_box", [0.0, 1.5, 0.0], 0.0, 1.0], ["lemon", 1.0, 104, 21, [0, 0, 0], "correct", 0, 0.0, [0.0, 0.0, 0.0], 3, [-0.023987830645856602, 1.4828287742192205, 0.006343702378537796], 1.0, 3, "confused", 21, "correct", 30.337422686136602, "lemon", 2.738637685775757, "mug", 1.0, 21, 1, [-0.023987830645856602, 1.4828287742192205, 0.006343702378537796], [-1.0911125239494148e-07, -2.794013219916522e-07, -2.1080659515179813e-07], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.02397364858699426, 1.4828361027200831, 0.006341299067296411], "LM_0", "lemon", [0.0, 1.5, 0.0], 0.0, 1.0], ["plum", 1.0, 444, 66, [0, 0, 0], "correct", 5, 0.0, [0.0, 0.0, 0.0], 10, [-0.020132559413568264, 1.4790838929651904, 0.009745738836979077], 1.0, 10, "confused", 66, "correct", 93.83029986464999, "plum", 8.298624992370605, "mug", 1.0, 66, 1, [-0.020132559413568264, 1.4790838929651904, 0.009745738836979077], [-1.2626560780473395e-07, -9.089344640920897e-08, 1.604731065777067e-08], "target_in_possible_matches_(TP)", [1.0, 0.0, 0.0, 0.0], [-0.02014041122217691, 1.4790847579672548, 0.009746393211508294], "LM_0", "plum", [0.0, 1.5, 0.0], 0.0, 1.0]]}