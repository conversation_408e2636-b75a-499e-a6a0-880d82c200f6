{% set version_match = load_file_regex(
    load_file="../src/tbp/monty/__init__.py",
    regex_pattern='__version__\s*=\s*"([^"]+)"',
    from_recipe_dir=True) %}
{% set version = version_match[1] %}

package:
  name: tbp.monty
  version: {{ version}}

source:
  path: ../

build:
  script: python -m pip install .

requirements:
  host:
    - python==3.8
    - pip
  run:
    - python==3.8
    - aihabitat::habitat-sim=0.2.2 # [linux64 or osx]
    - aihabitat::withbullet
    - conda-forge::cmake>=3.14.0
    - conda-forge::importlib_resources
    - conda-forge::matplotlib
    - conda-forge::mkl<2022 # [osx]
    - conda-forge::mkl==2024.0.0 # [linux64]
    - conda-forge::numpy<=1.23.5 # numpy >=1.24.0 missing np.long
    - conda-forge::pandas
    - conda-forge::pillow
    - conda-forge::quaternion=2023.0.3 # later versions missing np.long
    - conda-forge::scikit-image
    - conda-forge::scikit-learn=1.3.2
    - conda-forge::scipy
    - conda-forge::sympy
    - conda-forge::tqdm
    - conda-forge::wandb
    - conda-forge::wget
    - pyg::pyg
    - pytorch::pytorch=1.11.0
    - pytorch::torchvision

about:
  home: https://github.com/thousandbrainsproject/tbp.monty
  license: MIT
  license_file: LICENSE
  summary: Monty is a sensorimotor learning framework based on the thousand brains theory of the neocortex.
  description: |
    On Linux:
    `conda install -c aihabitat -c pytorch -c pyg -c conda-forge thousandbrainsproject::tbp.monty`
    On MacOS Monty relies on Rosetta to emulate x86_64 architecture, hence `--subdir osx-64` is required:
    `conda install -c aihabitat -c pytorch -c pyg -c conda-forge thousandbrainsproject::tbp.monty --subdir osx-64`
  dev_url: https://github.com/thousandbrainsproject/tbp.monty
  doc_url: https://thousandbrainsproject.readme.io
