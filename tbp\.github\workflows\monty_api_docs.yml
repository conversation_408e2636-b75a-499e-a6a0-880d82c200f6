name: Monty API Docs

concurrency:
  group: pages
  cancel-in-progress: false

on:
  workflow_run:
    workflows:
      - Monty
    branches:
      - main
    types:
      - completed

permissions:
  contents: read
  pages: write
  id-token: write

jobs:

  deploy_sphinx_monty_api_docs:
    name: deploy-sphinx-monty-api-docs
    runs-on: ubuntu-latest
    if: ${{ (github.repository_owner == 'thousandbrainsproject') && (github.event.workflow_run.conclusion == 'success') }}
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Checkout tbp.monty
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
          path: tbp.monty
      # We want to deploy the docs using the same criteria for deciding if <PERSON> code changed.
      - name: Should Run Monty API Docs
        id: should_run_monty_api_docs
        uses: ./tbp.monty/.github/actions/should_run_monty
        with:
          git_base_ref: ${{ github.event.workflow_run.head_branch }}
          git_sha: ${{ github.event.workflow_run.head_sha }}
          github_event_name: ${{ github.event.workflow_run.event }}
          working_directory: tbp.monty
      # Downloading the Sphinx HTML artifact from the previous run.
      - name: Download artifact
        if: ${{ steps.should_run_monty_api_docs.outputs.should_run_monty == 'true'}}
        uses: actions/download-artifact@v4
        with:
          name: sphinx-html-${{ github.event.workflow_run.head_sha }}
          run-id: ${{ github.event.workflow_run.id }}
          path: tbp.monty/sphinx_html
          github-token: ${{ github.token }}
      - name: Upload GitHub Pages artifact
        if: ${{ steps.should_run_monty_api_docs.outputs.should_run_monty == 'true'}}
        uses: actions/upload-pages-artifact@v3
        with:
          name: github-pages-${{ github.event.workflow_run.head_sha }}
          path: tbp.monty/sphinx_html
      - name: Deploy Monty API docs
        id: deployment
        if: ${{ steps.should_run_monty_api_docs.outputs.should_run_monty == 'true'}}
        uses: actions/deploy-pages@v4
        with:
          artifact_name: github-pages-${{ github.event.workflow_run.head_sha }}
