name: Docs Deploy

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.run_id }}
  cancel-in-progress: true

on:
  push:
    branches:
      - main
    paths:
      - 'docs/**'
      - 'tools/github_readme_sync/**'
      - '.github/workflows/docs_deploy.yml'
      - '.github/actions/get_preview_info/**'

jobs:
  docs_deploy:
    name: docs-deploy
    runs-on: ubuntu-latest
    if: ${{ github.repository_owner == 'thousandbrainsproject' }}
    steps:
      - name: Checkout tbp.monty
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
          path: tbp.monty
      - name: Set up ~/tbp
        run: |
          mkdir -p ~/tbp
          ln -s $GITHUB_WORKSPACE/tbp.monty ~/tbp/tbp.monty
      - name: Set up Python 3.8
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"
      - name: Install miniconda
        run: |
          if [ ! -d ~/miniconda ]
          then
            wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O ~/miniconda.sh
            bash ~/miniconda.sh -b -p ~/miniconda
            rm ~/miniconda.sh
          fi
          export PATH="$HOME/miniconda/bin:$PATH"
          conda --version
      - name: Create conda environment
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          (conda env list | grep tbp.monty) && conda remove --name tbp.monty --all --yes || true
          conda env create
          source activate tbp.monty
          pip install -e .[dev,github_readme_sync_tool,print_version_tool]
      - name: Get version and branch
        id: preview_info
        uses: ./tbp.monty/.github/actions/get_preview_info
        with:
          user_login: thousandbrainsproject
      - name: Deploy docs
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          source activate tbp.monty
          export README_API_KEY=${{ secrets.README_API_KEY }}
          export IMAGE_PATH="${{ steps.preview_info.outputs.image_path }}"
          python -m tools.github_readme_sync.cli upload docs "${{ steps.preview_info.outputs.monty_version }}"