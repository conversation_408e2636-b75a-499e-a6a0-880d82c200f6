'''###修改大纲

基于图结构（Graph）和网格结构（Grid）来实现
代码库提供了两种主要的对象模型类：GraphObjectModel 和 GridObjectModel，它们都继承自一个抽象基类 ObjectModel

GraphObjectModel 类
初始化 ：创建一个对象模型实例，指定对象的唯一标识符 object_id，同时初始化一个空图结构 _graph 和一个标志 has_ppf 表示是否具有点对特征（Point Pair Features）
构建图模型 ：通过 build_model 方法，使用给定的位置 locations 和特征 features 构建一个图模型，其中 k_n 表示每个节点连接的最近邻节点数，graph_delta_thresholds 是用于确定是否添加新点的阈值
更新图模型 ：update_model 方法将新的位置和特征添加到现有的图模型中，并根据给定的相对位置和旋转信息进行变换，然后重新构建图
添加点对特征 ：add_ppf_to_graph 方法为图的边添加点对特征，这有助于在图匹配和识别等任务中提供更多的几何信息
属性和方法 ：提供了对图结构中节点特征、位置、边索引和边属性等的访问方法，以及设置图结构的方法
表示和日志记录 ：__repr__ 方法提供对象的字符串表示，用于调试和日志记录
    修改内容：
        类型	名称	                        替换/新增	功能说明
        属性	x_mean, x_var, edge_prob	替换/新增	表示节点分布和边存在概率
        方法	build_model()	            替换	        构建概率图结构
        方法	update_model()	            替换	        贝叶斯融合节点+Sigmoid更新边
        “
        方法	extract_observed_subgraph()	新增	        提取带概率信息的观测子图
        方法	match_score()	            新增	        使用 KL 散度 + 方差差距评估匹配
        ”  ###复用困难，移动至
        /tbp.monty-main/src/tbp/monty/frameworks/models/evidence_matching.py

GridObjectModel 类
初始化 ：继承自 GraphObjectModel，增加了对网格结构的支持。需要指定对象的唯一标识符object _id，最大节点数 max_nodes，对象的最大尺寸 max_size 和每个维度上的体素（voxel）数 num_voxels_per_dim
构建网格模型 ：build_model 方法根据给定的位置和特征构建网格模型，将位置映射到网格中，并初始化网格结构
更新网格模型 ：update_model 方法将新的位置和特征添加到网格中，并重新构建图模型
寻找最近邻节点 ：find_nearest_neighbors 方法使用 kd 树或网格索引查找图中与给定搜索位置最近的节点
设置图结构 ：set_graph 方法可以设置图结构，并根据是否使用原始图结构来决定是否使用网格来限制节点
表示和日志记录 ：__repr__ 方法提供对象的详细信息，包括网格形状和存储的特征信息
网格初始化和更新 ：定义了网格的初始化、更新和管理方法，包括将位置转换为网格索引、更新网格中的观测计数、位置和特征等
从网格构建图 ：_build_graph_from_grids 方法从网格结构中提取顶点位置和特征，构建图模型。它根据观测计数的多少选取顶点，以控制图的大小

    修改逻辑：
    点：
    1.需要修改 GraphObjectModel 类中节点特征的存储方式（节点特征表示）：
        在原来确定性的特征向量基础上，引入概率分布参数，如均值和方差来表示节点属性的不确定性
        在节点特征的存储结构（如 _graph.x）中增加相应的维度或字段来保存这些概率分布参数
        在 build_model 和 update_model 方法中，对传入的 features 进行处理，将其转化为概率分布形式，并存储在图结构中
    2.在 update_model 方法中，修改节点特征更新的逻辑（节点特征更新逻辑）：
        采用贝叶斯融合方法，结合新的观测数据和原有的特征分布，更新节点特征的概率分布，使特征均值逐步收敛，方差减小
        这就需要在更新过程中，不仅要考虑新的观测特征，还要考虑原有特征分布的均值和方差，按照贝叶斯规则进行融合计算
    边：
    1.在 GraphObjectModel 类中，为边增加存在概率的表示（边存在概率的表示与初始化）：
        可在图结构中为边添加一个新的属性（如 edge_prob）来存储边的存在概率
        构建图结构的初始阶段（如在 _build_adjacency_graph 方法中），为新生成的边赋予初始的存在概率（如 0.5），以反映连接关系的不确定性
    2.在 update_model 方法中，根据新的空间关系观测，修改边存在概率的更新逻辑（边存在概率的更新逻辑）：
        根据新的观测数据中节点之间的共现频率，利用 Sigmoid 函数来更新边的存在概率，提高或降低连接关系的可信度
    针对目标识别推理过程：
    1.在进行目标识别时，需要从当前感知数据中提取局部观测子图（观测子图的提取与表示）：
        子图包含节点特征的概率性描述（如特征分布的均值与方差）以及边存在概率等信息
        在相关的目标识别代码部分（可能是在一个新的方法或现有的方法中），实现观测子图的提取逻辑，确保提取的子图能够准确地反映感知数据中的概率性信息
    2.修改目标识别过程中的节点匹配和边匹配计算逻辑（节点匹配与边匹配的计算）：
        在节点匹配方面，不再仅仅比较节点特征的确定性值，而是根据节点特征分布（均值与方差），采用 KL 散度、Wasserstein 距离等相似性度量方法来计算节点间的分布相似度
        在边匹配方面，需要基于边存在概率的一致性来评估匹配程度
        在匹配相关的代码部分（可能是在一个新的匹配方法或现有的匹配方法中）实现这些新的相似性计算逻辑
    3.调整目标识别的整体匹配评分计算方式（整体匹配评分与识别决策）：
        综合考虑节点匹配得分和边匹配得分，计算观测子图与内部记忆图中各对象的整体匹配概率
        在识别决策阶段，根据整体匹配概率选择匹配概率最高的对象作为识别结果，并输出匹配置信度
        在目标识别的决策相关代码部分进行相应的修改和扩展

'''

import copy
import logging

import numpy as np
import torch
import torch_geometric
import torch_geometric.transforms as T
from scipy.spatial import KDTree
from sklearn.neighbors import kneighbors_graph
from torch_geometric.data import Data

from tbp.monty.frameworks.models.abstract_monty_classes import ObjectModel
from tbp.monty.frameworks.utils.graph_matching_utils import get_correct_k_n
from tbp.monty.frameworks.utils.object_model_utils import (
    NumpyGraph,
    build_point_cloud_graph,
    circular_mean,
    expand_index_dims,
    get_most_common_bool,
    get_values_from_dense_last_dim,
    increment_sparse_tensor_by_count,
    pose_vector_mean,
    remove_close_points,
    torch_graph_to_numpy,
)
from tbp.monty.frameworks.utils.spatial_arithmetics import apply_rf_transform_to_points


class GraphObjectModel(ObjectModel):
    """Object model class that represents object as graphs."""
    '''
    def __init__(self, object_id):
        """Initialize the object model.

        Args:
            object_id: id of the object
        """
        logging.info(f"init object model with id {object_id}")
        self.object_id = object_id
        self._graph = None
        self.has_ppf = False
    '''
    def __init__(self, object_id):
        super().__init__()
        self.object_id = object_id
        logging.info(f"Initializing GraphObjectModel with id: {object_id}")

        self._graph = Data()

        # ✅ 明确初始化 probabilistic node features
        self._graph.x_mean = torch.empty((0, 0))  # 例如你使用的是 8 维特征
        self._graph.x_var = torch.empty((0, 0))   # 同上

        # 初始化边信息（如果有边特征、存在概率等）
        self._graph.edge_index = torch.empty((2, 0), dtype=torch.long)
        self._graph.edge_prob = torch.empty((0,))

        self.has_ppf = False

    # =============== Public Interface Functions ===============
    # ------------------- Main Algorithm -----------------------
    '''
    def build_model(self, locations, features, k_n, graph_delta_thresholds):
        """Build graph from locations and features sorted into grids."""
        graph = self._build_adjacency_graph(
            locations,
            features,
            k_n=k_n,
            graph_delta_thresholds=graph_delta_thresholds,
            old_graph_index=0,
        )
        self.k_n = k_n
        self.graph_delta_thresholds = graph_delta_thresholds
        self.set_graph(graph)
        logging.info(f"built graph {self._graph}")
    '''
    def build_model(self, features: torch.Tensor, edge_index: torch.Tensor):
        # 初始化节点特征为正态分布的均值和单位方差 —— 修改
        self._graph.x_mean = features.clone()
        self._graph.x_var = torch.ones_like(features) * 1.0

        # 初始化边连接与边存在概率 —— 修改
        self._graph.edge_index = edge_index
        self._graph.edge_prob = torch.ones(edge_index.shape[1]) * 0.5  # 默认存在概率为 0.5

    '''
    def update_model(
        self,
        locations,
        features,
        location_rel_model,
        object_location_rel_body,
        object_rotation,
        object_scale=1,
    ):
        """Add new locations and features into grids and rebuild graph."""
        rf_locations, rf_features = apply_rf_transform_to_points(
            locations=locations,
            features=features,
            location_rel_model=location_rel_model,
            object_location_rel_body=object_location_rel_body,
            object_rotation=object_rotation,
        )

        all_locations, all_features = self._combine_graph_information(
            rf_locations,
            rf_features,
        )

        logging.info("building graph")
        new_graph = self._build_adjacency_graph(
            all_locations,
            all_features,
            k_n=self.k_n,
            graph_delta_thresholds=self.graph_delta_thresholds,
            old_graph_index=self.num_nodes,
        )
        self.set_graph(new_graph)
        if self.has_ppf:
            self.add_ppf_to_graph()
    '''

    def update_model(self, new_features: torch.Tensor, node_indices: torch.Tensor, new_edges: torch.Tensor):
        # 贝叶斯更新节点特征分布 —— 修改
        for i, idx in enumerate(node_indices):
            prior_mean = self._graph.x_mean[idx]
            prior_var = self._graph.x_var[idx]
            obs = new_features[i]
            obs_var = torch.ones_like(obs) * 0.5  # 假设观测不确定性固定 —— 可调参数

            posterior_var = 1 / (1 / prior_var + 1 / obs_var)
            posterior_mean = posterior_var * (prior_mean / prior_var + obs / obs_var)

            self._graph.x_mean[idx] = posterior_mean
            self._graph.x_var[idx] = posterior_var

        # 更新边存在概率 —— 修改
        for edge in new_edges.T:
            src, tgt = edge.tolist()
            existing = ((self._graph.edge_index[0] == src) & (self._graph.edge_index[1] == tgt)).nonzero(as_tuple=True)
            if existing[0].numel() > 0:
                idx = existing[0].item()
                self._graph.edge_prob[idx] = torch.sigmoid(self._graph.edge_prob[idx] + 0.5)  # 调整公式可优化
            else:
                # 添加新边及其存在概率
                self._graph.edge_index = torch.cat([self._graph.edge_index, edge.view(2, 1)], dim=1)
                self._graph.edge_prob = torch.cat([self._graph.edge_prob, torch.tensor([0.5])], dim=0)

    '''此处为匹配逻辑
    复用困难，移动至
    /tbp.monty-main/src/tbp/monty/frameworks/models/evidence_matching.py
    
    def extract_observed_subgraph(self, observed_nodes: torch.Tensor): ###新增方法
        sub_mean = self._graph.x_mean[observed_nodes]
        sub_var = self._graph.x_var[observed_nodes]
        node_map = {int(old): i for i, old in enumerate(observed_nodes.tolist())}
        mask = [(i, j) for i, j in self._graph.edge_index.T.tolist() if i in node_map and j in node_map]
        if mask:
            edge_index = torch.tensor([[node_map[i], node_map[j]] for i, j in mask]).T
            edge_prob = torch.tensor(
                [self._graph.edge_prob[k] for k, (i, j) in enumerate(self._graph.edge_index.T.tolist()) if
                 (i, j) in mask])
        else:
            edge_index = torch.empty((2, 0), dtype=torch.long)
            edge_prob = torch.empty((0,))

        return {
            'x_mean': sub_mean,
            'x_var': sub_var,
            'edge_index': edge_index,
            'edge_prob': edge_prob
        }

    def match_score(self, obs_subgraph, candidate_indices):  ###新增方法
        matched_scores = []
        for idx in candidate_indices:
            node_mean = self._graph.x_mean[idx]
            node_var = self._graph.x_var[idx]

            obs_mean = obs_subgraph['x_mean'][0]
            obs_var = obs_subgraph['x_var'][0]

            # 使用 KL 散度计算分布相似度
            kl = self.kl_divergence_gaussians(obs_mean, obs_var, node_mean, node_var)
            var_diff = torch.abs(obs_var - node_var).mean().item()

            node_score = -kl - var_diff  # 简化示意，可用更复杂方式加权
            matched_scores.append((idx, node_score))

        matched_scores.sort(key=lambda x: x[1], reverse=True)
        best_idx, best_score = matched_scores[0]
        return best_idx, torch.sigmoid(torch.tensor(best_score))  # 返回置信度形式

    def kl_divergence_gaussians(mu1: torch.Tensor, var1: torch.Tensor, mu2: torch.Tensor,
                                var2: torch.Tensor) -> torch.Tensor:  ###新增：紧跟KL 散度计算方法（self方法，全局不可用）
        """
        计算两个多维高斯分布 N(mu1, var1) 和 N(mu2, var2) 之间的 KL 散度
        var 是方差（而不是标准差）
        """
        term1 = torch.log(var2 / var1)
        term2 = (var1 + (mu1 - mu2) ** 2) / var2
        kl = 0.5 * torch.sum(term1 + term2 - 1)
        return kl
        
    '''

    def add_ppf_to_graph(self):
        """Add point pair features to graph edges."""
        self._graph = T.PointPairFeatures(cat=False)(self._graph)
        self.has_ppf = True

    # ------------------ Getters & Setters ---------------------
    # Keep original properties of graphs for backward compatibility.
    @property
    def x(self):
        # TODO: How do we want to deal with self._graph being None?
        if self._graph is not None:
            return self._graph.x

    @property
    def pos(self):
        if self._graph is not None:
            return self._graph.pos

    @property
    def norm(self):
        if self._graph is not None:
            return self._graph.norm

    @property
    def feature_mapping(self):
        if self._graph is not None:
            return self._graph.feature_mapping

    @property
    def edge_index(self):
        if (self._graph is not None) and ("edge_index" in self._graph.keys):
            return self._graph.edge_index

    @property
    def edge_attr(self):
        if (self._graph is not None) and ("edge_attr" in self._graph.keys):
            return self._graph.edge_attr

    @property
    def num_nodes(self):
        return len(self._graph.pos)

    @property
    def feature_ids_in_graph(self):
        if self._graph is not None:
            return self._graph.feature_mapping.keys()

    def set_graph(self, graph):
        """Set self._graph property with given graph (i.e. from pretraining)."""
        self._graph = graph

    def get_values_for_feature(self, feature):
        featue_idx = self.feature_mapping[feature]
        return self.x[:, featue_idx[0] : featue_idx[1]]

    # ------------------ Logging & Saving ----------------------
    def __repr__(self):
        """Return a string representation of the object."""
        if self._graph is not None:
            return self._graph.__repr__()
        else:
            return f"Model for {self.object_id}:\n   No graph stored yet."

    # ======================= Private ==========================
    # ------------------- Main Algorithm -----------------------

    def _combine_graph_information(
        self,
        locations,
        features,
    ):
        """Combine new observations with those already stored in a graph.

        Combines datapoints from an existing graph and new points collected in the
        buffer using the detected pose. This is a util function for extend_graph.

        Args:
            locations: new observed locations (x,y,z)
            features: new observed features (dict)

        Returns:
            combines features at locations with new locations transformed into
                the graphs reference frame.
        """
        old_points = self.pos
        feature_mapping = self.feature_mapping

        all_features = dict()

        # Iterate through the different feature types, stacking on (i.e. appending)
        # those features associated w/ candidate new points to the old-graph point
        # features
        for feature in features.keys():
            new_feat = np.array(features[feature])
            if len(new_feat.shape) == 1:
                new_feat = new_feat.reshape((new_feat.shape[0], 1))
            if feature in feature_mapping.keys():
                feature_idx = feature_mapping[feature]
                old_feat = np.array(self.x)[:, feature_idx[0] : feature_idx[1]]
            else:
                # NOTE: currently never happens since all features are present at every
                # step. Should we remove this? Will this ever happen in the future?
                # add new feature into graph
                feature_start_idx = self.x.shape[-1]
                feature_len = new_feat.shape[-1]
                feature_mapping[feature] = [
                    feature_start_idx,
                    feature_start_idx + feature_len,
                ]
                # Pad with zeros for existing locations in graph
                old_feat = np.zeros((old_points.shape[0], feature_len))

            both_feat = np.vstack([old_feat, new_feat])
            all_features[feature] = both_feat

            for graph_feature in self.feature_ids_in_graph:
                if graph_feature not in features.keys() and graph_feature != "node_ids":
                    raise NotImplementedError(
                        f"{graph_feature} is represented in graph but",
                        " was not observed at this step. Implement padding with nan.",
                    )

        all_locations = np.vstack([old_points, locations])

        return all_locations, all_features

    def _build_adjacency_graph(
        self, locations, features, k_n, graph_delta_thresholds, old_graph_index
    ):
        """Build graph from observations with nodes linking to the n nearest neighbors.

        if k_n == None, this function will just return a graph without edges.

        Args:
            locations: array of x, y, z positions in space
            features: dictionary of features at locations
            k_n: How many nearest nodes each node should link to. If None,
                just return a point cloud with no links
            graph_delta_thresholds: dictionary of thresholds; if the L-2 distance
                between the locations of two observations (or other feature-distance
                measure) is below all of the given thresholds, then a point will be
                considered insufficiently interesting to be added.
            old_graph_index: If the graph is not new, the index associated with the
                final point in the old graph; we will skip this when checking for
                sameness, as they will already have been compared in the past to
                one-another, saving computation.


        Returns:
            A torch_geometric.data graph containing the observed features at
                locations, with edges betweed the k_n nearest neighbors.
        """
        locations_reduced, clean_ids = remove_close_points(
            np.array(locations), features, graph_delta_thresholds, old_graph_index
        )
        num_nodes = locations_reduced.shape[0]
        node_features = np.linspace(0, num_nodes - 1, num_nodes).reshape((num_nodes, 1))
        feature_mapping = dict()
        feature_mapping["node_ids"] = [0, 1]

        for feature_id in features.keys():
            # Get only the features-at-points that were not removed as close/
            # redundant points
            feats = np.array([features[feature_id][i] for i in clean_ids])
            if len(feats.shape) == 1:
                feats = feats.reshape((feats.shape[0], 1))

            feature_mapping[feature_id] = [
                node_features.shape[1],
                node_features.shape[1] + feats.shape[1],
            ]
            node_features = np.column_stack((node_features, feats))

            if feature_id == "pose_vectors":
                norm = torch.tensor(feats[:, :3], dtype=torch.float)

        assert np.all(
            locations[:old_graph_index] == locations_reduced[:old_graph_index]
        ), "Old graph points shouldn't change"

        x = torch.tensor(node_features, dtype=torch.float)
        pos = torch.tensor(locations_reduced, dtype=torch.float)

        graph = Data(x=x, pos=pos, norm=norm, feature_mapping=feature_mapping)

        if k_n is not None:
            k_n = get_correct_k_n(k_n, num_nodes)

            scipy_graph = kneighbors_graph(
                locations_reduced, n_neighbors=k_n, include_self=False
            )

            scipygraph = torch_geometric.utils.from_scipy_sparse_matrix(scipy_graph)
            edge_index = scipygraph[0]

            displacements = []
            for e, edge_start in enumerate(edge_index[0]):
                edge_end = edge_index[1][e]
                displacements.append(
                    locations_reduced[edge_end] - locations_reduced[edge_start]
                )

            edge_attr = torch.tensor(np.array(displacements), dtype=torch.float)

            graph.edge_index = edge_index
            graph.edge_attr = edge_attr

        return graph

    # ------------------------ Helper --------------------------
    # ----------------------- Logging --------------------------


class GridTooSmallError(Exception):
    """Exception raised when grid is too small to fit all observations."""

    pass


class GridObjectModel(GraphObjectModel):
    """Model of an object and all its functions.

    This model has the same basic functionality as the NumpyGraph models used in older
    LM versions. On top of that we now have a grid representation of the object that
    constraints the model size and resultion. Additionally, this model class implements
    a lot of functionality that was previously implemented in the graph_utils.py file.

    TODO: General cleanups that require more changes in other code
        - remove node_ids from input_channels and have as graph attribute
        - remove .norm as attribute and store as feature instead?
    """

    def __init__(self, object_id, max_nodes, max_size, num_voxels_per_dim):
        """Initialize a grid object model.

        Args:
            object_id: id of the object
            max_nodes: maximum number of nodes in the graph. Will be k in k winner
                voxels with highest observation count.
            max_size: maximum size of the object in meters. Defines size of obejcts
                that can be represented and how locations are mapped into voxels.
            num_voxels_per_dim: number of voxels per dimension in the models grids.
                Defines the resolution of the model.
        """
        logging.info(f"init object model with id {object_id}")
        self.object_id = object_id
        self._graph = None
        self._max_nodes = max_nodes
        self._max_size = max_size  # 1=1meter
        self._num_voxels_per_dim = num_voxels_per_dim
        # Sparse, 4d torch tensors that store content in the voxels of the model grid.
        # number of observations in each voxel
        self._observation_count = None
        # Average features in each voxel with observations
        # The first 3 dims are the 3d voxel indices, the forth dimensions are features
        self._feature_grid = None
        # Average location in each voxel with observations
        # The first 3 dims are the 3d voxel indices, xyz in the fourth dimension is
        # the average location in that voxel at float precision.
        self._location_grid = None
        # For backward compatibility. May remove later on.
        # This will be true if we load a pretrained graph. If True, grids are not
        # filled or used to constrain nodes in graph.
        self.use_original_graph = False
        self._location_tree = None

    # =============== Public Interface Functions ===============
    # ------------------- Main Algorithm -----------------------
    def build_model(self, locations, features):
        """Build graph from locations and features sorted into grids."""
        (
            feature_array,
            observation_feature_mapping,
        ) = self._extract_feature_array(features)
        # TODO: part of init method?
        logging.info(f"building graph from {locations.shape[0]} observations")
        self._initialize_and_fill_grid(
            locations=locations,
            features=feature_array,
            observation_feature_mapping=observation_feature_mapping,
        )
        self._graph = self._build_graph_from_grids()
        logging.info(f"built graph {self._graph}")

    def update_model(
        self,
        locations,
        features,
        location_rel_model,
        object_location_rel_body,
        object_rotation,
    ):
        """Add new locations and features into grids and rebuild graph."""
        rf_locations, rf_features = apply_rf_transform_to_points(
            locations=locations,
            features=features,
            location_rel_model=location_rel_model,
            object_location_rel_body=object_location_rel_body,
            object_rotation=object_rotation,
        )
        (
            feature_array,
            observation_feature_mapping,
        ) = self._extract_feature_array(rf_features)
        logging.info(f"adding {locations.shape[0]} observations")
        self._update_grids(
            locations=rf_locations,
            features=feature_array,
            feature_mapping=observation_feature_mapping,
        )
        new_graph = self._build_graph_from_grids()
        assert not np.any(np.isnan(new_graph.x))
        self._graph = new_graph
        # TODO: remove eventually and do search directly in grid?
        self._location_tree = KDTree(
            new_graph.pos,
            leafsize=40,
        )

    def find_nearest_neighbors(
        self,
        search_locations,
        num_neighbors,
        return_distance=False,
    ):
        """Find nearest neighbors in graph for list of search locations.

        Note:
            This is currently using kd tree search. In the future we may consider
            doing this directly by indexing the grids. However, an initial
            implementation of this does not seem to be faster than the kd tree search
            (~5-10x slower). However one must consider that search directly in the grid
            would remove the cost of building the tree. TODO: Investigate this further.

        Returns:
            If return_distance is True, return distances. Otherwise, return indices of
            nearest neighbors.
        """
        # if self._location_tree is not None:
        # We are using the pretrained graphs and location trees for matching
        (distances, nearest_node_ids) = self._location_tree.query(
            search_locations,
            k=num_neighbors,
            p=2,  # eucledian distance
            workers=1,  # using more than 1 worker slows down run on lambda.
        )
        # else:
        #     # TODO: This is not done yet and doesn't work. It seems at the moment
        #     # That kd Tree search is still more efficient.
        #     # using new grid structure directly to query nearest neighbors
        #     distances, nearest_node_ids = self._retrieve_locations_in_radius(
        #         search_locations,
        #         search_radius=search_radius,
        #         max_neighbors=num_neighbors,
        #     )

        if return_distance:
            return distances
        else:
            return nearest_node_ids

    # ------------------ Getters & Setters ---------------------
    def set_graph(self, graph):
        """Set self._graph property and convert input graph to right format."""
        if type(graph) is not NumpyGraph:
            # could also check if is type torch_geometric.data.data.Data
            logging.debug(f"turning graph of type {type(graph)} into numpy graph")
            graph = torch_graph_to_numpy(graph)
        if self.use_original_graph:
            # Just use pretrained graph. Do not use grids to constrain nodes.
            self._graph = graph
            self._location_tree = KDTree(
                graph.pos,
                leafsize=40,
            )
        else:
            self._initialize_and_fill_grid(
                locations=graph.pos,
                features=graph.x,
                observation_feature_mapping=graph.feature_mapping,
            )
            self._graph = self._build_graph_from_grids()

    # ------------------ Logging & Saving ----------------------
    def __repr__(self) -> str:
        """Return a string representation of the object."""
        if self._graph is None:
            return f"Model for {self.object_id}:\n   No graph stored yet."
        if self._feature_grid is not None:
            grid_shape = self._feature_grid.shape
        else:
            grid_shape = 0
        repr_string = (
            f"Model for {self.object_id}:\n"
            f"   Contains {self.pos.shape[0]} points in graph.\n"
            f"   Feature grid shape: {grid_shape}\n"
            f"   Stored features and their indexes:\n"
        )
        for feature in self.feature_mapping:
            feat_ids = self.feature_mapping[feature]
            repr_string += f"           {feature} - {feat_ids[0]}:{feat_ids[1]},\n"

        return repr_string

    # ======================= Private ==========================
    # ------------------- Main Algorithm -----------------------
    def _initialize_location_mapping(self, start_location):
        """Calculate and set location_scale_factor and location_offset."""
        # scale locations to integer mappings
        voxel_size = self._max_size / self._num_voxels_per_dim
        # Find multiplier that turns voxel locations into round integer indices
        self._location_scale_factor = 1 / voxel_size
        start_index = np.array(
            np.round(start_location * self._location_scale_factor), dtype=int
        )
        # Find offset factor that places start_location at the center of the grid
        center_id = self._num_voxels_per_dim // 2
        center_voxel_index = np.array([center_id, center_id, center_id], dtype=int)
        self._location_offset = center_voxel_index - start_index

    def _initialize_and_fill_grid(
        self, locations, features, observation_feature_mapping
    ):
        # TODO: Do we still need to do this with sparse tensors?
        self._observation_count = self._generate_empty_grid(
            self._num_voxels_per_dim, n_entries=1
        )
        # initialize location mapping by calculating the scale factor and offset.
        # The offset is set such that the first observed location starts at the
        # center of the grid. To preserve the relative locations, the offset is
        # applied to all following locations.
        self._initialize_location_mapping(start_location=locations[0])
        # initialize self._feature_grid with feat_dim calculated from features
        feat_dim = features.shape[-1]
        self._feature_grid = self._generate_empty_grid(
            self._num_voxels_per_dim, n_entries=feat_dim
        )
        self._location_grid = self._generate_empty_grid(
            self._num_voxels_per_dim, n_entries=3
        )
        # increment counters in observation_count
        self._update_grids(
            locations=locations,
            features=features,
            feature_mapping=observation_feature_mapping,
        )

    def _update_grids(self, locations, features, feature_mapping):
        """Update count, location and feature grids with observations.

        Raises:
            GridTooSmallError: If too many observations are outside of the grid
        """
        location_grid_ids = self._locations_to_grid_ids(locations)
        locations_in_bounds = np.all(
            (location_grid_ids >= 0) & (location_grid_ids < self._num_voxels_per_dim),
            axis=1,
        )
        percent_in_bounds = sum(locations_in_bounds) / len(locations_in_bounds)
        if percent_in_bounds < 0.9:
            logging.info(
                "Too many observations outside of grid "
                f"({np.round(percent_in_bounds*100,2)}%). Skipping update of grids."
            )
            raise GridTooSmallError
        voxel_ids_of_new_obs = location_grid_ids[locations_in_bounds]
        self._observation_count = increment_sparse_tensor_by_count(
            self._observation_count, voxel_ids_of_new_obs
        )

        # if new features contain input channel or features, add them to mapping
        if self.feature_mapping is not None:
            updated_fm, new_feat_dim = self._update_feature_mapping(feature_mapping)
        else:  # no graph has been initialized yet
            updated_fm = feature_mapping
            new_feat_dim = features.shape[-1]

        # Since we have dense entries in the last dimension that we want associated
        # with the voxel id in the first 3 dims, we need to extract them a bit
        # tediously to do the averaging. TODO: Maybe there is a better way to do this
        new_features = []
        previous_features_at_indices = []
        new_locations = []
        previous_locations_at_indices = []
        new_indices = []

        locations = locations[locations_in_bounds]
        features = features[locations_in_bounds]
        # update average features for each voxel with content
        # The indices() function will give us the voxel indices that have values
        voxels_with_values = self._observation_count.indices()
        # Calculate average of new features and put in new_feature_grid
        for voxel in zip(
            voxels_with_values[0],
            voxels_with_values[1],
            voxels_with_values[2],
        ):
            observations_in_voxel_ids = np.where(
                (voxel_ids_of_new_obs == voxel).all(axis=1)
            )[0]
            # Only update if there are new observations for this voxel
            if len(observations_in_voxel_ids) > 0:
                new_indices.append(np.array(voxel))
                previous_loc_in_voxel = get_values_from_dense_last_dim(
                    self._location_grid, voxel
                )
                previous_locations_at_indices.append(previous_loc_in_voxel)
                locations_in_voxel = locations[observations_in_voxel_ids]
                new_avg_location = self._get_new_voxel_location(
                    locations_in_voxel,
                    previous_loc_in_voxel,
                    voxel,
                )
                new_locations.append(new_avg_location)

                previous_feat_in_voxel = get_values_from_dense_last_dim(
                    self._feature_grid, voxel
                )

                previous_features_at_indices.append(previous_feat_in_voxel)
                features_in_voxel = features[observations_in_voxel_ids]
                new_avg_feat = self._get_new_voxel_features(
                    features_in_voxel,
                    np.array(previous_feat_in_voxel),
                    voxel,
                    feature_mapping,
                    updated_fm,
                    new_feat_dim,
                )
                new_features.append(new_avg_feat)

        (
            prev_sparse_locs,
            new_sparse_locs,
        ) = self._old_new_lists_to_sparse_tensors(
            indices=new_indices,
            new_values=new_locations,
            old_values=previous_locations_at_indices,
            target_mat_shape=self._location_grid.shape,
        )
        # Subtract old locations since new ones already contain them in their average
        # Don't just overwrite with new_sparse_locs since we may have voxels in the
        # _location_grid that did not get updated and should not be set to 0 now.
        self._location_grid = self._location_grid - prev_sparse_locs + new_sparse_locs

        (
            prev_sparse_feats,
            new_sparse_feats,
        ) = self._old_new_lists_to_sparse_tensors(
            new_indices,
            new_features,
            previous_features_at_indices,
            self._feature_grid.shape,
        )
        self._feature_grid = self._feature_grid - prev_sparse_feats + new_sparse_feats
        self._current_feature_mapping = updated_fm

    def _build_graph_from_grids(self):
        """Build graph from grids by taking the top k voxels with content.

        Returns:
            Graph with locations and features at the top k voxels with content.
        """
        top_voxel_idxs = self._get_top_k_voxel_indices()

        locations_at_ids = self._location_grid.to_dense()[
            top_voxel_idxs[0], top_voxel_idxs[1], top_voxel_idxs[2]
        ]
        features_at_ids = self._feature_grid.to_dense()[
            top_voxel_idxs[0], top_voxel_idxs[1], top_voxel_idxs[2]
        ]
        graph = build_point_cloud_graph(
            locations=np.array(locations_at_ids),
            features=np.array(features_at_ids),
            feature_mapping=self._current_feature_mapping,
        )
        # TODO: remove eventually and do search directly in grid?
        self._location_tree = KDTree(
            graph.pos,
            leafsize=40,
        )
        return graph

    # ------------------------ Helper --------------------------
    def _extract_feature_array(self, feature_dict):
        """Turns the dict of features into an array + feature mapping.

        For efficient calculations all features are stored in a single array. To
        retain information about where a specific feature is stored we have the
        feature_mapping dictionary which stores the indices of where each feature is
        stored. This function extracts all features in feature_dict and stacks them in
        a matrix. The returned feature_mapping tells where in the array each feature
        is stored.

        Note:
            Once this class goes out of the experimental stage and becomes more the
            default, the buffer get_all_features_on_object function could directly
            return this format instead of having to convert twice.

        Returns:
            feature_array: Array of features.
            feature_mapping: Dictionary with feature names as keys and their
                corresponding indices in feature_array as values.
        """
        feature_mapping = dict()
        feature_array = None
        for feature in feature_dict.keys():
            feats = feature_dict[feature]

            if len(feats.shape) == 1:
                feats = feats.reshape((feats.shape[0], 1))

            if feature_array is None:
                feature_array = feats
                prev_feat_shape = 0
            else:
                prev_feat_shape = feature_array.shape[1]
                feature_array = np.column_stack((feature_array, feats))

            feature_mapping[feature] = [
                prev_feat_shape,
                prev_feat_shape + feats.shape[1],
            ]
        return feature_array, feature_mapping

    def _generate_empty_grid(self, num_voxels, n_entries):
        # NOTE: torch sparse is made for 2D tensors. We use it for 4D tensors.
        # Some operations may not work as expected on these.
        shape = (num_voxels, num_voxels, num_voxels, n_entries)
        # Create empty sparse tensor
        sparse_tensor = torch.sparse_coo_tensor(
            torch.zeros((4, 0), dtype=torch.long), torch.tensor([]), size=shape
        )
        return sparse_tensor.coalesce()

    def _get_new_voxel_location(
        self, new_locations_in_voxel, previous_loc_in_voxel, voxel
    ):
        """Calculate new average location for a voxel.

        Returns:
            New average location for a voxel.
        """
        avg_loc = np.mean(new_locations_in_voxel, axis=0)
        # Only average with previous location if there was one stored there before.
        # since self._observation_count already includes the new observations it needs
        # to be > the number of new observations in the voxel.
        if self._observation_count[voxel[0], voxel[1], voxel[2], 0] > len(
            new_locations_in_voxel
        ):
            # NOTE: could weight these
            avg_loc = (avg_loc + previous_loc_in_voxel) / 2
        return avg_loc

    def _get_new_voxel_features(
        self,
        new_features_in_voxel,
        previous_feat_in_voxel,
        voxel,
        obs_fm,
        target_fm,
        target_feat_dim,
    ):
        """Calculate new average features for a voxel.

        Returns:
            New average features for a voxel.
        """
        new_feature_avg = np.zeros(target_feat_dim)
        if ("pose_vectors" in obs_fm.keys()) and (
            "pose_fully_defined" in obs_fm.keys()
        ):
            # TODO: deal with case where not all of those keys are present
            pv_ids = obs_fm["pose_vectors"]
            pdefined_ids = obs_fm["pose_fully_defined"]
            pose_vecs = new_features_in_voxel[:, pv_ids[0] : pv_ids[1]]
            pdefined = new_features_in_voxel[:, pdefined_ids[0] : pdefined_ids[1]]
            pv_mean, use_cds_to_update = pose_vector_mean(pose_vecs, pdefined)
        for feature in obs_fm:
            ids = obs_fm[feature]
            feats = new_features_in_voxel[:, ids[0] : ids[1]]
            if feature == "hsv":
                avg_feat = np.zeros(3)
                avg_feat[0] = circular_mean(feats[:, 0])
                avg_feat[1:] = np.mean(feats[:, 1:], axis=0)
            elif feature == "pose_vectors":
                avg_feat = pv_mean
            elif feature in ["on_object", "pose_fully_defined"]:
                avg_feat = get_most_common_bool(feats)
                # NOTE: object_id may need its own most common function until
                # IDs actually represent similarities
            else:
                avg_feat = np.mean(feats, axis=0)
            # Only take average if there was a feature stored here before.
            # since self._observation_count already includes the new obs
            # this needs to be > the number of new feature obs in the voxel.
            if self._observation_count[voxel[0], voxel[1], voxel[2], 0] > len(feats):
                old_ids = self.feature_mapping[feature]
                previous_average = previous_feat_in_voxel[old_ids[0] : old_ids[1],]

                if feature == "pose_vectors":
                    if avg_feat is None:
                        avg_feat = previous_average
                    elif use_cds_to_update is False:
                        avg_feat[3:] = previous_average[3:]
                # NOTE: could weight these
                avg_feat = (avg_feat + previous_average) / 2
            target_ids = target_fm[feature]
            new_feature_avg[target_ids[0] : target_ids[1]] = avg_feat
        return new_feature_avg

    def _update_feature_mapping(self, new_fm):
        """Update feature_mapping dict with potentially new features.

        Returns:
            updated_fm: Updated feature_mapping dictionary.
            new_feature_dim: Dimension of the new features.
        """
        updated_fm = copy.deepcopy(self.feature_mapping)
        new_feature_dim = self.x.shape[-1]
        for feature in new_fm:
            if feature not in updated_fm:
                start_idx = new_feature_dim
                new_feature_idxs = new_fm[feature]
                feat_size = new_feature_idxs[1] - new_feature_idxs[0]
                stop_idx = new_feature_dim + feat_size
                updated_fm[feature] = [start_idx, stop_idx]
                new_feature_dim = stop_idx
        return updated_fm, new_feature_dim

    def _get_top_k_voxel_indices(self):
        """Get voxel indices with k highest observation counts.

        Note:
            May return less than k (self._max_nodes) voxels if there are
            less than k voxels with content.

        Returns:
            Indices of the top k voxels with content.
        """
        num_non_zero_voxels = len(self._observation_count.values())
        if num_non_zero_voxels < self._max_nodes:
            print("There are less than max_nodes voxels with content.")
            k = num_non_zero_voxels
        else:
            k = self._max_nodes
        _counts, top_k_indices = self._observation_count.values().topk(k)
        indices_3d = self._observation_count.indices()[:3, top_k_indices]
        return indices_3d

    def _locations_to_grid_ids(self, locations):
        """Convert locations to grid ids using scale_factor and location_offset.

        Returns:
            Grid ids for the locations.
        """
        location_grid_ids = np.array(
            np.round(locations * self._location_scale_factor) + self._location_offset,
            dtype=int,
        )
        return location_grid_ids

    def _old_new_lists_to_sparse_tensors(
        self, indices, new_values, old_values, target_mat_shape
    ):
        """Turn two lists of old and new values into sparse tensors.

        Args:
            indices: list of indices of the form [x, y, z] (Will be expanded
                to 4d for sparse tensor)
            new_values: list of new values to be put into sparse tensor
            old_values: list of old values to be put into another sparse tensor
            target_mat_shape: shape of the two sparse tensors

        Returns:
            old_sparse_mat: Sparse tensor with old values.
            new_sparse_mat: Sparse tensor with new values.
        """
        indices_4d = expand_index_dims(indices, last_dim_size=target_mat_shape[-1])
        new_sparse_mat = torch.sparse_coo_tensor(
            indices_4d,
            np.array(new_values).flatten(),
            target_mat_shape,
        ).coalesce()
        old_sparse_mat = torch.sparse_coo_tensor(
            indices_4d,
            np.array(old_values).flatten(),
            target_mat_shape,
        ).coalesce()
        return old_sparse_mat, new_sparse_mat

    # ----------------------- Logging --------------------------
