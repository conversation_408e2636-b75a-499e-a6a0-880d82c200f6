name: Tools

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.run_id }}
  cancel-in-progress: true

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:

  test_tools:
    name: test-tools
    runs-on: ubuntu-latest
    if: ${{ github.repository_owner == 'thousandbrainsproject' }}
    steps:
      - name: Checkout tbp.monty
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
          path: tbp.monty
      - name: Filter by path for a pull request
        if: ${{ github.event_name == 'pull_request' }}
        id: filter_by_path_pr
        working-directory: tbp.monty
        run: |
          git diff --name-only origin/${{ github.base_ref }} > changed_files.txt
          while IFS= read -r changed_file
          do
            echo $changed_file
            if [[ $changed_file == tools/* ]] ||
               [[ $changed_file == pyproject.toml ]] ||
               [[ $changed_file == .github/workflows/tools.yml ]]; then
              echo "run_test_tools=true" >> $GITHUB_OUTPUT
              exit 0
            fi
          done < changed_files.txt
      - name: Filter by path for a push
        if: ${{ github.event_name == 'push' }}
        id: filter_by_path_push
        working-directory: tbp.monty
        run: |
          git diff --name-only ${{ github.sha }}^1 > changed_files.txt
          while IFS= read -r changed_file
          do
            echo $changed_file
            if [[ $changed_file == tools/* ]] ||
               [[ $changed_file == pyproject.toml ]] ||
               [[ $changed_file == .github/workflows/tools.yml ]]; then
              echo "run_test_tools=true" >> $GITHUB_OUTPUT
              exit 0
            fi
          done < changed_files.txt
      - name: Should run
        id: should_run
        if: ${{ steps.filter_by_path_pr.outputs.run_test_tools == 'true' || steps.filter_by_path_push.outputs.run_test_tools == 'true'}}
        run: echo "run_test_tools=true" >> $GITHUB_OUTPUT
      - name: Set up ~/tbp
        if: ${{ steps.should_run.outputs.run_test_tools == 'true' }}
        run: |
          mkdir -p ~/tbp
          ln -s $GITHUB_WORKSPACE/tbp.monty ~/tbp/tbp.monty
      - name: Set up Python 3.8
        if: ${{ steps.should_run.outputs.run_test_tools == 'true' }}
        uses: actions/setup-python@v5
        with:
          python-version: "3.8"
      - name: Install miniconda
        if: ${{ steps.should_run.outputs.run_test_tools == 'true' }}
        run: |
          if [ ! -d ~/miniconda ]
          then
            wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O ~/miniconda.sh
            bash ~/miniconda.sh -b -p ~/miniconda
            rm ~/miniconda.sh
          fi
          export PATH="$HOME/miniconda/bin:$PATH"
          conda --version
      - name: Create conda environment
        if: ${{ steps.should_run.outputs.run_test_tools == 'true' }}
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          (conda env list | grep tbp.monty) && conda remove --name tbp.monty --all --yes || true
          conda env create
          source activate tbp.monty
          pip install -e .[dev,github_readme_sync_tool,print_version_tool,generate_api_docs_tool]
          pip list
          conda list
      - name: Check style
        if: ${{ steps.should_run.outputs.run_test_tools == 'true' }}
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          source activate tbp.monty
          ruff check tools
      - name: Check types
        if: ${{ steps.should_run.outputs.run_test_tools == 'true' }}
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          source activate tbp.monty
          mypy --warn-unused-ignores tools
      - name: Check dependencies
        if: ${{ steps.should_run.outputs.run_test_tools == 'true' }}
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          source activate tbp.monty
          deptry src tools
      - name: Run tests
        if: ${{ steps.should_run.outputs.run_test_tools == 'true' }}
        working-directory: tbp.monty
        run: |
          export PATH="$HOME/miniconda/bin:$PATH"
          source activate tbp.monty
          pytest tools
