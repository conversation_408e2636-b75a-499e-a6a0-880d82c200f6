Metadata-Version: 2.1
Name: tbp.monty
Version: 0.3.0
Summary: Thousand Brains Project Monty
Author: Thousand Brains Project
License: Copyright 2025 Thousand Brains Project
        Copyright 2021-2024 Numenta Inc.
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
        
Project-URL: Homepage, https://thousandbrainsproject.org
Project-URL: Documentation, https://thousandbrainsproject.readme.io/docs
Project-URL: Repository, https://github.com/thousandbrainsproject.tbp.monty
Project-URL: Issues, https://github.com/thousandbrainsproject/tbp.monty/issues
Classifier: Development Status :: 3 - Alpha
Classifier: Environment :: Console
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: habitat_sim
Requires-Dist: importlib_resources
Requires-Dist: matplotlib
Requires-Dist: numpy
Requires-Dist: pandas
Requires-Dist: pillow
Requires-Dist: scikit-image
Requires-Dist: scikit-learn==1.3.2
Requires-Dist: scipy
Requires-Dist: sympy
Requires-Dist: torch
Requires-Dist: torchvision
Requires-Dist: torch-geometric
Requires-Dist: tqdm
Requires-Dist: wandb
Provides-Extra: analysis
Requires-Dist: ipython; extra == "analysis"
Requires-Dist: seaborn; extra == "analysis"
Provides-Extra: build
Requires-Dist: build; extra == "build"
Provides-Extra: dev
Requires-Dist: deptry; extra == "dev"
Requires-Dist: mypy==1.11.2; extra == "dev"
Requires-Dist: pytest==7.1.1; extra == "dev"
Requires-Dist: pytest-xdist==2.5.0; extra == "dev"
Requires-Dist: pytest-cov==3.0.0; extra == "dev"
Requires-Dist: ruff==0.11.4; extra == "dev"
Provides-Extra: generate-api-docs-tool
Requires-Dist: docutils>=0.17; extra == "generate-api-docs-tool"
Requires-Dist: sphinx; extra == "generate-api-docs-tool"
Requires-Dist: sphinx-autobuild; extra == "generate-api-docs-tool"
Requires-Dist: myst-parser; extra == "generate-api-docs-tool"
Requires-Dist: pydata_sphinx_theme; extra == "generate-api-docs-tool"
Provides-Extra: github-readme-sync-tool
Requires-Dist: requests; extra == "github-readme-sync-tool"
Requires-Dist: pyyaml; extra == "github-readme-sync-tool"
Requires-Dist: python-dotenv; extra == "github-readme-sync-tool"
Requires-Dist: colorama; extra == "github-readme-sync-tool"
Requires-Dist: markdown2; extra == "github-readme-sync-tool"
Requires-Dist: python-slugify; extra == "github-readme-sync-tool"
Requires-Dist: nh3; extra == "github-readme-sync-tool"
Provides-Extra: print-version-tool
Requires-Dist: semver; extra == "print-version-tool"
Provides-Extra: real-robots
Requires-Dist: gym; extra == "real-robots"
Requires-Dist: opencv-python; extra == "real-robots"
Requires-Dist: real_robots; extra == "real-robots"

![](docs/figures/overview/logo.png)

# Welcome to the Monty Repository!

*An open-source, sensorimotor learning system following the principles of the neocortex.*

[![](https://github.com/thousandbrainsproject/tbp.monty/actions/workflows/monty.yml/badge.svg)](https://github.com/thousandbrainsproject/tbp.monty/actions/workflows/monty.yml)

This repository contains the first implementation of a **sensorimotor learning system** from the **Thousand Brains Project at Numenta**. We lovingly call it **Monty** after Vernon Mountcastle, who proposed cortical columns as a repeating functional unit across the neocortex.

This is an open-source project by Numenta, partially funded by the Gates Foundation.

Please find our [**full documentation** here](https://thousandbrainsproject.readme.io/)

Our [**API documentation** here](https://api-monty.thousandbrains.org).

# Getting Started

You can find detailed instructions on how to install the project requirements and how to get started [here](https://thousandbrainsproject.readme.io/docs/getting-started)

# Current Performance
We regularly evaluate this system against a set of sensorimotor tasks, summarized in the **[benchmark experiments](./benchmarks/configs/)**. Any time a functional change is made to the code, these experiments are rerun, and results are updated.

You can find our current performance on these benchmarks as well as an explanation of them [here](https://thousandbrainsproject.readme.io/docs/benchmark-experiments).


# Contributing

Are you interested in contributing? Check out our tips and guidelines [here](https://thousandbrainsproject.readme.io/docs/contributing).

Before contributing, please sign our Contributor License Agreement (CLA). You can find the CLA and guidelines [here]( https://thousandbrainsproject.readme.io/docs/contributor-license-agreement).

# Disclaimer
This is not production-ready code. It is an **early beta version** that is under active development. This early beta version is functional but evolving. Expect frequent changes as we develop core features.

You can find a list of the systems **current capabilities and application criteria** [here](https://thousandbrainsproject.readme.io/docs/application-criteria).

You can find our **project road map** and details on the next features we are working on [here](https://thousandbrainsproject.readme.io/docs/project-roadmap).

# More Information and Updates
As mentioned above, we have extensive **documentation** of this project [here](https://thousandbrainsproject.readme.io/).

[![](docs/figures/overview/docs_screenshot.png)](https://thousandbrainsproject.readme.io/)

We also publish our meeting recordings on **YouTube** on the [Thousand Brains Project channel](https://www.youtube.com/@thousandbrainsproject).

[![](docs/figures/overview/youtube_screenshot.png)](https://www.youtube.com/@thousandbrainsproject)

If you want to use this code, contribute to it, ask questions or propose ideas, please consider joining [our discourse channel](https://thousandbrains.discourse.group/).

[![](docs/figures/overview/discourse_screenshot.png)](https://thousandbrains.discourse.group/)

If you would like to receive updates, follow us on [Bluesky](https://bsky.app/profile/1000brainsproj.bsky.social) or [Twitter](https://x.com/1000brainsproj) or [LinkedIn](https://www.linkedin.com/company/thousand-brains-project/).

If you have further questions or suggestions for collaborations, don't hesitate to contact us directly at **<EMAIL>**.

# Citing the Project
If you're writing a publication that references the Thousand Brains Project, please cite our TBP white paper:
```
@misc{thousandbrainsproject2024,
      title={The Thousand Brains Project: A New Paradigm for Sensorimotor Intelligence},
      author={Viviane Clay and Niels Leadholm and Jeff Hawkins},
      year={2024},
      eprint={2412.18354},
      archivePrefix={arXiv},
      primaryClass={cs.AI},
      url={https://arxiv.org/abs/2412.18354},
}
```

# License

The MIT License. See the [LICENSE](LICENSE) for details.
