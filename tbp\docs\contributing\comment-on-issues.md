---
title: Comment on Issues
---
We value your feedback and insights. If you have suggestions, ideas, or questions, feel free to comment on existing issues. Engaging in discussions helps shape <PERSON>'s direction and ensures that the community's needs and concerns are considered.

Please consider the following when commenting.

# People

Remember the people involved in the Pull Request. Be welcoming. If there is a conflict or coming to an agreement is difficult, having an audio or video call can help. Remember to always be polite and that everyone is just trying to help in their own way.

# Understanding

When commenting on issues, please favor gaining an understanding of what is being communicated. What is the other person's context? 

# Bug Reports

We encourage everyone to reproduce Bug Reports. If you generate a reproduction, please comment on the Issue with your reproduction to verify the problem.

# Feature Requests

If you begin working on a Feature Request, it is helpful to let people know. While Feature Requests are not assigned to specific people, in some cases, it may be beneficial to discuss them with others who are also working on them.