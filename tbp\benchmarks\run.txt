(base) jiang<PERSON><PERSON>@jiangzixideMacBook-Air tbp.monty-main %
(base) jiang<PERSON><PERSON>@jiangzixideMacBook-Air tbp.monty-main % softwareupdate --install-rosetta
conda env create -f environment_arm64.yml --subdir=osx-64
conda init zsh
conda activate tbp.monty
conda config --env --set subdir osx-64
I have read and agree to the terms of the software license agreement. A list of Apple SLAs may be found here: https://www.apple.com/legal/sla/
Type A and press return to agree: A
2025-04-22 07:57:40.308 softwareupdate[3783:58771] Package Authoring Error: 062-58681: Package reference com.apple.pkg.RosettaUpdateAuto is missing installKBytes attribute
Install of Rosetta 2 finished successfully
/opt/anaconda3/lib/python3.12/argparse.py:2006: FutureWarning: `remote_definition` is deprecated and will be removed in 25.9. Use `conda env create --file=URL` instead.
  action(self, namespace, argument_values, option_string)
Channels:
 - aihabitat
 - pytorch
 - pyg
 - conda-forge
 - defaults
Platform: osx-64
Collecting package metadata (repodata.json): done
Solving environment: done

Downloading and Extracting Packages:

Preparing transaction: done
Verifying transaction: done
Executing transaction: done
Installing pip dependencies: \ Ran pip subprocess with arguments:
['/opt/anaconda3/envs/tbp.monty/bin/python', '-m', 'pip', 'install', '-U', '-r', '/Users/<USER>/Downloads/tbp.monty-main/condaenv.2uuu5qms.requirements.txt', '--exists-action=b']
Pip subprocess output:
Obtaining file:///Users/<USER>/Downloads/tbp.monty-main
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Checking if build backend supports build_editable: started
  Checking if build backend supports build_editable: finished with status 'done'
  Getting requirements to build editable: started
  Getting requirements to build editable: finished with status 'done'
  Preparing editable metadata (pyproject.toml): started
  Preparing editable metadata (pyproject.toml): finished with status 'done'
Requirement already satisfied: habitat-sim in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (0.2.2)
Requirement already satisfied: importlib-resources in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (6.4.5)
Requirement already satisfied: matplotlib in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (3.7.3)
Requirement already satisfied: numpy in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (1.23.5)
Requirement already satisfied: pandas in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (2.0.3)
Requirement already satisfied: pillow in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (9.4.0)
Requirement already satisfied: scikit-image in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (0.21.0)
Requirement already satisfied: scikit-learn==1.3.2 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (1.3.2)
Requirement already satisfied: scipy in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (1.10.1)
Requirement already satisfied: sympy in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (1.13.3)
Requirement already satisfied: torch in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (1.11.0)
Requirement already satisfied: torchvision in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (0.12.0)
Requirement already satisfied: torch-geometric in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (2.1.0.post1)
Requirement already satisfied: tqdm in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (4.67.1)
Requirement already satisfied: wandb in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from tbp.monty==0.3.0) (0.16.6)
Requirement already satisfied: joblib>=1.1.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from scikit-learn==1.3.2->tbp.monty==0.3.0) (1.4.2)
Requirement already satisfied: threadpoolctl>=2.0.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from scikit-learn==1.3.2->tbp.monty==0.3.0) (3.5.0)
Collecting deptry (from tbp.monty==0.3.0)
  Downloading deptry-0.20.0-cp38-abi3-macosx_10_12_x86_64.whl.metadata (4.7 kB)
Collecting mypy==1.11.2 (from tbp.monty==0.3.0)
  Downloading mypy-1.11.2-cp38-cp38-macosx_10_9_x86_64.whl.metadata (1.9 kB)
Collecting pytest==7.1.1 (from tbp.monty==0.3.0)
  Downloading pytest-7.1.1-py3-none-any.whl.metadata (7.8 kB)
Collecting pytest-xdist==2.5.0 (from tbp.monty==0.3.0)
  Downloading pytest_xdist-2.5.0-py3-none-any.whl.metadata (21 kB)
Collecting pytest-cov==3.0.0 (from tbp.monty==0.3.0)
  Downloading pytest_cov-3.0.0-py3-none-any.whl.metadata (24 kB)
Collecting ruff==0.11.4 (from tbp.monty==0.3.0)
  Downloading ruff-0.11.4-py3-none-macosx_10_12_x86_64.whl.metadata (25 kB)
Requirement already satisfied: typing-extensions>=4.6.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from mypy==1.11.2->tbp.monty==0.3.0) (4.12.2)
Collecting mypy-extensions>=1.0.0 (from mypy==1.11.2->tbp.monty==0.3.0)
  Downloading mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)
Collecting tomli>=1.1.0 (from mypy==1.11.2->tbp.monty==0.3.0)
  Downloading tomli-2.2.1-py3-none-any.whl.metadata (10 kB)
Requirement already satisfied: attrs>=19.2.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from pytest==7.1.1->tbp.monty==0.3.0) (24.2.0)
Collecting iniconfig (from pytest==7.1.1->tbp.monty==0.3.0)
  Downloading iniconfig-2.1.0-py3-none-any.whl.metadata (2.7 kB)
Requirement already satisfied: packaging in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from pytest==7.1.1->tbp.monty==0.3.0) (25.0)
Collecting pluggy<2.0,>=0.12 (from pytest==7.1.1->tbp.monty==0.3.0)
  Downloading pluggy-1.5.0-py3-none-any.whl.metadata (4.8 kB)
Collecting py>=1.8.2 (from pytest==7.1.1->tbp.monty==0.3.0)
  Downloading py-1.11.0-py2.py3-none-any.whl.metadata (2.8 kB)
Collecting coverage>=5.2.1 (from coverage[toml]>=5.2.1->pytest-cov==3.0.0->tbp.monty==0.3.0)
  Downloading coverage-7.6.1-cp38-cp38-macosx_10_9_x86_64.whl.metadata (8.3 kB)
Collecting execnet>=1.1 (from pytest-xdist==2.5.0->tbp.monty==0.3.0)
  Downloading execnet-2.1.1-py3-none-any.whl.metadata (2.9 kB)
Collecting pytest-forked (from pytest-xdist==2.5.0->tbp.monty==0.3.0)
  Downloading pytest_forked-1.6.0-py3-none-any.whl.metadata (3.5 kB)
Requirement already satisfied: click<9,>=8.0.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from deptry->tbp.monty==0.3.0) (8.1.7)
Requirement already satisfied: gitpython in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from habitat-sim->tbp.monty==0.3.0) (3.1.43)
Requirement already satisfied: imageio in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from habitat-sim->tbp.monty==0.3.0) (2.36.0)
Requirement already satisfied: imageio-ffmpeg in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from habitat-sim->tbp.monty==0.3.0) (0.5.1)
Requirement already satisfied: numba in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from habitat-sim->tbp.monty==0.3.0) (0.58.1)
Requirement already satisfied: numpy-quaternion in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from habitat-sim->tbp.monty==0.3.0) (2023.0.3)
Requirement already satisfied: zipp>=3.1.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from importlib-resources->tbp.monty==0.3.0) (3.21.0)
Requirement already satisfied: contourpy>=1.0.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from matplotlib->tbp.monty==0.3.0) (1.1.1)
Requirement already satisfied: cycler>=0.10 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from matplotlib->tbp.monty==0.3.0) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from matplotlib->tbp.monty==0.3.0) (4.53.1)
Requirement already satisfied: kiwisolver>=1.0.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from matplotlib->tbp.monty==0.3.0) (1.4.5)
Requirement already satisfied: pyparsing>=2.3.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from matplotlib->tbp.monty==0.3.0) (3.1.4)
Requirement already satisfied: python-dateutil>=2.7 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from matplotlib->tbp.monty==0.3.0) (2.9.0)
Requirement already satisfied: pytz>=2020.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from pandas->tbp.monty==0.3.0) (2024.2)
Requirement already satisfied: tzdata>=2022.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from pandas->tbp.monty==0.3.0) (2024.2)
Requirement already satisfied: networkx>=2.8 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from scikit-image->tbp.monty==0.3.0) (3.1)
Requirement already satisfied: tifffile>=2022.8.12 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from scikit-image->tbp.monty==0.3.0) (2023.7.10)
Requirement already satisfied: PyWavelets>=1.1.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from scikit-image->tbp.monty==0.3.0) (1.4.1)
Requirement already satisfied: lazy_loader>=0.2 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from scikit-image->tbp.monty==0.3.0) (0.4)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from sympy->tbp.monty==0.3.0) (1.3.0)
Requirement already satisfied: jinja2 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from torch-geometric->tbp.monty==0.3.0) (3.1.4)
Requirement already satisfied: requests in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from torch-geometric->tbp.monty==0.3.0) (2.32.3)
Requirement already satisfied: psutil>=5.0.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from wandb->tbp.monty==0.3.0) (6.0.0)
Requirement already satisfied: sentry-sdk>=1.0.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from wandb->tbp.monty==0.3.0) (2.19.2)
Requirement already satisfied: docker-pycreds>=0.4.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from wandb->tbp.monty==0.3.0) (0.4.0)
Requirement already satisfied: PyYAML in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from wandb->tbp.monty==0.3.0) (6.0.2)
Requirement already satisfied: setproctitle in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from wandb->tbp.monty==0.3.0) (1.3.3)
Requirement already satisfied: setuptools in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from wandb->tbp.monty==0.3.0) (75.3.0)
Requirement already satisfied: appdirs>=1.4.3 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from wandb->tbp.monty==0.3.0) (1.4.4)
Requirement already satisfied: protobuf!=4.21.0,<5,>=3.19.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from wandb->tbp.monty==0.3.0) (4.25.3)
Requirement already satisfied: six>=1.4.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from docker-pycreds>=0.4.0->wandb->tbp.monty==0.3.0) (1.16.0)
Requirement already satisfied: gitdb<5,>=4.0.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from gitpython->habitat-sim->tbp.monty==0.3.0) (4.0.11)
Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from requests->torch-geometric->tbp.monty==0.3.0) (3.4.0)
Requirement already satisfied: idna<4,>=2.5 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from requests->torch-geometric->tbp.monty==0.3.0) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from requests->torch-geometric->tbp.monty==0.3.0) (2.2.3)
Requirement already satisfied: certifi>=2017.4.17 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from requests->torch-geometric->tbp.monty==0.3.0) (2024.8.30)
Requirement already satisfied: MarkupSafe>=2.0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from jinja2->torch-geometric->tbp.monty==0.3.0) (2.1.5)
Requirement already satisfied: llvmlite<0.42,>=0.41.0dev0 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from numba->habitat-sim->tbp.monty==0.3.0) (0.41.1)
Requirement already satisfied: importlib-metadata in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from numba->habitat-sim->tbp.monty==0.3.0) (8.5.0)
Requirement already satisfied: smmap<6,>=3.0.1 in /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages (from gitdb<5,>=4.0.1->gitpython->habitat-sim->tbp.monty==0.3.0) (3.0.5)
Downloading mypy-1.11.2-cp38-cp38-macosx_10_9_x86_64.whl (10.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.9/10.9 MB 7.5 MB/s eta 0:00:00
Downloading pytest-7.1.1-py3-none-any.whl (297 kB)
Downloading pytest_cov-3.0.0-py3-none-any.whl (20 kB)
Downloading pytest_xdist-2.5.0-py3-none-any.whl (41 kB)
Downloading ruff-0.11.4-py3-none-macosx_10_12_x86_64.whl (10.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.9/10.9 MB 13.4 MB/s eta 0:00:00
Downloading deptry-0.20.0-cp38-abi3-macosx_10_12_x86_64.whl (1.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.6/1.6 MB 18.9 MB/s eta 0:00:00
Downloading coverage-7.6.1-cp38-cp38-macosx_10_9_x86_64.whl (206 kB)
Downloading execnet-2.1.1-py3-none-any.whl (40 kB)
Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)
Downloading pluggy-1.5.0-py3-none-any.whl (20 kB)
Downloading py-1.11.0-py2.py3-none-any.whl (98 kB)
Downloading tomli-2.2.1-py3-none-any.whl (14 kB)
Downloading iniconfig-2.1.0-py3-none-any.whl (6.0 kB)
Downloading pytest_forked-1.6.0-py3-none-any.whl (4.9 kB)
Building wheels for collected packages: tbp.monty
  Building editable for tbp.monty (pyproject.toml): started
  Building editable for tbp.monty (pyproject.toml): finished with status 'done'
  Created wheel for tbp.monty: filename=tbp_monty-0.3.0-0.editable-py3-none-any.whl size=5009 sha256=cf4a357fd2a06e7772a3477b1f7ba9874e8a3034221d9991fbabb368a6b68cc4
  Stored in directory: /private/var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/pip-ephem-wheel-cache-74l3aj99/wheels/47/d4/e4/1f06f68a0e4c37234f5a3771d9fcc0c301ac0d76968d994973
Successfully built tbp.monty
Installing collected packages: tomli, ruff, py, pluggy, mypy-extensions, iniconfig, execnet, coverage, pytest, mypy, deptry, pytest-forked, pytest-cov, tbp.monty, pytest-xdist
Successfully installed coverage-7.6.1 deptry-0.20.0 execnet-2.1.1 iniconfig-2.1.0 mypy-1.11.2 mypy-extensions-1.0.0 pluggy-1.5.0 py-1.11.0 pytest-7.1.1 pytest-cov-3.0.0 pytest-forked-1.6.0 pytest-xdist-2.5.0 ruff-0.11.4 tbp.monty-0.3.0 tomli-2.2.1

done
#
# To activate this environment, use
#
#     $ conda activate tbp.monty
#
# To deactivate an active environment, use
#
#     $ conda deactivate

no change     /opt/anaconda3/condabin/conda
no change     /opt/anaconda3/bin/conda
no change     /opt/anaconda3/bin/conda-env
no change     /opt/anaconda3/bin/activate
no change     /opt/anaconda3/bin/deactivate
no change     /opt/anaconda3/etc/profile.d/conda.sh
no change     /opt/anaconda3/etc/fish/conf.d/conda.fish
no change     /opt/anaconda3/shell/condabin/Conda.psm1
no change     /opt/anaconda3/shell/condabin/conda-hook.ps1
no change     /opt/anaconda3/lib/python3.12/site-packages/xontrib/conda.xsh
no change     /opt/anaconda3/etc/profile.d/conda.csh
no change     /Users/<USER>/.zshrc
No action taken.
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp.monty-main % conda env list
# conda environments:
#
base                     /opt/anaconda3
tbp.monty             *  /opt/anaconda3/envs/tbp.monty

(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp.monty-main % conda activate tbp.monty
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp.monty-main % pytest
============================================================== test session starts ===============================================================
platform darwin -- Python 3.8.20, pytest-7.1.1, pluggy-1.5.0
rootdir: /Users/<USER>/Downloads/tbp.monty-main, configfile: pyproject.toml, testpaths: tests/unit
plugins: xdist-2.5.0, forked-1.6.0, cov-3.0.0
gw0 [357] / gw1 [357] / gw2 [357] / gw3 [357] / gw4 [357] / gw5 [357] / gw6 [357] / gw7 [357]
.......................................................................................................................................... [ 38%]
.......................................................................................................................................... [ 77%]
.........................................F.......................................                                                             [100%]
===================================================================== FAILURES ======================================================================
___________________________________________________ MontyRunTest.test_importing_existing_configs ____________________________________________________
[gw5] darwin -- Python 3.8.20 /opt/anaconda3/envs/tbp.monty/bin/python

self = <tests.unit.run_test.MontyRunTest testMethod=test_importing_existing_configs>

    def test_importing_existing_configs(self):
        """Test that any changes do not cause errors in existing configs."""
        current_file_path = pathlib.Path(__file__).parent.resolve()
>       base_repo_idx = current_file_path.parts.index("tbp.monty")
E       ValueError: tuple.index(x): x not in tuple

tests/unit/run_test.py:203: ValueError
================================================================= warnings summary ==================================================================
tests/unit/evidence_lm_test.py: 106 warnings
tests/unit/policy_test.py: 28 warnings
tests/unit/graph_learning_test.py: 94 warnings
tests/unit/run_parallel_test.py: 7 warnings
tests/unit/sensor_module_test.py: 3 warnings
  /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
    return _methods._mean(a, axis=axis, dtype=dtype,

tests/unit/evidence_lm_test.py: 106 warnings
tests/unit/policy_test.py: 28 warnings
tests/unit/graph_learning_test.py: 94 warnings
tests/unit/run_parallel_test.py: 7 warnings
tests/unit/sensor_module_test.py: 3 warnings
  /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
    ret = ret.dtype.type(ret / rcount)

tests/unit/graph_building_test.py::GraphLearningTest::test_can_build_graph_habitat_supervised
tests/unit/graph_building_test.py::GraphLearningTest::test_can_load_disp_graph_for_ppf_matching
tests/unit/graph_building_test.py::GraphLearningTest::test_can_load_disp_graph_for_feature_matching
tests/unit/graph_building_test.py::GraphLearningTest::test_can_load_disp_graph
  /opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/threadpoolctl.py:1214: RuntimeWarning:
  Found Intel OpenMP ('libiomp') and LLVM OpenMP ('libomp') loaded at
  the same time. Both libraries are known to be incompatible and this
  can cause random crashes or deadlocks on Linux when loaded in the
  same Python program.
  Using threadpoolctl may cause crashes or deadlocks. For more
  information and possible workarounds, please see
      https://github.com/joblib/threadpoolctl/blob/master/multiple_openmp.md

    warnings.warn(msg, RuntimeWarning)

tests/unit/evidence_lm_test.py::EvidenceLMTest::test_uniform_initial_poses
tests/unit/evidence_lm_test.py::EvidenceLMTest::test_uniform_initial_poses
tests/unit/evidence_lm_test.py::EvidenceLMTest::test_uniform_initial_poses
tests/unit/evidence_lm_test.py::EvidenceLMTest::test_uniform_initial_poses
tests/unit/evidence_lm_test.py::EvidenceLMTest::test_uniform_initial_poses
tests/unit/evidence_lm_test.py::EvidenceLMTest::test_uniform_initial_poses
tests/unit/evidence_lm_test.py::EvidenceLMTest::test_uniform_initial_poses
  /Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/buffer.py:677: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
    BufferEncoder.register(Rotation, lambda obj: obj.as_euler("xyz", degrees=True))

tests/unit/evidence_sdr_lm_test.py::EvidenceSDRIntegrationTest::test_can_generate_reasonable_sdrs
  /Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/evidence_sdr_matching.py:447: RuntimeWarning: invalid value encountered in divide
    evidence[valid_ix] = (evidence[valid_ix] - input_range[0]) * (

tests/unit/graph_learning_test.py::GraphLearningTest::test_uniform_initial_poses
tests/unit/graph_learning_test.py::GraphLearningTest::test_uniform_initial_poses
tests/unit/graph_learning_test.py::GraphLearningTest::test_uniform_initial_poses
tests/unit/graph_learning_test.py::GraphLearningTest::test_uniform_initial_poses
tests/unit/graph_learning_test.py::GraphLearningTest::test_uniform_initial_poses
tests/unit/graph_learning_test.py::GraphLearningTest::test_uniform_initial_poses
  /Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/graph_matching.py:858: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
    pose.inv().as_euler("xyz", degrees=True), 5

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
============================================================== short test summary info ==============================================================
FAILED tests/unit/run_test.py::MontyRunTest::test_importing_existing_configs - ValueError: tuple.index(x): x not in tuple
============================================== 1 failed, 356 passed, 494 warnings in 653.23s (0:10:53) ==============================================
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp.monty-main % python -m habitat_sim.utils.datasets_download --uids ycb --data-path ~/tbp/data/habitat
--2025-04-22 08:13:40--  https://dl.fbaipublicfiles.com/habitat/ycb/hab_ycb_v1.2.zip
Resolving dl.fbaipublicfiles.com (dl.fbaipublicfiles.com)... ***********
Connecting to dl.fbaipublicfiles.com (dl.fbaipublicfiles.com)|***********|:443... connected.
HTTP request sent, awaiting response... 200 OK
Length: 488457421 (466M) [application/zip]
Saving to: ‘/Users/<USER>/tbp/data/habitat/hab_ycb_v1.2.zip’

hab_ycb_v1.2.zip                      100%[======================================================================>] 465.83M  10.0MB/s    in 49s

2025-04-22 08:14:30 (9.59 MB/s) - ‘/Users/<USER>/tbp/data/habitat/hab_ycb_v1.2.zip’ saved [488457421/488457421]

=======================================================
Dataset (ycb) successfully downloaded.
Source: '/Users/<USER>/tbp/data/habitat/versioned_data/ycb_1.2'
Symlink: '/Users/<USER>/tbp/data/habitat/objects/ycb'
=======================================================
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp.monty-main % mkdir -p ~/tbp/results/monty/pretrained_models/

cd ~/tbp/results/monty/pretrained_models/

curl -L https://tbp-pretrained-models-public-c9c24aef2e49b897.s3.us-east-2.amazonaws.com/tbp.monty/pretrained_ycb_v10.tgz | tar -xzf -
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed
100  194M  100  194M    0     0  10.1M      0  0:00:19  0:00:19 --:--:-- 10.6M
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % ls
pretrained_ycb_v10
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % tar -xzf pretrained_ycb_v10.tgz
tar: Error opening archive: Failed to open 'pretrained_ycb_v10.tgz'
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % ls pretrained_ycb_v10

supervised_pre_training_5lms			surf_agent_1lm_10distinctobj			surf_agent_1lm_numenta_lab_obj
supervised_pre_training_5lms_all_objects	surf_agent_1lm_10similarobj
supervised_pre_training_base			surf_agent_1lm_77obj
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % python benchmarks/run.py -e my_experiment
python: can't open file 'benchmarks/run.py': [Errno 2] No such file or directory
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % export MONTY_MODELS=/path/to/your/pretrained/models/dir
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % export MONTY_DATA=/path/to/your/data
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % export MONTY_LOGS=/path/to/log/folder
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % export WANDB_DIR=${MONTY_LOGS}/wandb
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % python benchmarks/run.py -e my_experiment
python: can't open file 'benchmarks/run.py': [Errno 2] No such file or directory
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % pwd
/Users/<USER>/tbp/results/monty/pretrained_models
(tbp.monty) jiangzixi@jiangzixideMacBook-Air pretrained_models % cd ..
(tbp.monty) jiangzixi@jiangzixideMacBook-Air monty % cd ..
(tbp.monty) jiangzixi@jiangzixideMacBook-Air results % cd ..
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp % pwd
/Users/<USER>/tbp
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp % cd benchmark
cd: no such file or directory: benchmark
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp % ls
data	results
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp % cd ..
(tbp.monty) jiangzixi@jiangzixideMacBook-Air ~ % ll
zsh: command not found: ll
(tbp.monty) jiangzixi@jiangzixideMacBook-Air ~ % ls
Applications	Documents	Library		Music		Public		tbp
Desktop		Downloads	Movies		Pictures	PycharmProjects	temp
(tbp.monty) jiangzixi@jiangzixideMacBook-Air ~ % cd Downloads
(tbp.monty) jiangzixi@jiangzixideMacBook-Air Downloads % ls
WechatIMG140.jpg			tbp.monty-main				~$系统实验测试报告模板.doc
cv.pdf					~$espond.docx
i4ToolsDownloads			~$宇亮实验1.docx
(tbp.monty) jiangzixi@jiangzixideMacBook-Air Downloads % cd tbp.monty-main
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp.monty-main % ls
CODE_OF_CONDUCT.md	MAINTAINERS.md		docs			pyproject.toml		src
CONTRIBUTING.md		README.md		environment.yml		recipe			tests
LICENSE			benchmarks		environment_arm64.yml	rfcs			tools
(tbp.monty) jiangzixi@jiangzixideMacBook-Air tbp.monty-main % cd benchmarks
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % ls
README.md				make_detailed_follow_up_configs.py	run_parallel.py
__init__.py				results
configs					run.py
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % python run.py -e my_experiment
usage: run.py [-h]
              [-e {world_image_from_stream_on_scanned_model,world_image_on_scanned_model,dark_world_image_on_scanned_model,bright_world_image_on_scanned_model,hand_intrusion_world_image_on_scanned_model,multi_object_world_image_on_scanned_model,randrot_noise_sim_on_scan_monty_world,supervised_pre_training_base,supervised_pre_training_5lms,supervised_pre_training_5lms_all_objects,only_surf_agent_training_10obj,only_surf_agent_training_10simobj,only_surf_agent_training_allobj,only_surf_agent_training_numenta_lab_obj,base_config_10distinctobj_dist_agent,base_config_10distinctobj_surf_agent,randrot_noise_10distinctobj_dist_agent,randrot_noise_10distinctobj_dist_on_distm,randrot_noise_10distinctobj_surf_agent,randrot_10distinctobj_surf_agent,randrot_noise_10distinctobj_5lms_dist_agent,base_10simobj_surf_agent,randrot_noise_10simobj_surf_agent,randrot_noise_10simobj_dist_agent,randomrot_rawnoise_10distinctobj_surf_agent,base_10multi_distinctobj_dist_agent,surf_agent_unsupervised_10distinctobj,surf_agent_unsupervised_10distinctobj_noise,surf_agent_unsupervised_10simobj,base_77obj_dist_agent,base_77obj_surf_agent,randrot_noise_77obj_surf_agent,randrot_noise_77obj_dist_agent,randrot_noise_77obj_5lms_dist_agent,unsupervised_inference_distinctobj_surf_agent,unsupervised_inference_distinctobj_dist_agent} [{world_image_from_stream_on_scanned_model,world_image_on_scanned_model,dark_world_image_on_scanned_model,bright_world_image_on_scanned_model,hand_intrusion_world_image_on_scanned_model,multi_object_world_image_on_scanned_model,randrot_noise_sim_on_scan_monty_world,supervised_pre_training_base,supervised_pre_training_5lms,supervised_pre_training_5lms_all_objects,only_surf_agent_training_10obj,only_surf_agent_training_10simobj,only_surf_agent_training_allobj,only_surf_agent_training_numenta_lab_obj,base_config_10distinctobj_dist_agent,base_config_10distinctobj_surf_agent,randrot_noise_10distinctobj_dist_agent,randrot_noise_10distinctobj_dist_on_distm,randrot_noise_10distinctobj_surf_agent,randrot_10distinctobj_surf_agent,randrot_noise_10distinctobj_5lms_dist_agent,base_10simobj_surf_agent,randrot_noise_10simobj_surf_agent,randrot_noise_10simobj_dist_agent,randomrot_rawnoise_10distinctobj_surf_agent,base_10multi_distinctobj_dist_agent,surf_agent_unsupervised_10distinctobj,surf_agent_unsupervised_10distinctobj_noise,surf_agent_unsupervised_10simobj,base_77obj_dist_agent,base_77obj_surf_agent,randrot_noise_77obj_surf_agent,randrot_noise_77obj_dist_agent,randrot_noise_77obj_5lms_dist_agent,unsupervised_inference_distinctobj_surf_agent,unsupervised_inference_distinctobj_dist_agent} ...]]
              [-q QUIET_HABITAT_LOGS] [-p]
run.py: error: argument -e/--experiments: invalid choice: 'my_experiment' (choose from 'world_image_from_stream_on_scanned_model', 'world_image_on_scanned_model', 'dark_world_image_on_scanned_model', 'bright_world_image_on_scanned_model', 'hand_intrusion_world_image_on_scanned_model', 'multi_object_world_image_on_scanned_model', 'randrot_noise_sim_on_scan_monty_world', 'supervised_pre_training_base', 'supervised_pre_training_5lms', 'supervised_pre_training_5lms_all_objects', 'only_surf_agent_training_10obj', 'only_surf_agent_training_10simobj', 'only_surf_agent_training_allobj', 'only_surf_agent_training_numenta_lab_obj', 'base_config_10distinctobj_dist_agent', 'base_config_10distinctobj_surf_agent', 'randrot_noise_10distinctobj_dist_agent', 'randrot_noise_10distinctobj_dist_on_distm', 'randrot_noise_10distinctobj_surf_agent', 'randrot_10distinctobj_surf_agent', 'randrot_noise_10distinctobj_5lms_dist_agent', 'base_10simobj_surf_agent', 'randrot_noise_10simobj_surf_agent', 'randrot_noise_10simobj_dist_agent', 'randomrot_rawnoise_10distinctobj_surf_agent', 'base_10multi_distinctobj_dist_agent', 'surf_agent_unsupervised_10distinctobj', 'surf_agent_unsupervised_10distinctobj_noise', 'surf_agent_unsupervised_10simobj', 'base_77obj_dist_agent', 'base_77obj_surf_agent', 'randrot_noise_77obj_surf_agent', 'randrot_noise_77obj_dist_agent', 'randrot_noise_77obj_5lms_dist_agent', 'unsupervised_inference_distinctobj_surf_agent', 'unsupervised_inference_distinctobj_dist_agent')
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % python run.py -e base_config_10distinctobj_surf_agent

Traceback (most recent call last):
  File "run.py", line 44, in <module>
    main(all_configs=CONFIGS, experiments=cmd_args.experiments)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/run.py", line 110, in main
    os.makedirs(exp_config["logging_config"]["output_dir"], exist_ok=True)
  File "/opt/anaconda3/envs/tbp.monty/lib/python3.8/os.py", line 213, in makedirs
    makedirs(head, exist_ok=exist_ok)
  File "/opt/anaconda3/envs/tbp.monty/lib/python3.8/os.py", line 213, in makedirs
    makedirs(head, exist_ok=exist_ok)
  File "/opt/anaconda3/envs/tbp.monty/lib/python3.8/os.py", line 213, in makedirs
    makedirs(head, exist_ok=exist_ok)
  [Previous line repeated 4 more times]
  File "/opt/anaconda3/envs/tbp.monty/lib/python3.8/os.py", line 223, in makedirs
    mkdir(name, mode)
OSError: [Errno 30] Read-only file system: '/path'
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % export MONTY_MODELS=/Users/<USER>/tbp/results/monty/pretrained_models
export MONTY_DATA=/Users/<USER>/tbp/data/habitat
export MONTY_LOGS=/Users/<USER>/tbp/logs
export WANDB_DIR=${MONTY_LOGS}/wandb

(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % python benchmarks/run.py -e base_77obj_surf_agent

python: can't open file 'benchmarks/run.py': [Errno 2] No such file or directory
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % python run.py -e base_77obj_surf_agent

Traceback (most recent call last):
  File "run.py", line 44, in <module>
    main(all_configs=CONFIGS, experiments=cmd_args.experiments)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/run.py", line 112, in main
    run(exp_config)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/run.py", line 46, in run
    with config["experiment_class"](config) as exp:
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/monty_experiment.py", line 633, in __enter__
    self.setup_experiment(self.config)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/monty_experiment.py", line 78, in setup_experiment
    self.load_dataset_and_dataloaders(config)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/monty_experiment.py", line 202, in load_dataset_and_dataloaders
    self.dataset = self.load_dataset(dataset_class, dataset_args)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/monty_experiment.py", line 245, in load_dataset
    dataset = dataset_class(**dataset_args)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/environments/embodied_data.py", line 74, in __init__
    env = env_init_func(**env_init_args)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/simulators/habitat/environment.py", line 119, in __init__
    self._env = HabitatSim(
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/simulators/habitat/simulator.py", line 172, in __init__
    raise ValueError(f"No valid habitat data found in {data_path}")
ValueError: No valid habitat data found in /Users/<USER>/tbp/data/habitat/habitat/objects/ycb
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % # Step 1: 设置环境变量
export MONTY_MODELS=~/tbp/results/monty/pretrained_models
export MONTY_DATA=~/tbp/data/habitat
export MONTY_LOGS=~/tbp/logs
export WANDB_DIR=${MONTY_LOGS}/wandb

# Step 2: 运行一个已支持的实验
cd ~/Downloads/tbp.monty-main/benchmarks
python run.py -e only_surf_agent_training_10obj

zsh: command not found: #
zsh: command not found: #
Traceback (most recent call last):
  File "run.py", line 44, in <module>
    main(all_configs=CONFIGS, experiments=cmd_args.experiments)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/run.py", line 112, in main
    run(exp_config)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/run.py", line 46, in run
    with config["experiment_class"](config) as exp:
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/monty_experiment.py", line 633, in __enter__
    self.setup_experiment(self.config)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/pretraining_experiments.py", line 43, in setup_experiment
    super().setup_experiment(config)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/monty_experiment.py", line 78, in setup_experiment
    self.load_dataset_and_dataloaders(config)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/monty_experiment.py", line 202, in load_dataset_and_dataloaders
    self.dataset = self.load_dataset(dataset_class, dataset_args)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/experiments/monty_experiment.py", line 245, in load_dataset
    dataset = dataset_class(**dataset_args)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/environments/embodied_data.py", line 74, in __init__
    env = env_init_func(**env_init_args)
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/simulators/habitat/environment.py", line 119, in __init__
    self._env = HabitatSim(
  File "/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/simulators/habitat/simulator.py", line 172, in __init__
    raise ValueError(f"No valid habitat data found in {data_path}")
ValueError: No valid habitat data found in /Users/<USER>/tbp/data/habitat/habitat/objects/ycb
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % ls /Users/<USER>/tbp/data/habitat/objects/ycb

LICENSE.txt			configs				ycb.scene_dataset_config.json
collison_meshes			meshes
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % export MONTY_DATA=/Users/<USER>/tbp/data

(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks % python run.py -e base_77obj_surf_agent

wandb: WARNING Path /Users/<USER>/tbp/logs/wandb/wandb/ wasn't writable, using system temp directory.
wandb: WARNING Path /Users/<USER>/tbp/logs/wandb/wandb/ wasn't writable, using system temp directory
wandb: WARNING Path /Users/<USER>/tbp/logs/wandb/wandb/ wasn't writable, using system temp directory
wandb: (1) Create a W&B account
wandb: (2) Use an existing W&B account
wandb: (3) Don't visualize my results
wandb: Enter your choice: 3
wandb: You chose "Don't visualize my results"
wandb: Tracking run with wandb version 0.16.6
wandb: W&B syncing is set to `offline` in this directory.
wandb: Run `wandb online` or set WANDB_MODE=online to enable cloud syncing.
---------evaluating---------
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/evidence_matching.py:629: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  r_euler = mlh["rotation"].inv().as_euler("xyz", degrees=True)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/utils/logging_utils.py:767: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  last_mlh["rotation"].inv().as_euler("xyz", degrees=True)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/evidence_matching.py:629: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  r_euler = mlh["rotation"].inv().as_euler("xyz", degrees=True)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/utils/logging_utils.py:767: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  last_mlh["rotation"].inv().as_euler("xyz", degrees=True)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/evidence_matching.py:629: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  r_euler = mlh["rotation"].inv().as_euler("xyz", degrees=True)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/utils/logging_utils.py:767: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  last_mlh["rotation"].inv().as_euler("xyz", degrees=True)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/buffer.py:578: RuntimeWarning: invalid value encountered in multiply
  new_vals = np.empty((len(self) + 1, new_val_len)) * np.nan
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
wandb:
wandb:
wandb: Run history:
wandb: LM_0/episode/individual_ts_rotation_error ▁▁▁▁▁▁▁▁▁▁▃▁▁▁▁▃█▁▁▄▂▂▂▆▁▂▂▂▂▄▅▃▁▁▅▂▁▇▁▁
wandb:       LM_0/episode/steps_to_individual_ts ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▃▁▁▁█▁▁▁▁▁▁▁▁▁▁▂▁▁
wandb:                          episode/confused ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                      episode/confused_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                           episode/correct ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                       episode/correct_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█▁▁▁▁▁▁▁▁▁█▁▁▁
wandb:           episode/goal_state_success_rate ▆▁███████▃███▆▆▇█▅███▇▆▁██▁▆▇▁█████▆▆▅█▆
wandb:             episode/goal_states_attempted ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▃▁▁▁▁▂▂▁▁▂▁▂▁█▁▁▁▁▁▂▁█▁▁▁
wandb:                          episode/lm_steps ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▂▁█▁▄▁▁▁▁▁▁▁█▁▁▁
wandb:          episode/mean_lm_steps_to_indv_ts ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▃▁▁▁▁▂▂▁▁▂▁ ▁█▁▁▁▁▁▂▁ ▁▁▁
wandb:              episode/monty_matching_steps ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▂▁█▁▄▁▁▁▁▁▁▁█▁▁▁
wandb:                       episode/monty_steps ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▂▁▆▁▄▁▁▁▁▁▁▁█▁▁▁
wandb:                          episode/no_match ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                     episode/pose_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                    episode/rotation_error ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▁▂▁▁▂▂▁▁▁█▁▁▁
wandb:                          episode/run_time ▁▁▁▁▁▁▂▁▂▁▁▁▁▁▁▂▁▂▁▁▁▂▁▁▂▁▇▁▅▁▂▁▁▁▁▂█▁▁▂
wandb:                 episode/symmetry_evidence █▁▁█▁██▁█▁▁▁██████████▁▁█▁▁▁█▁▁██▁██▁▁▁█
wandb:                          episode/time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           episode/used_mlh_after_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█▁▁▁▁▁▁▁▁▁█▁▁▁
wandb:              overall/avg_episode_run_time ▁▂▁▁▁▂▂▂▂▂▂▂▃▃▃▄▄▄▄▄▄▄▄▅▅▅▆▆▇▆▆▇▇▇▇▇▇▇██
wandb:                  overall/avg_num_lm_steps ▁▁▁▁▁▂▂▂▂▂▂▂▃▃▃▅▅▄▅▄▄▄▄▅▅▆▇▇▇▇▇▇▇▇▇▇▇▇██
wandb:      overall/avg_num_monty_matching_steps ▁▁▁▁▁▂▂▂▂▂▂▂▃▃▃▅▅▄▅▄▄▄▄▅▅▆▇▇▇▇▇▇▇▇▇▇▇▇██
wandb:               overall/avg_num_monty_steps ▁▁▁▁▁▂▂▂▂▂▂▂▅▄▄▅▅▅▅▅▅▄▄▅▅▅▆▆▆▇▇▇▇▇▇▇▇▇██
wandb:                overall/avg_rotation_error ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▂▃▃▄▄▄▄▄▄▅▅▅▅▅▆▆▆▆▆▆▇▇██
wandb:                      overall/num_episodes ▁▁▁▁▂▂▂▂▂▃▃▃▃▃▃▄▄▄▄▄▅▅▅▅▅▅▆▆▆▆▆▇▇▇▇▇▇███
wandb:                  overall/percent_confused ▁▁▁▁▁▁▁▁▁▁▁█▇▇▇▆▆▆▅▅▅▅▅▄▄▄▄▄▇▆▆▆▆▆▆▅▅▅▅▅
wandb:              overall/percent_confused_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_confused_per_lm ▁▁▁▁▁▁▁▁▁▁▁█▇▇▇▆▆▆▅▅▅▅▅▄▄▄▄▄▇▆▆▆▆▆▆▅▅▅▅▅
wandb:                   overall/percent_correct ███████████▁▂▂▂▃▃▃▄▄▄▄▄▅▅▅▅▅▂▃▃▃▃▃▃▄▄▄▄▄
wandb:               overall/percent_correct_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▅▅▅▆▆▅▅▅▅▅▄▅▅▆▇▇▆▇▇▇▆▇▇▇▇▇██
wandb:            overall/percent_correct_per_lm ███████████▁▂▂▂▃▃▃▄▄▄▄▄▅▅▅▅▅▂▃▃▃▃▃▃▄▄▄▄▄
wandb:                  overall/percent_no_match ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_no_match_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:             overall/percent_pose_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:      overall/percent_pose_time_out_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                  overall/percent_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_time_out_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:    overall/percent_used_mlh_after_timeout ▁▁▁▁▁▁▁▁▁▁▁▁▅▅▅▆▆▅▅▅▅▅▄▅▅▆▇▇▆▇▇▇▆▇▇▇▇▇██
wandb:                          overall/run_time ▁▁▁▁▁▁▂▂▂▂▂▂▂▂▃▃▃▃▃▃▄▄▄▄▄▅▅▅▅▆▆▆▆▆▇▇▇▇██
wandb:
wandb: Run summary:
wandb:                       episode/confused 0
wandb:                   episode/confused_mlh 0
wandb:                        episode/correct 1
wandb:                    episode/correct_mlh 1
wandb:        episode/goal_state_success_rate 0.55556
wandb:          episode/goal_states_attempted 9
wandb:                       episode/lm_steps 500
wandb:       episode/mean_lm_steps_to_indv_ts nan
wandb:           episode/monty_matching_steps 500
wandb:                    episode/monty_steps 2770
wandb:                       episode/no_match 0
wandb:                  episode/pose_time_out 0
wandb:                 episode/rotation_error 3.0391
wandb:                       episode/run_time 53.29429
wandb:              episode/symmetry_evidence 0.0
wandb:                       episode/time_out 0
wandb:        episode/used_mlh_after_time_out 1
wandb:           overall/avg_episode_run_time 6.48995
wandb:               overall/avg_num_lm_steps 52.28139
wandb:   overall/avg_num_monty_matching_steps 52.28139
wandb:            overall/avg_num_monty_steps 411.35065
wandb:             overall/avg_rotation_error 0.09684
wandb:                   overall/num_episodes 231
wandb:               overall/percent_confused 0.8658
wandb:           overall/percent_confused_mlh 0.0
wandb:        overall/percent_confused_per_lm 0.8658
wandb:                overall/percent_correct 99.1342
wandb:            overall/percent_correct_mlh 5.19481
wandb:         overall/percent_correct_per_lm 99.1342
wandb:               overall/percent_no_match 0.0
wandb:        overall/percent_no_match_per_lm 0.0
wandb:          overall/percent_pose_time_out 0.0
wandb:   overall/percent_pose_time_out_per_lm 0.0
wandb:               overall/percent_time_out 0.0
wandb:        overall/percent_time_out_per_lm 0.0
wandb: overall/percent_used_mlh_after_timeout 5.19481
wandb:                       overall/run_time 1499.17799
wandb:
wandb: You can sync this run to the cloud by running:
wandb: wandb sync /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250422_082621-szw51ri2
wandb: Find logs at: /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250422_082621-szw51ri2/logs
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks %