---
title: Motor System Improvements
description: Improvements we would like to add to the motor system.
---

These are the things we would like to implement:

- [Interpret goal states in motor system & switch policies](motor-system-improvements/interpret-goal-states-in-motor-system-switch-policies.md) #goalpolicy
- [Implement switching between learning and inference-focused policies](motor-system-improvements/implement-switching-between-learning-and-inference-focused-policies.md) #learning
- [Bottom-up exploration policy for surface agent](motor-system-improvements/bottom-up-exploration-policy-for-surface-agent.md) #learning
- [Model-based exploration policy](motor-system-improvements/model-based-exploration-policy.md) #learning #numsteps
- [Implement efficient saccades driven by model-free and model-based signals](motor-system-improvements/implement-efficient-saccades-driven-by-model-free-and-model-based-signals.md) #numsteps #multiobj
- [Learn policy using RL and simplified action space](motor-system-improvements/learn-policy-using-rl.md) #numsteps #speed
- [Decompose goals into subgoals & communicate](motor-system-improvements/decompose-goals-into-subgoals-communicate.md) #goalpolicy
- [Reuse hypothesis testing policy target points](motor-system-improvements/reuse-hypothesis-testing-policy-target-points.md) #goalpolicy #numsteps
- [Implement a simple cross-modal policy](motor-system-improvements/implement-a-simple-cross-modal-policy-for-sensory-guidance.md) #learning #multiobj #goalpolicy #numsteps
- [Model-based policy to recognize an object before moving onto a new object](motor-system-improvements/model-based-policy-to-recognize-an-object-before-moving-on-to-a-new-object.md) #multiobj #compositional
- [Policy to quickly move to a new object](motor-system-improvements/policy-to-quickly-move-to-a-new-object.md) #speed #multiobj #compositional

!snippet[../snippets/contributing-tasks.md]