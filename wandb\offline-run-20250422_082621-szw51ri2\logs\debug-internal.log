2025-04-22 08:26:21,178 INFO    StreamThr :6032 [internal.py:wandb_internal():86] W&B internal server running at pid: 6032, started at: 2025-04-22 08:26:21.176926
2025-04-22 08:26:21,179 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status
2025-04-22 08:26:21,180 INFO    WriterThread:6032 [datastore.py:open_for_write():87] open: /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250422_082621-szw51ri2/run-szw51ri2.wandb
2025-04-22 08:26:21,186 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: run_start
2025-04-22 08:26:21,187 DEBUG   HandlerThread:6032 [system_info.py:__init__():26] System info init
2025-04-22 08:26:21,187 DEBUG   HandlerThread:6032 [system_info.py:__init__():41] System info init done
2025-04-22 08:26:21,187 INFO    HandlerThread:6032 [system_monitor.py:start():194] Starting system monitor
2025-04-22 08:26:21,187 INFO    SystemMonitor:6032 [system_monitor.py:_start():158] Starting system asset monitoring threads
2025-04-22 08:26:21,187 INFO    HandlerThread:6032 [system_monitor.py:probe():214] Collecting system info
2025-04-22 08:26:21,188 INFO    SystemMonitor:6032 [interfaces.py:start():190] Started cpu monitoring
2025-04-22 08:26:21,188 DEBUG   HandlerThread:6032 [system_info.py:probe():150] Probing system
2025-04-22 08:26:21,188 INFO    SystemMonitor:6032 [interfaces.py:start():190] Started disk monitoring
2025-04-22 08:26:21,190 INFO    SystemMonitor:6032 [interfaces.py:start():190] Started memory monitoring
2025-04-22 08:26:21,190 INFO    SystemMonitor:6032 [interfaces.py:start():190] Started network monitoring
2025-04-22 08:26:21,197 DEBUG   HandlerThread:6032 [gitlib.py:_init_repo():56] git repository is invalid
2025-04-22 08:26:21,197 DEBUG   HandlerThread:6032 [system_info.py:probe():198] Probing system done
2025-04-22 08:26:21,197 DEBUG   HandlerThread:6032 [system_monitor.py:probe():223] {'os': 'macOS-10.16-x86_64-i386-64bit', 'python': '3.8.20', 'heartbeatAt': '2025-04-22T00:26:21.188397', 'startedAt': '2025-04-22T00:26:21.159364', 'docker': None, 'cuda': None, 'args': ('-e', 'base_77obj_surf_agent'), 'state': 'running', 'program': 'run.py', 'codePathLocal': 'run.py', 'codePath': 'run.py', 'host': 'jiangzixideMacBook-Air.local', 'username': 'jiangzixi', 'executable': '/opt/anaconda3/envs/tbp.monty/bin/python', 'cpu_count': 8, 'cpu_count_logical': 8, 'cpu_freq': {'current': 2400, 'min': 2400, 'max': 2400}, 'cpu_freq_per_core': [{'current': 2400, 'min': 2400, 'max': 2400}], 'disk': {'/': {'total': 228.27386474609375, 'used': 171.81040954589844}}, 'memory': {'total': 8.0}}
2025-04-22 08:26:21,197 INFO    HandlerThread:6032 [system_monitor.py:probe():224] Finished collecting system info
2025-04-22 08:26:21,197 INFO    HandlerThread:6032 [system_monitor.py:probe():227] Publishing system info
2025-04-22 08:26:21,197 DEBUG   HandlerThread:6032 [system_info.py:_save_conda():207] Saving list of conda packages installed into the current environment
2025-04-22 08:26:23,196 DEBUG   HandlerThread:6032 [system_info.py:_save_conda():222] Saving conda packages done
2025-04-22 08:26:23,197 INFO    HandlerThread:6032 [system_monitor.py:probe():229] Finished publishing system info
2025-04-22 08:26:23,313 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: python_packages
2025-04-22 08:26:26,203 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:26:26,203 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:26:27,674 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:27,675 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:27,676 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:31,217 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:26:31,217 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:26:31,733 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:31,734 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:31,734 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:36,242 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:26:36,243 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:26:38,645 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:38,645 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:38,646 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:41,267 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:26:41,268 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:26:42,297 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:42,298 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:42,298 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:46,112 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:46,112 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:46,113 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:46,285 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:26:46,285 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:26:50,691 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:50,691 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:50,691 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:51,303 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:26:51,304 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:26:54,616 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:54,616 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:54,617 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:56,324 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:26:56,325 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:26:58,048 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:58,048 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:26:58,049 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:01,215 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:01,215 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:01,215 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:01,346 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:01,346 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:05,638 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:05,639 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:05,639 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:06,366 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:06,366 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:09,820 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:09,820 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:09,820 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:11,391 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:11,392 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:13,796 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:13,796 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:13,796 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:16,410 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:16,410 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:16,888 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:16,889 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:16,889 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:20,589 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:20,590 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:20,590 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:21,195 DEBUG   SystemMonitor:6032 [system_monitor.py:_start():172] Starting system metrics aggregation loop
2025-04-22 08:27:21,435 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:21,435 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:25,041 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:25,042 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:25,042 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:26,456 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:26,456 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:28,568 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:28,569 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:28,569 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:31,479 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:31,479 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:31,729 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:31,729 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:31,730 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:36,504 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:36,504 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:36,767 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:36,768 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:36,768 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:40,606 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:40,606 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:40,607 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:41,520 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:41,520 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:45,668 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:45,668 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:45,669 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:46,543 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:46,543 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:51,564 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:51,565 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:51,566 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:51,567 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:51,567 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:55,068 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:55,068 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:55,069 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:56,589 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:27:56,589 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:27:59,340 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:59,341 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:27:59,341 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:01,611 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:01,615 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:05,195 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:05,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:05,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:06,634 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:06,634 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:11,658 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:11,658 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:12,135 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:12,136 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:12,136 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:16,684 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:16,684 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:16,808 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:16,809 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:16,809 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:20,523 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:20,524 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:20,524 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:21,704 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:21,704 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:24,215 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:24,216 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:24,216 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:26,727 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:26,727 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:31,747 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:31,747 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:33,466 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:33,467 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:33,467 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:36,772 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:36,772 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:37,986 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:37,986 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:37,986 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:41,298 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:41,298 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:41,299 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:41,794 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:41,794 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:46,812 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:46,812 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:47,528 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:47,529 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:47,529 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:51,836 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:51,837 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:28:52,037 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:52,038 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:52,038 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:56,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:56,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:56,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:28:56,862 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:28:56,862 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:00,099 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:00,100 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:00,100 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:01,888 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:01,889 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:06,676 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:06,676 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:06,676 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:06,907 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:06,908 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:09,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:09,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:09,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:11,930 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:11,930 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:16,946 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:16,947 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:19,924 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:19,924 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:19,924 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:21,970 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:21,971 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:23,152 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:23,152 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:23,152 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:25,848 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:25,849 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:25,849 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:26,995 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:26,995 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:31,096 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:31,096 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:31,097 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:32,018 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:32,018 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:36,195 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:36,195 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:36,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:37,038 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:37,038 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:39,168 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:39,168 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:39,168 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:42,060 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:42,061 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:43,570 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:43,570 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:43,570 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:46,913 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:46,914 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:46,914 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:47,085 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:47,085 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:50,464 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:50,465 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:50,465 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:52,109 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:52,109 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:29:55,692 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:55,692 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:55,693 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:29:57,128 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:29:57,128 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:02,149 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:02,149 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:02,893 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:02,893 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:02,893 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:06,363 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:06,363 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:06,364 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:07,173 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:07,174 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:12,199 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:12,200 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:12,719 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:12,719 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:12,719 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:15,416 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:15,417 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:15,417 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:17,220 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:17,220 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:22,237 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:22,237 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:23,085 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:23,085 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:23,085 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:26,351 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:26,352 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:26,352 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:27,259 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:27,260 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:30,712 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:30,713 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:30,713 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:32,284 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:32,285 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:33,707 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:33,707 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:33,708 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:37,308 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:37,308 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:37,596 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:37,597 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:37,597 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:41,543 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:41,543 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:41,544 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:42,328 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:42,329 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:45,203 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:45,203 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:45,203 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:47,348 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:47,348 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:48,438 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:48,438 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:48,439 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:52,372 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:52,373 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:30:54,280 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:54,281 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:54,281 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:30:57,389 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:30:57,389 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:01,094 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:01,095 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:01,095 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:02,415 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:02,415 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:06,235 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:06,236 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:06,236 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:07,438 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:07,439 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:12,457 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:12,457 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:15,780 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:15,781 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:15,781 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:17,481 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:17,481 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:19,292 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:19,293 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:19,293 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:22,506 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:22,506 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:26,236 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:26,237 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:26,237 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:27,531 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:27,532 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:30,161 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:30,161 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:30,161 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:31:32,556 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:32,556 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:37,580 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:37,581 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:42,606 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:42,606 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:47,629 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:47,630 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:52,651 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:52,651 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:31:57,674 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:31:57,674 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:02,697 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:02,698 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:07,723 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:07,723 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:11,735 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:11,736 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:11,736 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:12,747 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:12,747 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:17,773 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:17,773 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:22,798 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:22,799 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:27,824 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:27,825 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:32,850 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:32,850 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:34,248 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:34,248 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:34,248 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:37,536 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:37,536 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:37,537 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:37,874 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:37,874 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:40,885 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:40,886 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:40,886 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:42,897 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:42,897 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:44,066 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:44,067 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:44,067 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:47,921 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:47,922 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:48,257 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:48,257 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:48,257 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:52,855 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:52,855 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:52,855 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:52,944 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:52,945 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:56,570 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:56,570 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:56,571 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:57,968 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:32:57,968 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:32:59,746 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:59,747 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:32:59,747 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:02,983 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:02,984 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:03,095 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:03,096 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:03,096 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:06,888 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:06,888 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:06,889 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:07,998 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:07,999 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:12,853 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:12,853 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:12,854 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:13,020 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:13,020 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:16,808 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:16,808 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:16,809 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:18,037 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:18,037 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:23,062 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:23,063 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:24,842 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:24,842 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:24,842 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:28,087 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:28,087 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:28,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:28,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:28,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:32,963 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:32,964 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:32,964 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:33,111 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:33,112 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:38,136 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:38,136 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:43,161 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:43,161 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:43,241 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:43,241 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:43,242 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:48,182 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:48,182 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:48,954 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:48,955 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:48,955 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:53,209 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:53,210 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:33:54,125 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:54,126 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:54,126 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:33:58,234 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:33:58,234 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:03,259 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:03,259 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:08,284 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:08,284 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:13,309 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:13,310 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:18,335 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:18,335 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:23,353 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:23,353 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:28,377 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:28,378 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:33,398 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:33,398 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:38,425 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:38,425 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:43,447 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:43,448 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:44,413 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:44,414 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:44,414 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:48,474 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:48,474 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:50,518 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:50,518 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:50,518 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:53,500 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:53,501 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:34:58,477 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:58,478 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:58,478 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:34:58,522 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:34:58,522 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:03,540 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:03,540 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:04,835 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:04,835 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:04,836 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:08,421 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:08,422 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:08,422 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:08,566 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:08,566 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:12,705 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:12,706 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:12,706 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:13,585 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:13,585 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:17,180 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:17,180 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:17,181 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:18,610 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:18,611 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:20,866 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:20,866 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:20,866 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:23,633 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:23,634 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:27,058 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:27,059 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:27,059 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:28,648 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:28,649 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:31,583 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:31,583 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:31,583 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:33,671 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:33,672 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:37,819 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:37,820 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:37,820 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:38,695 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:38,695 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:43,488 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:43,489 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:43,489 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:43,719 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:43,720 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:48,745 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:48,747 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:49,920 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:49,920 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:49,921 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:53,764 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:53,765 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:35:54,130 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:54,131 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:54,131 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:35:58,786 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:35:58,786 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:00,922 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:00,922 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:00,922 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:03,809 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:03,810 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:08,830 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:08,830 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:08,949 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:08,950 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:08,950 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:13,854 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:13,854 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:17,082 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:17,082 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:17,083 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:18,875 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:18,875 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:22,572 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:22,573 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:22,573 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:23,896 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:23,897 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:28,914 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:28,914 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:29,142 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:29,143 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:29,143 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:33,827 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:33,827 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:33,828 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:33,936 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:33,936 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:38,231 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:38,232 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:38,232 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:38,961 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:38,961 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:43,984 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:43,984 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:44,620 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:44,621 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:44,621 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:48,711 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:48,712 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:48,712 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:49,009 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:49,009 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:54,027 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:54,028 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:54,576 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:54,577 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:54,577 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:59,049 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:36:59,050 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:36:59,567 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:59,568 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:36:59,568 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:04,071 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:04,071 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:06,536 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:06,537 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:06,537 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:09,090 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:09,090 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:10,689 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:10,690 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:10,690 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:14,109 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:14,110 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:16,272 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:16,273 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:16,273 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:19,124 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:19,124 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:23,124 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:23,124 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:23,124 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:24,150 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:24,150 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:28,298 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:28,299 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:28,299 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:29,173 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:29,174 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:30,921 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:30,921 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:30,922 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:34,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:34,196 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:36,195 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:36,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:36,196 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:39,221 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:39,222 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:41,400 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:41,401 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:41,401 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:44,247 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:44,248 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:48,684 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:48,684 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:48,685 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:49,267 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:49,267 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:51,799 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:51,800 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:51,800 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:54,291 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:54,292 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:37:57,829 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:57,829 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:57,829 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:37:59,310 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:37:59,310 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:01,832 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:01,832 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:01,832 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:04,336 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:04,336 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:07,349 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:07,350 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:07,350 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:09,357 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:09,358 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:14,380 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:14,381 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:14,941 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:14,942 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:14,942 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:19,398 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:19,398 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:24,420 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:24,421 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:29,446 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:29,447 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:33,673 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:33,674 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:33,674 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:34,466 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:34,466 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:36,837 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:36,838 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:36,838 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:39,478 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:39,479 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:43,646 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:43,647 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:43,647 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:44,492 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:44,493 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:46,413 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:46,413 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:46,413 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:49,519 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:49,519 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:50,608 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:50,608 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:50,608 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:54,035 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:54,035 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:54,036 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:54,540 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:54,540 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:38:57,130 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:57,130 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:57,131 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:38:59,562 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:38:59,562 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:02,643 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:02,644 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:02,644 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:04,585 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:04,586 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:06,280 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:06,280 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:06,280 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:09,610 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:09,610 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:11,553 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:11,553 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:11,553 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:14,635 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:14,635 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:19,660 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:19,661 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:24,683 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:24,683 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:29,708 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:29,708 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:34,734 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:34,734 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:39,758 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:39,758 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:44,782 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:44,783 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:49,808 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:49,808 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:50,590 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:50,591 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:50,591 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:54,483 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:54,483 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:54,483 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:39:54,831 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:54,831 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:39:59,842 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:39:59,843 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:01,139 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:01,139 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:01,139 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:04,860 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:04,861 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:06,152 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:06,152 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:06,152 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:09,884 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:09,885 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:11,178 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:11,178 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:11,178 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:14,899 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:14,899 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:16,550 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:16,550 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:16,550 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:19,923 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:19,923 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:20,648 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:20,649 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:20,649 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:24,945 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:24,946 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:29,961 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:29,961 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:31,828 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:31,829 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:31,829 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:34,987 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:34,988 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:39,869 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:39,869 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:39,869 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:40:40,001 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:40,001 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:45,026 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:45,027 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:50,053 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:50,053 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:40:55,077 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:40:55,078 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:00,094 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:00,094 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:05,116 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:05,116 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:10,141 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:10,141 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:15,164 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:15,165 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:20,189 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:20,190 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:24,775 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:24,775 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:24,775 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:25,214 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:25,214 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:27,805 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:27,805 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:27,806 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:30,240 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:30,240 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:32,581 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:32,581 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:32,581 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:35,264 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:35,264 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:36,660 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:36,660 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:36,660 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:40,281 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:40,281 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:40,507 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:40,508 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:40,508 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:43,991 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:43,991 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:43,991 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:45,306 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:45,307 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:49,902 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:49,902 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:49,903 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:41:50,327 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:50,327 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:41:55,345 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:41:55,345 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:00,366 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:00,367 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:05,391 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:05,392 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:10,416 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:10,416 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:15,437 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:15,437 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:20,462 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:20,462 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:23,941 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:23,941 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:23,942 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:25,487 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:25,487 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:27,428 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:27,428 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:27,429 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:30,504 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:30,504 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:34,769 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:34,770 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:34,770 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:42:35,524 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:35,525 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:40,536 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:40,536 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:45,561 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:45,561 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:50,585 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:50,586 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:42:55,611 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:42:55,611 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:00,632 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:00,632 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:05,653 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:05,654 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:10,679 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:10,679 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:13,184 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:13,184 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:13,185 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:15,703 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:15,704 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:18,324 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:18,324 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:18,324 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:20,742 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:20,748 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:25,767 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:25,768 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:30,792 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:30,792 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:34,998 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:34,998 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:34,998 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:35,814 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:35,814 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:40,832 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:40,832 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:44,597 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:44,598 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:44,598 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:45,852 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:45,853 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:47,821 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:47,822 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:47,822 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:50,875 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:50,876 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:43:54,036 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:54,037 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:54,037 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:43:55,898 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:43:55,899 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:00,923 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:00,923 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:05,948 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:05,949 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:10,973 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:10,974 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:12,736 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:12,737 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:12,737 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:15,999 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:16,000 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:17,392 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:17,393 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:17,393 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:21,025 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:21,025 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:21,791 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:21,791 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:21,791 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:26,051 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:26,051 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:27,925 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:27,926 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:27,926 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:31,072 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:31,072 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:36,096 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:36,097 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:41,119 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:41,119 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:46,143 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:46,143 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:51,164 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:51,164 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:54,926 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:54,926 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:54,927 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:56,183 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:44:56,184 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:44:59,376 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:59,376 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:44:59,377 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:01,208 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:01,208 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:03,521 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:03,522 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:03,522 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:06,235 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:06,237 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:09,538 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:09,538 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:09,538 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:11,256 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:11,256 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:16,282 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:16,282 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:21,307 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:21,308 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:26,330 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:26,330 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:31,355 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:31,356 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:36,381 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:36,382 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:41,407 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:41,408 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:43,032 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:43,032 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:43,033 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:46,432 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:46,433 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:48,200 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:48,201 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:48,201 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:51,456 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:51,456 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:52,870 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:52,870 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:52,870 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:56,478 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:45:56,478 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:45:57,262 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:57,263 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:45:57,263 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:01,501 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:01,503 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:02,030 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:02,031 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:02,031 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:06,521 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:06,521 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:11,536 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:11,537 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:12,542 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:12,542 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:12,543 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:16,560 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:16,561 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:16,719 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:16,719 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:16,720 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:21,585 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:21,586 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:26,008 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:26,008 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:26,009 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:26,603 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:26,603 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:31,624 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:31,625 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:32,320 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:32,321 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:32,321 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:36,648 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:36,648 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:38,169 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:38,170 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:38,170 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:41,672 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:41,673 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:43,198 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:43,199 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:43,199 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:46,697 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:46,697 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:49,446 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:49,447 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:49,447 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:51,720 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:51,721 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:46:53,542 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:53,543 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:53,543 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:46:56,743 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:46:56,744 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:01,762 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:01,763 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:06,786 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:06,786 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:11,810 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:11,810 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:16,830 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:16,830 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:21,851 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:21,851 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:26,475 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:26,476 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:26,476 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:26,877 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:26,877 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:31,643 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:31,644 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:31,644 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:31,902 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:31,902 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:36,627 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:36,628 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:36,628 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:36,926 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:36,926 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:41,942 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:41,943 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:42,857 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:42,857 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:42,858 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:46,434 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:46,434 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:46,435 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:46,965 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:46,965 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:51,982 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:51,983 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:53,562 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:53,563 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:53,563 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:57,006 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:47:57,006 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:47:58,970 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:58,971 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:47:58,971 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:02,031 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:02,032 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:04,731 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:04,731 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:04,731 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:07,057 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:07,057 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:09,418 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:09,419 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:09,419 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:12,080 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:12,081 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:17,094 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:17,094 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:22,039 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:22,039 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:22,040 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:22,113 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:22,113 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:24,733 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:24,733 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:24,734 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:48:27,135 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:27,136 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:32,160 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:32,161 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:37,185 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:37,185 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:42,206 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:42,206 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:47,232 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:47,233 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:52,259 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:52,259 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:48:57,284 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:48:57,285 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:02,310 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:02,310 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:07,332 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:07,333 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:12,357 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:12,358 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:17,381 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:17,382 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:19,071 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:19,071 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:19,072 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:22,396 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:22,396 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:25,568 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:25,568 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:25,568 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:27,421 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:27,421 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:28,574 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:28,574 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:28,574 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:32,443 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:32,444 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:33,942 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:33,942 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:33,942 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:37,470 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:37,470 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:40,008 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:40,008 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:40,008 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:42,491 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:42,491 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:45,307 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:45,308 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:45,308 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:47,510 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:47,510 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:50,997 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:50,998 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:50,998 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:52,529 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:52,530 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:49:55,337 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:55,337 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:55,338 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:49:57,555 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:49:57,555 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:01,090 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:01,091 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:01,091 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:02,576 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:02,576 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:06,761 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:06,761 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:06,762 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:07,599 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:07,599 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:11,774 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:11,774 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:11,774 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:12,622 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:12,622 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:15,507 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:15,508 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:15,508 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:17,646 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:17,647 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:22,668 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:22,668 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:24,229 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:24,229 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:24,230 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:27,048 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:27,049 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:27,049 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:27,692 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:27,692 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:32,709 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:32,709 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:33,404 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:33,404 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:33,405 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:37,722 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:37,722 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:41,072 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:41,072 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:41,073 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:42,744 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:42,744 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:45,096 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:45,097 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:45,097 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:47,767 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:47,767 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:49,003 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:49,003 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:49,003 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:52,108 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:52,109 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:52,109 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:50:52,792 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:52,793 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:50:57,818 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:50:57,818 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:00,567 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:00,568 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:00,568 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:02,840 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:02,840 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:07,856 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:07,856 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:12,882 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:12,882 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:17,908 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:17,908 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:22,930 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:22,931 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:27,952 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:27,953 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:32,970 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:32,971 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:37,987 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:37,988 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:43,006 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:43,007 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:46,776 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:46,776 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:46,776 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:48,032 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:48,032 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:50,239 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:50,240 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:50,240 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:53,056 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:53,056 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:51:57,376 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:57,377 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:57,377 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:51:58,077 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:51:58,077 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:03,096 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:03,096 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:08,115 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:08,116 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:13,132 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:13,132 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:13,593 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:13,594 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:13,594 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:18,152 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:18,152 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:19,275 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:19,275 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:19,275 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:23,144 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:23,145 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:23,145 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:23,174 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:23,174 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:27,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:27,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:27,552 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:52:28,186 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:28,186 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:33,208 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:33,209 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:38,234 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:38,234 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:43,260 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:43,261 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:48,288 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:48,299 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:53,324 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:53,325 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:52:58,351 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:52:58,351 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:03,374 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:03,375 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:08,396 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:08,398 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:10,068 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:53:10,069 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:53:10,070 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:53:13,414 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:13,416 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:18,441 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:18,444 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:19,841 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:53:19,842 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:53:19,843 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:53:23,462 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:23,463 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:28,483 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:28,483 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:33,502 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:33,503 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:38,521 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:38,522 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:43,539 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:43,539 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:48,560 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:48,560 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:53,580 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:53,581 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:53:58,610 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:53:58,614 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:03,635 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:03,635 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:05,320 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:05,320 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:05,320 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:08,662 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:08,663 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:13,076 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:13,077 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:13,077 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:13,682 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:13,682 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:17,476 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:17,476 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:17,477 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:18,698 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:18,698 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:22,094 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:22,094 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:22,095 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:23,716 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:23,716 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:28,730 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:28,731 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:29,884 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:29,884 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:29,885 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:33,752 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:33,753 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:34,504 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:34,504 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:34,504 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:38,778 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:38,779 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:40,775 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:40,775 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:40,776 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:54:43,798 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:43,799 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:48,823 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:48,824 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:53,846 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:53,847 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:54:58,872 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:54:58,872 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:03,891 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:03,892 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:08,914 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:08,914 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:13,927 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:13,928 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:18,937 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:18,939 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:23,951 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:23,953 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:28,974 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:28,975 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:33,988 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:33,989 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:36,185 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:55:36,186 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:55:36,186 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:55:39,017 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:39,026 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:44,047 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:44,048 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:49,074 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:49,084 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:50,825 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:55:50,827 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:55:50,828 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:55:54,105 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:54,107 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:55:59,130 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:55:59,131 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:02,416 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:56:02,421 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:56:02,421 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:56:04,146 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:04,147 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:09,160 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:09,161 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:14,180 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:14,182 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:19,216 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:19,218 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:24,244 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:24,245 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:29,266 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:29,267 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:34,288 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:34,290 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:39,309 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:39,309 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:44,323 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:44,323 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:49,346 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:49,347 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:54,366 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:54,367 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:57,425 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:56:57,426 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:56:57,426 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: partial_history
2025-04-22 08:56:58,832 DEBUG   SenderThread:6032 [sender.py:send():379] send: exit
2025-04-22 08:56:58,834 INFO    SenderThread:6032 [sender.py:send_exit():586] handling exit code: 0
2025-04-22 08:56:58,843 INFO    SenderThread:6032 [sender.py:send_exit():588] handling runtime: 1837
2025-04-22 08:56:58,845 INFO    SenderThread:6032 [sender.py:_save_file():1390] saving file wandb-summary.json with policy end
2025-04-22 08:56:58,845 INFO    SenderThread:6032 [sender.py:send_exit():594] send defer
2025-04-22 08:56:58,845 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,846 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 0
2025-04-22 08:56:58,846 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,846 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 0
2025-04-22 08:56:58,847 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 1
2025-04-22 08:56:58,847 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,847 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 1
2025-04-22 08:56:58,847 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,847 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 1
2025-04-22 08:56:58,847 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 2
2025-04-22 08:56:58,847 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,847 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 2
2025-04-22 08:56:58,847 INFO    HandlerThread:6032 [system_monitor.py:finish():203] Stopping system monitor
2025-04-22 08:56:58,848 DEBUG   SystemMonitor:6032 [system_monitor.py:_start():179] Finished system metrics aggregation loop
2025-04-22 08:56:58,848 DEBUG   SystemMonitor:6032 [system_monitor.py:_start():183] Publishing last batch of metrics
2025-04-22 08:56:58,849 INFO    HandlerThread:6032 [interfaces.py:finish():202] Joined cpu monitor
2025-04-22 08:56:58,850 INFO    HandlerThread:6032 [interfaces.py:finish():202] Joined disk monitor
2025-04-22 08:56:58,850 INFO    HandlerThread:6032 [interfaces.py:finish():202] Joined memory monitor
2025-04-22 08:56:58,850 INFO    HandlerThread:6032 [interfaces.py:finish():202] Joined network monitor
2025-04-22 08:56:58,850 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,850 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 2
2025-04-22 08:56:58,850 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 3
2025-04-22 08:56:58,850 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,850 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 3
2025-04-22 08:56:58,850 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,850 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 3
2025-04-22 08:56:58,850 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 4
2025-04-22 08:56:58,851 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,851 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 4
2025-04-22 08:56:58,851 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,851 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 4
2025-04-22 08:56:58,851 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 5
2025-04-22 08:56:58,851 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,851 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 5
2025-04-22 08:56:58,851 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,851 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 5
2025-04-22 08:56:58,851 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 6
2025-04-22 08:56:58,851 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,851 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 6
2025-04-22 08:56:58,851 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,851 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 6
2025-04-22 08:56:58,852 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 7
2025-04-22 08:56:58,852 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: status_report
2025-04-22 08:56:58,852 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,852 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 7
2025-04-22 08:56:58,852 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: status_report
2025-04-22 08:56:58,852 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,852 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 7
2025-04-22 08:56:58,852 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 8
2025-04-22 08:56:58,852 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,852 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 8
2025-04-22 08:56:58,852 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,852 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 8
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 9
2025-04-22 08:56:58,853 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,853 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 9
2025-04-22 08:56:58,853 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 9
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 10
2025-04-22 08:56:58,853 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,853 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 10
2025-04-22 08:56:58,853 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 10
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 11
2025-04-22 08:56:58,853 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,853 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 11
2025-04-22 08:56:58,853 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 11
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 12
2025-04-22 08:56:58,853 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,853 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 12
2025-04-22 08:56:58,853 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 12
2025-04-22 08:56:58,853 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 13
2025-04-22 08:56:58,854 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,854 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 13
2025-04-22 08:56:58,854 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,854 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 13
2025-04-22 08:56:58,854 INFO    SenderThread:6032 [sender.py:transition_state():614] send defer: 14
2025-04-22 08:56:58,855 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: defer
2025-04-22 08:56:58,855 DEBUG   SenderThread:6032 [sender.py:send():379] send: final
2025-04-22 08:56:58,855 INFO    HandlerThread:6032 [handler.py:handle_request_defer():172] handle defer: 14
2025-04-22 08:56:58,855 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: defer
2025-04-22 08:56:58,855 INFO    SenderThread:6032 [sender.py:send_request_defer():610] handle sender defer: 14
2025-04-22 08:56:58,858 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: poll_exit
2025-04-22 08:56:58,858 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: poll_exit
2025-04-22 08:56:58,860 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: internal_messages
2025-04-22 08:56:58,861 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: server_info
2025-04-22 08:56:58,861 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: get_summary
2025-04-22 08:56:58,861 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: sampled_history
2025-04-22 08:56:58,861 DEBUG   SenderThread:6032 [sender.py:send_request():406] send_request: server_info
2025-04-22 08:56:58,869 DEBUG   HandlerThread:6032 [handler.py:handle_request():146] handle_request: shutdown
2025-04-22 08:56:58,869 INFO    HandlerThread:6032 [handler.py:finish():866] shutting down handler
2025-04-22 08:56:59,870 INFO    WriterThread:6032 [datastore.py:close():296] close: /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250422_082621-szw51ri2/run-szw51ri2.wandb
2025-04-22 08:56:59,874 INFO    SenderThread:6032 [sender.py:finish():1546] shutting down sender
