---
title: Welcome to the Thousand Brains Project Documentation!
---

![](../figures/overview/logo.png)

Welcome to the Thousand Brains Project, an open-source framework for sensorimotor learning systems that follow the same principles as the human brain.

This documentation outlines the current features, and future vision, of our platform for building next-generation AI and robotics applications using neocortical algorithms. In addition, we describe the details of <PERSON>, the first implementation of a thousand-brains system. Named in honor of <PERSON>, who argued that the power of the mammalian brain lies in its re-use of cortical columns as the primary computational unit, <PERSON> represents a fundamentally new way of building AI systems.

The Monty project incorporates a lot of new concepts and ideas and will require considerable learning on the part of contributors who want to make significant code contributions or to use the code to solve real-world problems.  With that in mind, we've tried to make the project easy to comprehend and get started with.  To understand the fundamental principles of the project, these are some resources that we recommend:

1. 🧠 [Vision of the Thousand Brains Project](./vision-of-the-thousand-brains-project.md) which describes the **guiding principles** of the project.
2. 🎥 [YouTube Videos](https://www.youtube.com/@thousandbrainsproject) that contain in depth descriptions of the project and the principles that guide it.  The **Quick Start playlist** is the fastest way to learn the basics.
3. 📚 [Tutorials](../how-to-use-monty/tutorials.md) which are **step-by-step guides** for using Monty.
4. 💬 [Discourse Forum](https://thousandbrains.discourse.group/) which is a community forum for discussing the project and a great place for beginners to **get answers to questions**.
5. ❓ You can also check out our FAQs for the [Thousand Brains Project](./faq-thousand-brains-project.md), and the underlying [Monty algorithms](../how-monty-works/faq-monty.md).

# Section Overview

The documentation is broken into six main sections:

| Section | Description |
|---------|-------------|
| 🔍&nbsp;[Overview](./vision-of-the-thousand-brains-project.md) | Learn about the core principles and architecture behind the Thousand Brains Project. This section also talks about potential practical applications of the system and presents the abilities of our current implementation. |
| 🚀&nbsp;[How&nbsp;to&nbsp;Use&nbsp;Monty](../how-to-use-monty/getting-started.md) | Get started quickly with step-by-step guides for installing, configuring, and running your first Monty experiments. |
| ⚙️&nbsp;[How&nbsp;Monty&nbsp;Works](../how-monty-works/implementation-overview.md) | Dive deep into the concrete algorithms that make Monty work and understand how the different components function together. |
| 🤝&nbsp;[Contributing](../contributing/why-contribute.md) | Discover the many ways you can contribute to the project - from code and documentation to testing and ideas. |
| 👥&nbsp;[Community](../community/code-of-conduct.md) | Join our welcoming community! Learn about our guidelines, code of conduct, and how to participate effectively. |
| 🔮&nbsp;[Future&nbsp;Work](../future-work/project-roadmap.md) | Explore exciting opportunities to help shape Monty's future by contributing to planned features and improvements. |


# Our Level of Community Engagement

We are excited to have you here!  Our intention for making the project open-source is to foster a community of researchers and developers interested in contributing to the project and to allow all of humanity to benefit from these advances in AI.  The Thousand Brains Project team is quite small, and we have a limited amount of time, so please be patient with our responses and know that we'll do our best to get back to you as soon as possible.  That said, here's a list of [ways you can contribute to the project](../contributing/ways-to-contribute-to-code.md).


-----------------------------------

# Resources

| Resource | Description |
|----------|-------------|
| <a href="https://github.com/thousandbrainsproject/tbp.monty" style="display: flex; align-items: center;"><img src="../figures/overview/github.png" alt="GitHub" height="15" style="opacity: 1; transition: opacity 0.2s; &:hover { opacity: 0.8; }" pointer-events="none">&nbsp;GitHub&nbsp;Repository</a> | Access our source code, contribute features, report issues and collaborate with other developers |
| <a href="https://thousandbrains.discourse.group/" style="display: flex; align-items: center;"><img src="../figures/overview/discourse.png" alt="Forum" height="15" style="opacity: 1; transition: opacity 0.2s; &:hover { opacity: 0.8; }" pointer-events="none">&nbsp;Community&nbsp;Forum</a> | Discuss ideas, ask questions and connect with other community members |
| <a href="https://thousandbrains.org/" style="display: flex; align-items: center;"><img src="../figures/overview/website.png" alt="Website" height="15" style="opacity: 1; transition: opacity 0.2s; &:hover { opacity: 0.8; }" pointer-events="none">&nbsp;Official&nbsp;Website</a> | Learn about our mission, team and the science behind the project |
| <a href="https://www.youtube.com/@thousandbrainsproject" style="display: flex; align-items: center;"><img src="../figures/overview/youtube.png" alt="YouTube" height="15" style="opacity: 1; transition: opacity 0.2s; &:hover { opacity: 0.8; }" pointer-events="none">&nbsp;YouTube&nbsp;Channel</a> | Watch tutorials, technical deep-dives and project updates |
| <a href="https://bsky.app/profile/1000brainsproj.bsky.social" style="display: flex; align-items: center;"><img src="../figures/overview/bluesky.png" alt="Bluesky" height="15" style="opacity: 1; transition: opacity 0.2s; &:hover { opacity: 0.8; }" pointer-events="none">&nbsp;Bluesky</a> <a href="https://x.com/1000brainsproj" style="display: flex; align-items: center;"><img src="../figures/overview/twitter.png" alt="X/Twitter" height="15" style="opacity: 1; transition: opacity 0.2s; &:hover { opacity: 0.8; }" pointer-events="none">&nbsp;X/Twitter</a> | Get the latest news and announcements, and engage with our community |
| <a href="https://www.linkedin.com/company/thousand-brains-project" style="display: flex; align-items: center;"><img src="../figures/overview/linkedin.png" alt="LinkedIn" height="15" style="opacity: 1; transition: opacity 0.2s; &:hover { opacity: 0.8; }" pointer-events="none">&nbsp;LinkedIn&nbsp;Page</a> | Get the latest news and announcements |

# Citing This Project
If you're writing a publication that references the Thousand Brains Project, please cite our TBP white paper:
```
@misc{thousandbrainsproject2024,
      title={The Thousand Brains Project: A New Paradigm for Sensorimotor Intelligence},
      author={Viviane Clay and Niels Leadholm and Jeff Hawkins},
      year={2024},
      eprint={2412.18354},
      archivePrefix={arXiv},
      primaryClass={cs.AI},
      url={https://arxiv.org/abs/2412.18354},
}
```