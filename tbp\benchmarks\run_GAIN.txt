(tbp.monty) jiang<PERSON><PERSON>@jiangzixideMacBook-Air benchmarks % python run_GAIN.py -e base_77obj_surf_agent
MONTY_LOGS not set. Using default directory: ~/tbp/results/monty/
MONTY_MODELS not set. Using default directory: ~/tbp/results/monty/pretrained_models/
MONTY_DATA not set. Using default directory: /Users/<USER>/tbp/data/
WANDB_DIR not set. Using default directory: ~/tbp/results/monty/
Type of CONFIGS: <class 'dict'>
CONFIGS content: {'base_config_10distinctobj_dist_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 14, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10distinctobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 5, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'hsv', 'principal_curvatures_log'], 'delta_thresholds': {'on_object': 0, 'n_steps': 20, 'hsv': [0.1, 0.1, 0.1], 'pose_vectors': [0.7853981633974483, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [2, 2], 'distance': 0.01}, 'save_raw_obs': False}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': False}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 5.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16054f5b0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16054f850>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0]), array([  0, 270,   0]), array([90,  0,  0]), array([ 90, 180,   0]), array([35, 45,  0]), array([325,  45,   0]), array([ 35, 315,   0]), array([325, 315,   0]), array([ 35, 135,   0]), array([325, 135,   0]), array([ 35, 225,   0]), array([325, 225,   0])]
         change every episode: None}}, 'base_config_10distinctobj_surf_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 5000, 'n_train_epochs': 3, 'n_eval_epochs': 14, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10distinctobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 5, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'delta_thresholds': {'on_object': 0, 'n_steps': 20, 'hsv': [0.1, 0.1, 0.1], 'pose_vectors': [0.7853981633974483, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [2, 2], 'distance': 0.01}, 'surf_agent_sm': True, 'save_raw_obs': False}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': False}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16054fa30>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16054fcd0>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0]), array([  0, 270,   0]), array([90,  0,  0]), array([ 90, 180,   0]), array([35, 45,  0]), array([325,  45,   0]), array([ 35, 315,   0]), array([325, 315,   0]), array([ 35, 135,   0]), array([325, 135,   0]), array([ 35, 225,   0]), array([325, 225,   0])]
         change every episode: None}}, 'randrot_noise_10distinctobj_dist_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 10, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10distinctobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 5.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16054fe20>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16055b100>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x16055b130>}}, 'randrot_noise_10distinctobj_dist_on_distm': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 10, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/supervised_pre_training_base/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 5.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16055b280>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16055b520>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x16055b550>}}, 'randrot_noise_10distinctobj_surf_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 5000, 'n_train_epochs': 3, 'n_eval_epochs': 10, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10distinctobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'surf_agent_sm': True, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16055b6a0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16055b940>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x16055b970>}}, 'randrot_10distinctobj_surf_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 5000, 'n_train_epochs': 3, 'n_eval_epochs': 10, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10distinctobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 5, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'delta_thresholds': {'on_object': 0, 'n_steps': 20, 'hsv': [0.1, 0.1, 0.1], 'pose_vectors': [0.7853981633974483, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [2, 2], 'distance': 0.01}, 'surf_agent_sm': True, 'save_raw_obs': False}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': False}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16055bb50>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16055bdf0>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x16055be20>}}, 'randrot_noise_10distinctobj_5lms_dist_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 10, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/supervised_pre_training_5lms/pretrained/', 'min_lms_match': 3, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_0': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}, 'learning_module_1': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_1': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}, 'learning_module_2': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_2': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}, 'learning_module_3': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_3': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}, 'learning_module_4': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_4': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_0', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_1', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_2': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_2', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_3': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_3', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_4': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_4', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_5': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 5.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch_0': 'agent_id_0', 'patch_1': 'agent_id_0', 'patch_2': 'agent_id_0', 'patch_3': 'agent_id_0', 'patch_4': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0], [1], [2], [3], [4]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': [[1, 2, 3, 4], [0, 2, 3, 4], [0, 1, 3, 4], [0, 1, 2, 4], [0, 1, 2, 3]], 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch_0', 'patch_1', 'patch_2', 'patch_3', 'patch_4', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64], [64, 64], [64, 64], [64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.01, 0.0], [0.0, -0.01, 0.0], [0.01, 0.0, 0.0], [-0.01, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False, False, False, False, False], 'zooms': [10.0, 10.0, 10.0, 10.0, 10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16055bf70>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16056c2b0>]}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x16056c310>}}, 'base_10simobj_surf_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 14, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10similarobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 5, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'delta_thresholds': {'on_object': 0, 'n_steps': 20, 'hsv': [0.1, 0.1, 0.1], 'pose_vectors': [0.7853981633974483, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [2, 2], 'distance': 0.01}, 'surf_agent_sm': True, 'save_raw_obs': False}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': False}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16056c4c0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16056c760>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'e_cups', 'knife', 'fork', 'spoon', 'c_cups', 'd_cups', 'cracker_box', 'sugar_box', 'pudding_box'], 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0]), array([  0, 270,   0]), array([90,  0,  0]), array([ 90, 180,   0]), array([35, 45,  0]), array([325,  45,   0]), array([ 35, 315,   0]), array([325, 315,   0]), array([ 35, 135,   0]), array([325, 135,   0]), array([ 35, 225,   0]), array([325, 225,   0])]
         change every episode: None}}, 'randrot_noise_10simobj_surf_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 10, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10similarobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'surf_agent_sm': True, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16056c8b0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16056cb50>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'e_cups', 'knife', 'fork', 'spoon', 'c_cups', 'd_cups', 'cracker_box', 'sugar_box', 'pudding_box'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x16056cb80>}}, 'randrot_noise_10simobj_dist_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 10, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10similarobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 5.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x16056ccd0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x16056cf70>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'e_cups', 'knife', 'fork', 'spoon', 'c_cups', 'd_cups', 'cracker_box', 'sugar_box', 'pudding_box'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x16056cfa0>}}, 'randomrot_rawnoise_10distinctobj_surf_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 5000, 'n_train_epochs': 3, 'n_eval_epochs': 10, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10distinctobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'surf_agent_sm': True, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160578130>, <tbp.monty.frameworks.environment_utils.transforms.AddNoiseToRawDepthImage object at 0x1605783d0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x160578460>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x160578490>}}, 'base_10multi_distinctobj_dist_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 14, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_10distinctobj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 5, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'hsv', 'principal_curvatures_log'], 'delta_thresholds': {'on_object': 0, 'n_steps': 20, 'hsv': [0.1, 0.1, 0.1], 'pose_vectors': [0.7853981633974483, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [2, 2], 'distance': 0.01}, 'save_raw_obs': False}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': False}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 10.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [True, True], 'zooms': [10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160578670>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x160578910>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': {'targets_list': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'source_object_list': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'num_distractors': 10}, 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0]), array([  0, 270,   0]), array([90,  0,  0]), array([ 90, 180,   0]), array([35, 45,  0]), array([325,  45,   0]), array([ 35, 315,   0]), array([325, 315,   0]), array([ 35, 135,   0]), array([325, 135,   0]), array([ 35, 225,   0]), array([325, 225,   0])]
         change every episode: None}}, 'surf_agent_unsupervised_10distinctobj': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': True, 'do_eval': False, 'show_sensor_output': False, 'max_train_steps': 4000, 'max_eval_steps': 500, 'max_total_steps': 4000, 'n_train_epochs': 10, 'n_eval_epochs': 3, 'model_name_or_path': '', 'min_lms_match': 1, 'seed': 42}, 'logging_config': {'monty_log_level': 'DETAILED', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>], 'wandb_handlers': [], 'python_log_level': 'INFO', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/monty_runs/', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': 'qlekeppu', 'wandb_group': 'debugging', 'log_parallel_wandb': False}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.graph_matching.MontyForGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 50, 'graph_delta_thresholds': {'patch': {'distance': 0.01, 'pose_vectors': [0.39269908169872414, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [1.0, 1.0], 'hsv': [0.1, 1, 1]}}, 'object_evidence_threshold': 100, 'required_symmetry_evidence': 20, 'max_nneighbors': 5}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.HabitatSurfacePatchSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log', 'gaussian_curvature', 'mean_curvature', 'gaussian_curvature_sc', 'mean_curvature_sc'], 'save_raw_obs': True}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicy'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': False, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 3, 'min_train_steps': 100, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160578a90>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x160578d30>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0]), array([  0, 270,   0]), array([90,  0,  0]), array([ 90, 180,   0]), array([35, 45,  0]), array([325,  45,   0]), array([ 35, 315,   0]), array([325, 315,   0]), array([ 35, 135,   0]), array([325, 135,   0]), array([ 35, 225,   0]), array([325, 225,   0])]
         change every episode: None}, 'train_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'train_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x160578d90>}}, 'surf_agent_unsupervised_10distinctobj_noise': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': True, 'do_eval': False, 'show_sensor_output': False, 'max_train_steps': 4000, 'max_eval_steps': 500, 'max_total_steps': 4000, 'n_train_epochs': 10, 'n_eval_epochs': 3, 'model_name_or_path': '', 'min_lms_match': 1, 'seed': 42}, 'logging_config': {'monty_log_level': 'DETAILED', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>], 'wandb_handlers': [], 'python_log_level': 'INFO', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/monty_runs/', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': 'qlekeppu', 'wandb_group': 'debugging', 'log_parallel_wandb': False}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.graph_matching.MontyForGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 50, 'graph_delta_thresholds': {'patch': {'distance': 0.01, 'pose_vectors': [0.39269908169872414, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [1.0, 1.0], 'hsv': [0.1, 1, 1]}}, 'object_evidence_threshold': 100, 'required_symmetry_evidence': 20, 'max_nneighbors': 5}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'surf_agent_sm': True, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicy'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': False, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 3, 'min_train_steps': 100, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160578f40>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x160587220>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0]), array([  0, 270,   0]), array([90,  0,  0]), array([ 90, 180,   0]), array([35, 45,  0]), array([325,  45,   0]), array([ 35, 315,   0]), array([325, 315,   0]), array([ 35, 135,   0]), array([325, 135,   0]), array([ 35, 225,   0]), array([325, 225,   0])]
         change every episode: None}, 'train_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'train_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x160587280>}}, 'surf_agent_unsupervised_10simobj': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': True, 'do_eval': False, 'show_sensor_output': False, 'max_train_steps': 4000, 'max_eval_steps': 500, 'max_total_steps': 4000, 'n_train_epochs': 10, 'n_eval_epochs': 3, 'model_name_or_path': '', 'min_lms_match': 1, 'seed': 42}, 'logging_config': {'monty_log_level': 'DETAILED', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>], 'wandb_handlers': [], 'python_log_level': 'INFO', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/monty_runs/', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': 'qlekeppu', 'wandb_group': 'debugging', 'log_parallel_wandb': False}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.graph_matching.MontyForGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 50, 'graph_delta_thresholds': {'patch': {'distance': 0.01, 'pose_vectors': [0.39269908169872414, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [1.0, 1.0], 'hsv': [0.1, 1, 1]}}, 'object_evidence_threshold': 100, 'required_symmetry_evidence': 20, 'max_nneighbors': 5}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.HabitatSurfacePatchSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log', 'gaussian_curvature', 'mean_curvature', 'gaussian_curvature_sc', 'mean_curvature_sc'], 'save_raw_obs': True}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicy'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': False, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 3, 'min_train_steps': 100, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160587430>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x1605876d0>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'spoon', 'strawberry', 'mustard_bottle', 'dice', 'golf_ball', 'c_lego_duplo', 'banana'], 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0]), array([  0, 270,   0]), array([90,  0,  0]), array([ 90, 180,   0]), array([35, 45,  0]), array([325,  45,   0]), array([ 35, 315,   0]), array([325, 315,   0]), array([ 35, 135,   0]), array([325, 135,   0]), array([ 35, 225,   0]), array([325, 225,   0])]
         change every episode: None}, 'train_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'train_dataloader_args': {'object_names': ['mug', 'e_cups', 'knife', 'fork', 'spoon', 'c_cups', 'd_cups', 'cracker_box', 'sugar_box', 'pudding_box'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x160587730>}}, 'base_77obj_dist_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 3, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_77obj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 5, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'hsv', 'principal_curvatures_log'], 'delta_thresholds': {'on_object': 0, 'n_steps': 20, 'hsv': [0.1, 0.1, 0.1], 'pose_vectors': [0.7853981633974483, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [2, 2], 'distance': 0.01}, 'save_raw_obs': False}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': False}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 5.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160587940>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x160587be0>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'master_chef_can', 'i_cups', 'spoon', 'b_cups', 'pitcher_base', 'knife', 'b_marbles', 'h_cups', 'strawberry', 'power_drill', 'padlock', 'golf_ball', 'hammer', 'softball', 'orange', 'c_lego_duplo', 'c_toy_airplane', 'b_lego_duplo', 'banana', 'nine_hole_peg_test', 'tomato_soup_can', 'baseball', 'g_cups', 'gelatin_box', 'lemon', 'plum', 'racquetball', 'plate', 'pudding_box', 'e_cups', 'apple', 'j_cups', 'foam_brick', 'large_marker', 'peach', 'phillips_screwdriver', 'a_toy_airplane', 'e_lego_duplo', 'sugar_box', 'a_colored_wood_blocks', 'c_cups', 'pear', 'f_cups', 'wood_block', 'd_lego_duplo', 'b_toy_airplane', 'b_colored_wood_blocks', 'g_lego_duplo', 'a_lego_duplo', 'mini_soccer_ball', 'medium_clamp', 'a_marbles', 'extra_large_clamp', 'd_cups', 'e_toy_airplane', 'adjustable_wrench', 'rubiks_cube', 'f_lego_duplo', 'a_cups', 'skillet_lid', 'sponge', 'tennis_ball', 'spatula', 'd_toy_airplane', 'chain', 'scissors', 'mustard_bottle', 'bleach_cleanser', 'tuna_fish_can', 'cracker_box', 'fork', 'large_clamp', 'dice', 'flat_screwdriver'], 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0])]
         change every episode: None}}, 'base_77obj_surf_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 5000, 'n_train_epochs': 3, 'n_eval_epochs': 3, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_77obj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 5, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'delta_thresholds': {'on_object': 0, 'n_steps': 20, 'hsv': [0.1, 0.1, 0.1], 'pose_vectors': [0.7853981633974483, 6.283185307179586, 6.283185307179586], 'principal_curvatures_log': [2, 2], 'distance': 0.01}, 'surf_agent_sm': True, 'save_raw_obs': False}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': False}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160587dc0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x1605910a0>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'master_chef_can', 'i_cups', 'spoon', 'b_cups', 'pitcher_base', 'knife', 'b_marbles', 'h_cups', 'strawberry', 'power_drill', 'padlock', 'golf_ball', 'hammer', 'softball', 'orange', 'c_lego_duplo', 'c_toy_airplane', 'b_lego_duplo', 'banana', 'nine_hole_peg_test', 'tomato_soup_can', 'baseball', 'g_cups', 'gelatin_box', 'lemon', 'plum', 'racquetball', 'plate', 'pudding_box', 'e_cups', 'apple', 'j_cups', 'foam_brick', 'large_marker', 'peach', 'phillips_screwdriver', 'a_toy_airplane', 'e_lego_duplo', 'sugar_box', 'a_colored_wood_blocks', 'c_cups', 'pear', 'f_cups', 'wood_block', 'd_lego_duplo', 'b_toy_airplane', 'b_colored_wood_blocks', 'g_lego_duplo', 'a_lego_duplo', 'mini_soccer_ball', 'medium_clamp', 'a_marbles', 'extra_large_clamp', 'd_cups', 'e_toy_airplane', 'adjustable_wrench', 'rubiks_cube', 'f_lego_duplo', 'a_cups', 'skillet_lid', 'sponge', 'tennis_ball', 'spatula', 'd_toy_airplane', 'chain', 'scissors', 'mustard_bottle', 'bleach_cleanser', 'tuna_fish_can', 'cracker_box', 'fork', 'large_clamp', 'dice', 'flat_screwdriver'], 'object_init_sampler': PredefinedObjectInitializer with params:
         positions: [[0.0, 1.5, 0.0]]
         rotations: [array([0, 0, 0]), array([ 0, 90,  0]), array([  0, 180,   0])]
         change every episode: None}}, 'randrot_noise_77obj_surf_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 5000, 'n_train_epochs': 3, 'n_eval_epochs': 3, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_77obj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.025}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'object_coverage', 'min_depth', 'mean_depth', 'hsv', 'principal_curvatures', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'surf_agent_sm': True, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.SurfacePolicyCurvatureInformed'>, 'policy_args': {'action_sampler_args': {'actions': [<class 'tbp.monty.frameworks.actions.actions.MoveForward'>, <class 'tbp.monty.frameworks.actions.actions.MoveTangentially'>, <class 'tbp.monty.frameworks.actions.actions.OrientHorizontal'>, <class 'tbp.monty.frameworks.actions.actions.OrientVertical'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.025, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25, 'alpha': 0.1, 'pc_alpha': 0.5, 'max_pc_bias_steps': 32, 'min_general_steps': 8, 'min_heading_steps': 12}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.1], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.03]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0], 'action_space_type': 'surface_agent'}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x1605911f0>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x160591490>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'master_chef_can', 'i_cups', 'spoon', 'b_cups', 'pitcher_base', 'knife', 'b_marbles', 'h_cups', 'strawberry', 'power_drill', 'padlock', 'golf_ball', 'hammer', 'softball', 'orange', 'c_lego_duplo', 'c_toy_airplane', 'b_lego_duplo', 'banana', 'nine_hole_peg_test', 'tomato_soup_can', 'baseball', 'g_cups', 'gelatin_box', 'lemon', 'plum', 'racquetball', 'plate', 'pudding_box', 'e_cups', 'apple', 'j_cups', 'foam_brick', 'large_marker', 'peach', 'phillips_screwdriver', 'a_toy_airplane', 'e_lego_duplo', 'sugar_box', 'a_colored_wood_blocks', 'c_cups', 'pear', 'f_cups', 'wood_block', 'd_lego_duplo', 'b_toy_airplane', 'b_colored_wood_blocks', 'g_lego_duplo', 'a_lego_duplo', 'mini_soccer_ball', 'medium_clamp', 'a_marbles', 'extra_large_clamp', 'd_cups', 'e_toy_airplane', 'adjustable_wrench', 'rubiks_cube', 'f_lego_duplo', 'a_cups', 'skillet_lid', 'sponge', 'tennis_ball', 'spatula', 'd_toy_airplane', 'chain', 'scissors', 'mustard_bottle', 'bleach_cleanser', 'tuna_fish_can', 'cracker_box', 'fork', 'large_clamp', 'dice', 'flat_screwdriver'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x1605914c0>}}, 'randrot_noise_77obj_dist_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 3, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/surf_agent_1lm_77obj/pretrained/', 'min_lms_match': 1, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 5.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': None, 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False], 'zooms': [10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160591610>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x1605918b0>], 'rng': None}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'master_chef_can', 'i_cups', 'spoon', 'b_cups', 'pitcher_base', 'knife', 'b_marbles', 'h_cups', 'strawberry', 'power_drill', 'padlock', 'golf_ball', 'hammer', 'softball', 'orange', 'c_lego_duplo', 'c_toy_airplane', 'b_lego_duplo', 'banana', 'nine_hole_peg_test', 'tomato_soup_can', 'baseball', 'g_cups', 'gelatin_box', 'lemon', 'plum', 'racquetball', 'plate', 'pudding_box', 'e_cups', 'apple', 'j_cups', 'foam_brick', 'large_marker', 'peach', 'phillips_screwdriver', 'a_toy_airplane', 'e_lego_duplo', 'sugar_box', 'a_colored_wood_blocks', 'c_cups', 'pear', 'f_cups', 'wood_block', 'd_lego_duplo', 'b_toy_airplane', 'b_colored_wood_blocks', 'g_lego_duplo', 'a_lego_duplo', 'mini_soccer_ball', 'medium_clamp', 'a_marbles', 'extra_large_clamp', 'd_cups', 'e_toy_airplane', 'adjustable_wrench', 'rubiks_cube', 'f_lego_duplo', 'a_cups', 'skillet_lid', 'sponge', 'tennis_ball', 'spatula', 'd_toy_airplane', 'chain', 'scissors', 'mustard_bottle', 'bleach_cleanser', 'tuna_fish_can', 'cracker_box', 'fork', 'large_clamp', 'dice', 'flat_screwdriver'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x1605918e0>}}, 'randrot_noise_77obj_5lms_dist_agent': {'experiment_class': <class 'tbp.monty.frameworks.experiments.object_recognition_experiments.MontyObjectRecognitionExperiment'>, 'experiment_args': {'do_train': False, 'do_eval': True, 'show_sensor_output': False, 'max_train_steps': 1000, 'max_eval_steps': 500, 'max_total_steps': 6000, 'n_train_epochs': 3, 'n_eval_epochs': 1, 'model_name_or_path': '/Users/<USER>/tbp/results/monty/pretrained_models/pretrained_ycb_v10/supervised_pre_training_5lms_all_objects/pretrained/', 'min_lms_match': 3, 'seed': 42, 'python_log_level': 'DEBUG'}, 'logging_config': {'monty_log_level': 'BASIC', 'monty_handlers': [<class 'tbp.monty.frameworks.loggers.monty_handlers.BasicCSVStatsHandler'>, <class 'tbp.monty.frameworks.loggers.monty_handlers.ReproduceEpisodeHandler'>], 'wandb_handlers': [<class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbTableStatsHandler'>, <class 'tbp.monty.frameworks.loggers.wandb_handlers.BasicWandbChartStatsHandler'>], 'python_log_level': 'WARNING', 'python_log_to_file': True, 'python_log_to_stdout': True, 'output_dir': '/Users/<USER>/tbp/results/monty/projects/evidence_eval_runs/logs', 'run_name': '', 'resume_wandb_run': False, 'wandb_id': '63tkpa50', 'wandb_group': 'benchmark_experiments', 'log_parallel_wandb': True}, 'monty_config': {'monty_class': <class 'tbp.monty.frameworks.models.evidence_matching.MontyForEvidenceGraphMatching'>, 'learning_module_configs': {'learning_module_0': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_0': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}, 'learning_module_1': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_1': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}, 'learning_module_2': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_2': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}, 'learning_module_3': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_3': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}, 'learning_module_4': {'learning_module_class': <class 'tbp.monty.frameworks.models.evidence_matching.EvidenceGraphLM'>, 'learning_module_args': {'max_match_distance': 0.01, 'tolerances': {'patch_4': {'hsv': array([0.1, 0.2, 0.2]), 'principal_curvatures_log': array([1., 1.])}}, 'feature_weights': {'patch': {'hsv': array([1. , 0.5, 0.5])}}, 'x_percent_threshold': 20, 'max_nneighbors': 10, 'evidence_update_threshold': '80%', 'max_graph_size': 0.3, 'num_model_voxels_per_dim': 100, 'gsg_class': <class 'tbp.monty.frameworks.models.goal_state_generation.EvidenceGoalStateGenerator'>, 'gsg_args': {'goal_tolerances': {'location': 0.015}, 'elapsed_steps_factor': 10, 'min_post_goal_success_steps': 5, 'x_percent_scale_factor': 0.75, 'desired_object_distance': 0.03}}}}, 'sensor_module_configs': {'sensor_module_0': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_0', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_1': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_1', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_2': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_2', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_3': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_3', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_4': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.FeatureChangeSM'>, 'sensor_module_args': {'sensor_module_id': 'patch_4', 'features': ['pose_vectors', 'pose_fully_defined', 'on_object', 'hsv', 'principal_curvatures_log'], 'save_raw_obs': False, 'delta_thresholds': {'on_object': 0, 'distance': 0.01}, 'noise_params': {'features': {'pose_vectors': 2, 'hsv': 0.1, 'principal_curvatures_log': 0.1, 'pose_fully_defined': 0.01}, 'location': 0.002}}}, 'sensor_module_5': {'sensor_module_class': <class 'tbp.monty.frameworks.models.sensor_modules.DetailedLoggingSM'>, 'sensor_module_args': {'sensor_module_id': 'view_finder', 'save_raw_obs': True}}}, 'motor_system_config': {'motor_system_class': <class 'tbp.monty.frameworks.models.motor_system.MotorSystem'>, 'motor_system_args': {'policy_class': <class 'tbp.monty.frameworks.models.motor_policies.InformedPolicy'>, 'policy_args': {'action_sampler_args': {'rotation_degrees': 5.0, 'actions': [<class 'tbp.monty.frameworks.actions.actions.LookUp'>, <class 'tbp.monty.frameworks.actions.actions.LookDown'>, <class 'tbp.monty.frameworks.actions.actions.TurnLeft'>, <class 'tbp.monty.frameworks.actions.actions.TurnRight'>, <class 'tbp.monty.frameworks.actions.actions.SetAgentPose'>, <class 'tbp.monty.frameworks.actions.actions.SetSensorRotation'>]}, 'action_sampler_class': <class 'tbp.monty.frameworks.actions.action_samplers.ConstantSampler'>, 'agent_id': 'agent_id_0', 'file_name': None, 'good_view_percentage': 0.5, 'desired_object_distance': 0.03, 'use_goal_state_driven_actions': True, 'switch_frequency': 1.0, 'min_perc_on_obj': 0.25}}}, 'sm_to_agent_dict': {'patch_0': 'agent_id_0', 'patch_1': 'agent_id_0', 'patch_2': 'agent_id_0', 'patch_3': 'agent_id_0', 'patch_4': 'agent_id_0', 'view_finder': 'agent_id_0'}, 'sm_to_lm_matrix': [[0], [1], [2], [3], [4]], 'lm_to_lm_matrix': None, 'lm_to_lm_vote_matrix': [[1, 2, 3, 4], [0, 2, 3, 4], [0, 1, 3, 4], [0, 1, 2, 4], [0, 1, 2, 3]], 'monty_args': {'num_exploratory_steps': 1000, 'min_eval_steps': 20, 'min_train_steps': 3, 'max_total_steps': 2500}}, 'dataset_class': <class 'tbp.monty.frameworks.environments.embodied_data.EnvironmentDataset'>, 'dataset_args': {'env_init_func': <class 'tbp.monty.simulators.habitat.environment.HabitatEnvironment'>, 'env_init_args': {'agents': [{'agent_type': <class 'tbp.monty.simulators.habitat.agents.MultiSensorAgent'>, 'agent_args': {'agent_id': 'agent_id_0', 'sensor_ids': ['patch_0', 'patch_1', 'patch_2', 'patch_3', 'patch_4', 'view_finder'], 'height': 0.0, 'position': [0.0, 1.5, 0.2], 'resolutions': [[64, 64], [64, 64], [64, 64], [64, 64], [64, 64], [64, 64]], 'positions': [[0.0, 0.0, 0.0], [0.0, 0.01, 0.0], [0.0, -0.01, 0.0], [0.01, 0.0, 0.0], [-0.01, 0.0, 0.0], [0.0, 0.0, 0.0]], 'rotations': [[1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0], [1.0, 0.0, 0.0, 0.0]], 'semantics': [False, False, False, False, False, False], 'zooms': [10.0, 10.0, 10.0, 10.0, 10.0, 1.0]}}], 'objects': [{'name': 'coneSolid', 'position': (0.0, 1.5, -0.1), 'rotation': (1.0, 0.0, 0.0, 0.0), 'scale': (1.0, 1.0, 1.0), 'semantic_id': None, 'enable_physics': False, 'object_to_avoid': False, 'primary_target_bb': None}], 'scene_id': None, 'seed': 42, 'data_path': '/Users/<USER>/tbp/data/habitat/objects/ycb'}, 'transform': [<tbp.monty.frameworks.environment_utils.transforms.MissingToMaxDepth object at 0x160591a30>, <tbp.monty.frameworks.environment_utils.transforms.DepthTo3DLocations object at 0x160591d30>]}, 'eval_dataloader_class': <class 'tbp.monty.frameworks.environments.embodied_data.InformedEnvironmentDataLoader'>, 'eval_dataloader_args': {'object_names': ['mug', 'bowl', 'potted_meat_can', 'master_chef_can', 'i_cups', 'spoon', 'b_cups', 'pitcher_base', 'knife', 'b_marbles', 'h_cups', 'strawberry', 'power_drill', 'padlock', 'golf_ball', 'hammer', 'softball', 'orange', 'c_lego_duplo', 'c_toy_airplane', 'b_lego_duplo', 'banana', 'nine_hole_peg_test', 'tomato_soup_can', 'baseball', 'g_cups', 'gelatin_box', 'lemon', 'plum', 'racquetball', 'plate', 'pudding_box', 'e_cups', 'apple', 'j_cups', 'foam_brick', 'large_marker', 'peach', 'phillips_screwdriver', 'a_toy_airplane', 'e_lego_duplo', 'sugar_box', 'a_colored_wood_blocks', 'c_cups', 'pear', 'f_cups', 'wood_block', 'd_lego_duplo', 'b_toy_airplane', 'b_colored_wood_blocks', 'g_lego_duplo', 'a_lego_duplo', 'mini_soccer_ball', 'medium_clamp', 'a_marbles', 'extra_large_clamp', 'd_cups', 'e_toy_airplane', 'adjustable_wrench', 'rubiks_cube', 'f_lego_duplo', 'a_cups', 'skillet_lid', 'sponge', 'tennis_ball', 'spatula', 'd_toy_airplane', 'chain', 'scissors', 'mustard_bottle', 'bleach_cleanser', 'tuna_fish_can', 'cracker_box', 'fork', 'large_clamp', 'dice', 'flat_screwdriver'], 'object_init_sampler': <tbp.monty.frameworks.config_utils.make_dataset_configs.RandomRotationObjectInitializer object at 0x160591d90>}}}
CONFIGS is not a list. Type: <class 'dict'>
wandb: WARNING Path ~/tbp/results/monty/wandb/ wasn't writable, using system temp directory.
wandb: WARNING Path ~/tbp/results/monty/wandb/ wasn't writable, using system temp directory
wandb: WARNING Path ~/tbp/results/monty/wandb/ wasn't writable, using system temp directory
wandb: (1) Create a W&B account
wandb: (2) Use an existing W&B account
wandb: (3) Don't visualize my results
wandb: Enter your choice: 3
wandb: You chose "Don't visualize my results"
wandb: Tracking run with wandb version 0.16.6
wandb: W&B syncing is set to `offline` in this directory.
wandb: Run `wandb online` or set WANDB_MODE=online to enable cloud syncing.
---------evaluating---------
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/buffer.py:578: RuntimeWarning: invalid value encountered in multiply
  new_vals = np.empty((len(self) + 1, new_val_len)) * np.nan
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/buffer.py:578: RuntimeWarning: invalid value encountered in multiply
  new_vals = np.empty((len(self) + 1, new_val_len)) * np.nan
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/evidence_matching.py:629: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  r_euler = mlh["rotation"].inv().as_euler("xyz", degrees=True)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/utils/logging_utils.py:767: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  last_mlh["rotation"].inv().as_euler("xyz", degrees=True)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/evidence_matching.py:629: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  r_euler = mlh["rotation"].inv().as_euler("xyz", degrees=True)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/utils/logging_utils.py:767: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  last_mlh["rotation"].inv().as_euler("xyz", degrees=True)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/evidence_matching.py:629: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  r_euler = mlh["rotation"].inv().as_euler("xyz", degrees=True)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/utils/logging_utils.py:767: UserWarning: Gimbal lock detected. Setting third angle to zero since it is not possible to uniquely determine all angles.
  last_mlh["rotation"].inv().as_euler("xyz", degrees=True)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/buffer.py:578: RuntimeWarning: invalid value encountered in multiply
  new_vals = np.empty((len(self) + 1, new_val_len)) * np.nan
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/Users/<USER>/Downloads/tbp.monty-main/src/tbp/monty/frameworks/models/buffer.py:578: RuntimeWarning: invalid value encountered in multiply
  new_vals = np.empty((len(self) + 1, new_val_len)) * np.nan
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/fromnumeric.py:3432: RuntimeWarning: Mean of empty slice.
  return _methods._mean(a, axis=axis, dtype=dtype,
/opt/anaconda3/envs/tbp.monty/lib/python3.8/site-packages/numpy/core/_methods.py:190: RuntimeWarning: invalid value encountered in double_scalars
  ret = ret.dtype.type(ret / rcount)
wandb:
wandb:
wandb: Run history:
wandb: LM_0/episode/individual_ts_rotation_error ▁▁▁▁▁▁▁▁▁▁▃▁▁▁▁▃█▁▁▄▂▂▂▆▁▂▂▂▂▄▅▃▁▁▅▂▁▇▁▁
wandb:       LM_0/episode/steps_to_individual_ts ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▃▁▁▁█▁▁▁▁▁▁▁▁▁▁▂▁▁
wandb:                          episode/confused ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                      episode/confused_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                           episode/correct ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                       episode/correct_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█▁▁▁▁▁▁▁▁▁█▁▁▁
wandb:           episode/goal_state_success_rate ▆▁███████▃███▆▆▇█▅███▇▆▁██▁▆▇▁█████▆▆▅█▆
wandb:             episode/goal_states_attempted ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▃▁▁▁▁▂▂▁▁▂▁▂▁█▁▁▁▁▁▂▁█▁▁▁
wandb:                          episode/lm_steps ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▂▁█▁▄▁▁▁▁▁▁▁█▁▁▁
wandb:          episode/mean_lm_steps_to_indv_ts ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▃▁▁▁▁▂▂▁▁▂▁ ▁█▁▁▁▁▁▂▁ ▁▁▁
wandb:              episode/monty_matching_steps ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▂▁█▁▄▁▁▁▁▁▁▁█▁▁▁
wandb:                       episode/monty_steps ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▂▁▆▁▄▁▁▁▁▁▁▁█▁▁▁
wandb:                          episode/no_match ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                     episode/pose_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                    episode/rotation_error ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▁▂▁▁▂▂▁▁▁█▁▁▁
wandb:                          episode/run_time ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▂▂▁▁▁▂▁▁▂▁▇▁▅▁▁▂▂▁▁▂█▁▁▁
wandb:                 episode/symmetry_evidence █▁▁█▁██▁█▁▁▁██████████▁▁█▁▁▁█▁▁██▁██▁▁▁█
wandb:                          episode/time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           episode/used_mlh_after_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁█▁▁▁▁▁▁▁▁▁█▁▁▁
wandb:              overall/avg_episode_run_time ▁▂▁▁▁▂▂▂▂▂▂▂▃▃▃▄▄▄▄▄▅▅▅▅▅▆▆▆▇▇▇▇▇▇▇▇▇▇██
wandb:                  overall/avg_num_lm_steps ▁▁▁▁▁▂▂▂▂▂▂▂▃▃▃▅▅▄▅▄▄▄▄▅▅▆▇▇▇▇▇▇▇▇▇▇▇▇██
wandb:      overall/avg_num_monty_matching_steps ▁▁▁▁▁▂▂▂▂▂▂▂▃▃▃▅▅▄▅▄▄▄▄▅▅▆▇▇▇▇▇▇▇▇▇▇▇▇██
wandb:               overall/avg_num_monty_steps ▁▁▁▁▁▂▂▂▂▂▂▂▅▄▄▅▅▅▅▅▅▄▄▅▅▅▆▆▆▇▇▇▇▇▇▇▇▇██
wandb:                overall/avg_rotation_error ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▂▃▃▄▄▄▄▄▄▅▅▅▅▅▆▆▆▆▆▆▇▇██
wandb:                      overall/num_episodes ▁▁▁▁▂▂▂▂▂▃▃▃▃▃▃▄▄▄▄▄▅▅▅▅▅▅▆▆▆▆▆▇▇▇▇▇▇███
wandb:                  overall/percent_confused ▁▁▁▁▁▁▁▁▁▁▁█▇▇▇▆▆▆▅▅▅▅▅▄▄▄▄▄▇▆▆▆▆▆▆▅▅▅▅▅
wandb:              overall/percent_confused_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_confused_per_lm ▁▁▁▁▁▁▁▁▁▁▁█▇▇▇▆▆▆▅▅▅▅▅▄▄▄▄▄▇▆▆▆▆▆▆▅▅▅▅▅
wandb:                   overall/percent_correct ███████████▁▂▂▂▃▃▃▄▄▄▄▄▅▅▅▅▅▂▃▃▃▃▃▃▄▄▄▄▄
wandb:               overall/percent_correct_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▅▅▅▆▆▅▅▅▅▅▄▅▅▆▇▇▆▇▇▇▆▇▇▇▇▇██
wandb:            overall/percent_correct_per_lm ███████████▁▂▂▂▃▃▃▄▄▄▄▄▅▅▅▅▅▂▃▃▃▃▃▃▄▄▄▄▄
wandb:                  overall/percent_no_match ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_no_match_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:             overall/percent_pose_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:      overall/percent_pose_time_out_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                  overall/percent_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_time_out_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:    overall/percent_used_mlh_after_timeout ▁▁▁▁▁▁▁▁▁▁▁▁▅▅▅▆▆▅▅▅▅▅▄▅▅▆▇▇▆▇▇▇▆▇▇▇▇▇██
wandb:                          overall/run_time ▁▁▁▁▁▁▂▂▂▂▂▂▂▂▂▃▃▃▃▃▄▄▄▄▄▅▅▅▅▆▆▆▆▇▇▇▇▇██
wandb:
wandb: Run summary:
wandb:                       episode/confused 0
wandb:                   episode/confused_mlh 0
wandb:                        episode/correct 1
wandb:                    episode/correct_mlh 1
wandb:        episode/goal_state_success_rate 0.55556
wandb:          episode/goal_states_attempted 9
wandb:                       episode/lm_steps 500
wandb:       episode/mean_lm_steps_to_indv_ts nan
wandb:           episode/monty_matching_steps 500
wandb:                    episode/monty_steps 2770
wandb:                       episode/no_match 0
wandb:                  episode/pose_time_out 0
wandb:                 episode/rotation_error 3.0391
wandb:                       episode/run_time 1104.87938
wandb:              episode/symmetry_evidence 0.0
wandb:                       episode/time_out 0
wandb:        episode/used_mlh_after_time_out 1
wandb:           overall/avg_episode_run_time 12.17949
wandb:               overall/avg_num_lm_steps 52.28139
wandb:   overall/avg_num_monty_matching_steps 52.28139
wandb:            overall/avg_num_monty_steps 411.35065
wandb:             overall/avg_rotation_error 0.09684
wandb:                   overall/num_episodes 231
wandb:               overall/percent_confused 0.8658
wandb:           overall/percent_confused_mlh 0.0
wandb:        overall/percent_confused_per_lm 0.8658
wandb:                overall/percent_correct 99.1342
wandb:            overall/percent_correct_mlh 5.19481
wandb:         overall/percent_correct_per_lm 99.1342
wandb:               overall/percent_no_match 0.0
wandb:        overall/percent_no_match_per_lm 0.0
wandb:          overall/percent_pose_time_out 0.0
wandb:   overall/percent_pose_time_out_per_lm 0.0
wandb:               overall/percent_time_out 0.0
wandb:        overall/percent_time_out_per_lm 0.0
wandb: overall/percent_used_mlh_after_timeout 5.19481
wandb:                       overall/run_time 2813.46184
wandb:
wandb: You can sync this run to the cloud by running:
wandb: wandb sync /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250422_124448-63tkpa50
wandb: Find logs at: /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250422_124448-63tkpa50/logs
(tbp.monty) jiangzixi@jiangzixideMacBook-Air benchmarks %
