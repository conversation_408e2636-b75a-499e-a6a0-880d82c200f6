---
title: Ways to Contribute to Code
---
> [!NOTE]
>
> For an architecture overview see the [Architecture Overview](../overview/architecture-overview.md) page. Each of the major components in the architecture can be customized. For more information on how to customize different modules, please see our guide on [Customizing Monty](../how-to-use-monty/customizing-monty.md).

There are many ways in which you can contribute to the code. The list below is not comprehensive but might give you some ideas.

- **Create a Custom Sensor Module**: Sensor Modules are the interface between the real-world sensors and Monty. If you have a specific sensor that you would like <PERSON> to support, consider contributing a Sensor Module for it. Also, if you have a good idea of how to extract useful features from a raw stream of data, this can be integrated as a new Sensor Module. 
- **Create a Custom Learning Module**: Learning Modules are the heart of Monty. They are the repeating modeling units that can learn from a stream of sensorimotor data and use their internal models to recognize objects and suggest actions. What exactly happens inside a Learning Module is not prescribed by <PERSON>. We have some suggestions, but you may have a lot of other ideas. As long as a Learning Module adheres to the Cortical Message Protocol and implements the abstract functions defined [here](../../src/tbp/monty/frameworks/models/abstract_monty_classes.py), it can be used in Monty. It would be great to see many ideas for Learning Modules in this code base that we can test and compare. For information on how to implement a custom Learning Module, see our guide on [Customizing Monty](../how-to-use-monty/customizing-monty.md).
- **Write a Custom Motor Policy**: Monty is a sensorimotor system, which means that action selection and execution are important aspects. Model-based action policies are implemented within the Learning Module's [Goal State Generator](../../src/tbp/monty/frameworks/models/goal_state_generation.py), but model-free ones, as well as the execution of the suggested actions from the Learning Modules, are implemented in the motor system. Our Thousand Brains Project team doesn't have much in-house robotics experience so we value contributions from people who do.
- **Add Support for More Environments**: If you know of other environments that would be interesting to test Monty in (whether you designed it or it is a common benchmark environment) you can add a custom `EnvironmentDataset` and/or `EnvironmentDataLoader` to support this environment.
- **Improve the Code Infrastructure**: Making the code easier to read and understand is a high priority for us, and we are grateful for your help. If you have ideas on how to refactor or document the code to improve this, consider contributing. We also appreciate help on making our unit test suite more comprehensive. Please [create an RFC](./request-for-comments-rfc.md) before working on any major code refactor.
- **Optimize the Code**: We are always looking for ways to run our algorithms faster and more efficiently, and we appreciate your ideas on that. Just like the previous point, PRs around this should not change anything in the outputs of the system.
- **Add to our Benchmarks**: If you have ideas on how to test more capabilities of the system we appreciate if you add to our [benchmark experiments](../overview/benchmark-experiments.md). This could be evaluating different aspects in our current environments or adding completely new environments. Please note that in order to allow us to frequently run all the benchmark experiments, we only add one experiment for each specific capability we test and try to keep the run times reasonable.
- **Work on an open Issue**: If you came to our project and want to contribute code but are unsure of what, the [open Issues](https://github.com/thousandbrainsproject/tbp.monty/issues) are a good place to start.  See our guide on [how to identify an issue to work on](ways-to-contribute-to-code/identify-an-issue-to-work-on.md) for more information.

# How To Contribute Code

Monty integrates code changes using GitHub Pull Requests. To start contributing code to Monty, please consult the [Contributing Pull Requests](pull-requests.md) guide.