"""
实现无监督推理的证据图匹配的Monty类和学习模块
它包括两个主要类：MontyForNoResetEvidenceGraphMatching和NoResetEvidenceGraphLM
    1. MontyForNoResetEvidenceGraphMatching类
        功能：用于无监督推理实验，避免在episode之间重置Monty的内部状态。
        继承自：MontyForEvidenceGraphMatching
        属性：
        init_pre_episode：跟踪pre_episode是否至少被调用过一次。
        方法：
        pre_episode(primary_target, semantic_id_to_label)：在首次调用时执行完整的初始化，后续调用仅重置终端状态和步数计数器。
        _reset_modules_buffers()：重置学习模块和传感器模块的缓冲区以节省内存。

    2. NoResetEvidenceGraphLM类
        功能：在无监督推理中管理证据图学习模块，避免在对象交换时指数增长等待因子。
        继承自：TheoreticalLimitLMLoggingMixin和EvidenceGraphLM
        属性：
        last_location：记录上次观察到的位置。
        gsg.wait_growth_multiplier：设置为1，避免无监督信号时的指数增长。
        方法：
        reset()：重置证据和上次位置。
        _add_displacements(obs)：为当前观察添加位移向量。
        _agent_moved_since_reset()：判断代理自上次重置以来是否移动过。

实现无监督推理的证据图匹配：（EvidenceGraphLM的作用域）
    NoResetEvidenceGraphLM 类继承自 TheoreticalLimitLMLoggingMixin 和 EvidenceGraphLM，并在其基础上进行了修改，以适应无监督推理的场景。
    在 NoResetEvidenceGraphLM 的 __init__ 方法中，调用了 super().__init__(*args, **kwargs)，从而初始化了 EvidenceGraphLM 的部分。
    重写了 reset 方法，重置了 EvidenceGraphLM 的证据和上次位置。
    重写了 _add_displacements 方法，为当前观察添加位移向量，并更新 last_location。
    重写了 _agent_moved_since_reset 方法，判断代理自上次重置以来是否移动过。

修改内容：修改 MontyForNoResetEvidenceGraphMatching 和 NoResetEvidenceGraphLM 以利用新的概率匹配机制，增强无监督推理的能力

"""

from typing import List

import numpy as np

from tbp.monty.frameworks.models.evidence_matching import (
    EvidenceGraphLM,
    MontyForEvidenceGraphMatching,
)
from tbp.monty.frameworks.models.mixins.no_reset_evidence import (
    TheoreticalLimitLMLoggingMixin,
)
from tbp.monty.frameworks.models.states import State


class MontyForNoResetEvidenceGraphMatching(MontyForEvidenceGraphMatching):
    """Monty class for unsupervised inference without explicit episode resets.

    This variant of `MontyForEvidenceGraphMatching` is designed for unsupervised
    inference experiments where objects may change dynamically without any reset
    signal. Unlike standard experiments, this class avoids resetting Monty's
    internal state (e.g., hypothesis space, evidence scores) between episodes.

    This setup better reflects real-world conditions, where object boundaries
    are ambiguous and no supervisory signal is available to indicate when a new
    object appears. Only minimal state — such as step counters and termination
    flags — is reset to prevent buffers from accumulating across objects. Additionally,
    Monty is currently forced to switch to Matching state. Evaluation of unsupervised
    inference is performed over a fixed number of matching steps per object.

    *Intended for evaluation-only runs using pre-trained models, with Monty
    remaining in the matching phase throughout.*
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Track whether `pre_episode` has been called at least once.
        # There are two separate issues this helps avoid:
        #
        # 1. Some internal variables in SMs and LMs (e.g., `stepwise_targets_list`,
        #    `terminal_state`, `is_exploring`, `visited_locs`) are not initialized
        #    in `__init__`, but only inside `pre_episode`. Ideally, these should be
        #    initialized once in `__init__` and reset in `pre_episode`, but fixing
        #    this would require changes across multiple classes.
        #
        # 2. The order of operations: Graphs are loaded into LMs *after* the Monty
        #    object is constructed but *before* `pre_episode` is called. Some
        #    functions (e.g., in `EvidenceGraphLM`) depend on the graph being loaded to
        #    compute initial possible matches inside `pre_episode`, and this cannot
        #    be safely moved into `__init__`.
        #
        # As a workaround, we allow `pre_episode` to run normally once (to complete
        # required initialization), and skip full resets on subsequent calls.
        # TODO: Remove initialization logic from `pre_episode`
        self.init_pre_episode = False

    def pre_episode(self, primary_target, semantic_id_to_label=None):
        if not self.init_pre_episode:
            self.init_pre_episode = True
            return super().pre_episode(primary_target, semantic_id_to_label)

        # reset terminal state
        self._is_done = False
        self.reset_episode_steps()
        self.switch_to_matching_step()

        # keep target up-to-date for logging
        self.primary_target = primary_target
        self.semantic_id_to_label = semantic_id_to_label
        for lm in self.learning_modules:
            lm.primary_target = primary_target["object"]
            lm.primary_target_rotation_quat = primary_target["quat_rotation"]

        # reset LMs and SMs buffers to save memory
        self._reset_modules_buffers()

    def _reset_modules_buffers(self):
        """Resets buffers for LMs and SMs."""
        for lm in self.learning_modules:
            lm.buffer.reset()
        for sm in self.sensor_modules:
            sm.raw_observations = []
            sm.sm_properties = []
            sm.processed_obs = []

    ###新写的方法拿来用！！！
    def pre_episode(self, primary_target, semantic_id_to_label=None):
        if not self.init_pre_episode:
            self.init_pre_episode = True
            return super().pre_episode(primary_target, semantic_id_to_label)

        # 重置终端状态
        self._is_done = False
        self.reset_episode_steps()
        self.switch_to_matching_step()

        # 保持目标更新以用于日志记录
        self.primary_target = primary_target
        self.semantic_id_to_label = semantic_id_to_label
        for lm in self.learning_modules:
            lm.primary_target = primary_target["object"]
            lm.primary_target_rotation_quat = primary_target["quat_rotation"]

        # 重置 LMs 和 SMs 的缓冲区以节省内存
        self._reset_modules_buffers()

        # 提取观测子图
        observed_nodes = torch.tensor([0, 1, 2])  # 示例节点索引，实际中应根据观测数据确定
        for lm in self.learning_modules:
            if isinstance(lm, NoResetEvidenceGraphLM):
                obs_subgraph = lm.extract_observed_subgraph(observed_nodes)

                # 候选节点索引
                candidate_indices = torch.tensor([3, 4, 5])  # 示例候选节点索引

                # 计算匹配分数
                best_idx, confidence = lm.match_score(obs_subgraph, candidate_indices)
                print(f"最佳匹配节点索引: {best_idx}, 置信度: {confidence}")


class NoResetEvidenceGraphLM(TheoreticalLimitLMLoggingMixin, EvidenceGraphLM):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.last_location = {}

        # it does not make sense for the wait factor to exponentially
        # grow when objects are swapped without any supervisory signal.
        self.gsg.wait_growth_multiplier = 1

    def reset(self) -> None:
        super().reset()
        self.evidence = {}
        self.last_location = {}

    def _add_displacements(self, obs: List[State]) -> List[State]:
        """Add displacements to the current observation.

        For each input channel, this function computes the displacement vector by
        subtracting the current location from the last observed location. It then
        updates `self.last_location` for use in the next step. If any observation
        has a recorded previous location, we assume movement has occurred.

        In this unsupervised inference setting, the displacement is set to zero
        at the beginning of the first episode when the last location is not set.

        Args:
            obs (List[State]): A list of observations to which displacements will be
                added.

        Returns:
            obs (List[State]): The list of observations, each updated with a
                displacement vector.
        """
        for o in obs:
            if o.sender_id in self.last_location.keys():
                displacement = o.location - self.last_location[o.sender_id]
            else:
                displacement = np.zeros(3)
            o.set_displacement(displacement)
            self.last_location[o.sender_id] = o.location
        return obs

    def _agent_moved_since_reset(self):
        """Overwrites the logic of whether the agent has moved since the last reset.

        In unsupervised inference, the first movement is detected on the first
        episode only. If a `last_location` exists, then first movement has occurred.

        Returns:
            - Whether the agent has moved since the last reset.
        """
        return len(self.last_location) > 0

    ###新增的方法！！！EvidenceGraphLM的复用
    def extract_observed_subgraph(self, observed_nodes: torch.Tensor):
        """提取带概率信息的观测子图。"""
        sub_mean = self._graph.x_mean[observed_nodes]
        sub_var = self._graph.x_var[observed_nodes]
        node_map = {int(old): i for i, old in enumerate(observed_nodes.tolist())}
        mask = [(i, j) for i, j in self._graph.edge_index.T.tolist() if i in node_map and j in node_map]
        if mask:
            edge_index = torch.tensor([[node_map[i], node_map[j]] for i, j in mask]).T
            edge_prob = torch.tensor(
                [self._graph.edge_prob[k] for k, (i, j) in enumerate(self._graph.edge_index.T.tolist()) if
                 (i, j) in mask]
            )
        else:
            edge_index = torch.empty((2, 0), dtype=torch.long)
            edge_prob = torch.empty((0,))

        return {
            'x_mean': sub_mean,
            'x_var': sub_var,
            'edge_index': edge_index,
            'edge_prob': edge_prob
        }

    def match_score(self, obs_subgraph, candidate_indices):
        """使用KL散度和方差差距评估匹配。"""
        matched_scores = []
        for idx in candidate_indices:
            node_mean = self._graph.x_mean[idx]
            node_var = self._graph.x_var[idx]

            obs_mean = obs_subgraph['x_mean'][0]
            obs_var = obs_subgraph['x_var'][0]

            # 使用 KL 散度计算分布相似度
            kl = self.kl_divergence_gaussians(obs_mean, obs_var, node_mean, node_var)
            var_diff = torch.abs(obs_var - node_var).mean().item()

            node_score = -kl - var_diff  # 简化示意，可用更复杂方式加权
            matched_scores.append((idx, node_score))

        matched_scores.sort(key=lambda x: x[1], reverse=True)
        best_idx, best_score = matched_scores[0]
        return best_idx, torch.sigmoid(torch.tensor(best_score))  # 返回置信度形式

    def kl_divergence_gaussians(self, mu1: torch.Tensor, var1: torch.Tensor, mu2: torch.Tensor, var2: torch.Tensor) -> torch.Tensor:
        """计算两个多维高斯分布 N(mu1, var1) 和 N(mu2, var2) 之间的 KL 散度。"""
        term1 = torch.log(var2 / var1)
        term2 = (var1 + (mu1 - mu2) ** 2) / var2
        kl = 0.5 * torch.sum(term1 + term2 - 1)
        return kl