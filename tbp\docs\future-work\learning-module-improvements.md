---
title: Learning Module Improvements
description: Improvements we would like to add to the learning modules.
---
We have a guide on [customizing learning modules](learning-module-improvements/contributing-learning-modules.md) here.

These are the things we would like to implement:

- [Use off-object observations](learning-module-improvements/use-off-object-observations.md) #numsteps #multiobj
- [Implement and test rapid evidence decay as form of unsupervised memory resetting](learning-module-improvements/implement-and-test-rapid-evidence-decay-as-form-of-unsupervised-memory-resetting.md) #multiobj
- [Improve bounded evidence performance](learning-module-improvements/improve-bounded-evidence-performance.md) #multiobj
- [Use models with fewer points](learning-module-improvements/use-models-with-fewer-points.md) #speed #generalization
- [Make it possible to store multiple feature maps on one graph](learning-module-improvements/make-it-possible-to-store-multiple-feature-maps-on-one-graph.md) #featsandmorph
- [Test particle-filter-like resampling of hypothesis space](learning-module-improvements/test-particle-filter-like-resampling-of-hypothesis-space.md) #accuracy #speed
- [Re-anchor hypotheses for robustness to noise and distortions](learning-module-improvements/re-anchor-hypotheses.md) #deformations #noise #generalization
- [Less dependency on first observation](learning-module-improvements/less-dependency-on-first-observation.md) #noise #multiobj
- [Deal with incomplete models](learning-module-improvements/deal-with-incomplete-models.md) #learning
- [Implement & test GNNs to model object behaviors & states](learning-module-improvements/implement-test-gnns-to-model-object-behaviors-states.md) #dynamic
- [Deal with moving objects](learning-module-improvements/deal-with-moving-objects.md) #dynamic #realworld
- [Support scale invariance](learning-module-improvements/support-scale-invariance.md) #scale
- [Improve handling of symmetry](learning-module-improvements/improve-handling-of-symmetry.md) #pose
- [Use Better Priors for Hypothesis Initialization](learning-module-improvements/use-better-priors-for-hypothesis-initialization.md) #numsteps #pose #scale

!snippet[../snippets/contributing-tasks.md]