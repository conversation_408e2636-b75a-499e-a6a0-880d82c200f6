- Start Date: 2025-01-21
- RFC PR: https://github.com/thousandbrainsproject/tbp.monty/pull/148

The RFC process can be intimidating. The default template asks for a lot of information. An intimidating template combined with a bureaucratic process can discourage people from contributing.

This RFC proposes that we offer multiple templates for RFCs to convey the idea that not every template section is required. For example, `0000_minimal_template.md` and `0000_comprehensive_template.md`. The current template will become the `0000_comprehensive_template.md`. The minimal template will be:

```
- Start Date: (fill me in with today's date, YYYY-MM-DD)
- RFC PR: (leave this empty, it will be filled in after RFC is merged)

Content here.
```

Additionally, lets update the tone and content of the process documentation to reflect that the idea of the templates is to provide a starting point. The RFC can take the form appropriate to the idea being conveyed.

This RFC will become an example of a minimal RFC and we can use [RFC 4: Action Object](https://github.com/thousandbrainsproject/tbp.monty/blob/main/rfcs/0004_action_object.md) as an example of a comprehensive RFC.