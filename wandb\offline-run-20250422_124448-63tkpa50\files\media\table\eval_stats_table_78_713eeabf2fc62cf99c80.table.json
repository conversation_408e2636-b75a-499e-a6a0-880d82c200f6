{"columns": ["stepwise_target_object", "symmetry_evidence", "monty_matching_steps", "monty_steps", "location_rel_body", "num_steps", "detected_rotation", "detected_scale", "individual_ts_rotation_error", "highest_evidence", "most_likely_object", "mean_objects_per_graph", "TFNP", "stepwise_performance", "time", "primary_target_object", "goal_states_attempted", "num_possible_matches", "goal_state_achieved", "rotation_error", "individual_ts_performance", "lm_id", "most_likely_location", "primary_target_rotation_euler", "mean_graphs_per_object", "result", "primary_target_position", "individual_ts_reached_at_step", "primary_target_rotation_quat", "primary_target_scale", "primary_performance", "detected_location", "most_likely_rotation"], "data": [["mug", 0, 21, 145, [0.048278657670249914, 1.5255842113998312, 0.000659535022124064], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 32.19389246122554, "mug", 1.0, "target_in_possible_matches_(TP)", "correct", 3.1574110984802246, "mug", 3, 1, 3, 0.0, "correct", "LM_0", [0.04827869784446947, 1.5255843390296748, 0.0006595213846287335], [0, 0, 0], 1.0, "mug", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.04827869784446947, 1.5255843390296748, 0.0006595213846287335], [-1.2613639698555097e-09, -1.414141657471365e-07, -3.4677215954426507e-09]], ["mug", 5, 28, 171, [0.017099367796117256, 1.526288022269123, 0.07252900867473087], 28, [0.0, 0.0, 0.0], 1.0, 0.0, 39.93795051765593, "bowl", 1.0, "target_in_possible_matches_(TP)", "confused", 3.489279270172119, "bowl", 4, 1, 3, 0.0, "correct", "LM_0", [0.01709874844364522, 1.5262877823110497, 0.07252894416912768], [0, 0, 0], 1.0, "bowl", [0.0, 1.5, 0.0], 28, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.01709874844364522, 1.5262877823110497, 0.07252894416912768], [6.978751307343297e-07, 1.7226730182351677e-07, -2.8359716024226857e-07]], ["mug", 0, 21, 92, [0.046650397961418635, 1.4634979150237282, -0.012595343481950974], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 24.933970799296688, "potted_meat_can", 1.0, "target_in_possible_matches_(TP)", "confused", 6.493534326553345, "potted_meat_can", 2, 1, 2, 0.0, "correct", "LM_0", [0.0466503971397864, 1.4634979587491566, -0.012595344189527992], [0, 0, 0], 1.0, "potted_meat_can", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.0466503971397864, 1.4634979587491566, -0.012595344189527992], [1.5923807169492286e-07, -3.818497617252743e-08, 1.9577323012240975e-06]], ["mug", 0, 21, 134, [-0.04670310315014935, 1.4648881846948136, -0.01747181344030455], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 31.665217320797964, "master_chef_can", 1.0, "target_in_possible_matches_(TP)", "confused", 2.6377551555633545, "master_chef_can", 3, 1, 1, 0.0, "correct", "LM_0", [-0.04670856835808608, 1.4648853329989062, -0.017471676128368516], [0, 0, 0], 1.0, "master_chef_can", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.04670856835808608, 1.4648853329989062, -0.017471676128368516], [-9.732044985379409e-09, 1.4512756511549494e-08, 5.2308631053121705e-08]], ["mug", 5, 26, 175, [-0.04269878762702425, 1.5375154339290544, -0.011442031927366833], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 40.302988965298226, "i_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 2.8219077587127686, "i_cups", 4, 1, 4, 0.0, "correct", "LM_0", [-0.042700593155055026, 1.537517373946107, -0.01144203696830286], [0, 0, 0], 1.0, "i_cups", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.042700593155055026, 1.537517373946107, -0.01144203696830286], [3.150893572322168e-08, -2.7146504928383972e-08, -7.2058910034345045e-09]], ["mug", 0, 24, 142, [0.08114656764585267, 1.5007467485951698, 0.03918786097125007], 24, [0.0, 0.0, 0.0], 1.0, 0.0, 44.85396477738898, "spoon", 1.0, "target_in_possible_matches_(TP)", "confused", 3.3505001068115234, "spoon", 3, 1, 0, 0.0, "correct", "LM_0", [0.08115347375403746, 1.500694834429485, 0.03913170907242402], [0, 0, 0], 1.0, "spoon", [0.0, 1.5, 0.0], 24, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.08115347375403746, 1.500694834429485, 0.03913170907242402], [3.238950197556162e-07, -1.1112985279220786e-09, 8.710127520444519e-09]], ["mug", 5, 27, 186, [0.029921574269422477, 1.5307884479924598, 5.65324073414739e-05], 27, [0.0, 0.0, 0.0], 1.0, 0.0, 39.7510521633753, "b_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 2.929058074951172, "b_cups", 4, 1, 4, 0.0, "correct", "LM_0", [0.029921242585189665, 1.530788189106696, 5.652351681443299e-05], [0, 0, 0], 1.0, "b_cups", [0.0, 1.5, 0.0], 27, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.029921242585189665, 1.530788189106696, 5.652351681443299e-05], [-4.3854034740478e-08, 1.4506789790967068e-08, 2.2723548510264056e-08]], ["mug", 0, 21, 143, [-0.06926462090286849, 1.5904820742147538, 0.05219117172008219], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 32.74147845721651, "pitcher_base", 1.0, "target_in_possible_matches_(TP)", "confused", 2.295232057571411, "pitcher_base", 3, 1, 3, 0.0, "correct", "LM_0", [-0.06926268455539607, 1.5904807014133693, 0.052190912693183676], [0, 0, 0], 1.0, "pitcher_base", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.06926268455539607, 1.5904807014133693, 0.052190912693183676], [2.758725620563841e-08, -1.035721933136768e-07, -1.0227456002932006e-08]], ["mug", 0, 21, 120, [0.09669261846134418, 1.5107198938022095, 0.004474822159162554], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 34.67410902521489, "knife", 1.0, "target_in_possible_matches_(TP)", "confused", 2.2320079803466797, "knife", 3, 1, 0, 0.0, "correct", "LM_0", [0.09668441981628999, 1.510719869793297, 0.00447514658180902], [0, 0, 0], 1.0, "knife", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.09668441981628999, 1.510719869793297, 0.00447514658180902], [-1.6369901593387637e-07, 1.2261876182367141e-06, -1.534748308835367e-07]], ["mug", 5, 26, 148, [-0.007848392379279081, 1.49351651526853, -0.014162021583061426], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 34.82293547201873, "b_marbles", 1.0, "target_in_possible_matches_(TP)", "confused", 3.4912590980529785, "b_marbles", 4, 1, 4, 0.0, "correct", "LM_0", [-0.007847781340527781, 1.493517350414534, -0.014162004251139663], [0, 0, 0], 1.0, "b_marbles", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.007847781340527781, 1.493517350414534, -0.014162004251139663], [-2.739911952083711e-10, 2.7320659380522187e-08, -2.000562264121761e-09]], ["mug", 5, 26, 224, [-0.044738551764022534, 1.5342375949939515, 0.008428708146116026], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 42.478306590819734, "h_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 3.2683610916137695, "h_cups", 4, 1, 4, 0.0, "correct", "LM_0", [-0.04473852656499761, 1.5342375734331517, 0.008428707402263815], [0, 0, 0], 1.0, "h_cups", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.04473852656499761, 1.5342375734331517, 0.008428707402263815], [-2.4937908845209843e-09, -1.9323905244887e-09, -1.3092378512213604e-08]], ["mug", 0, 21, 96, [-0.022480342583121683, 1.4941534238552878, 0.014536356578650333], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 34.17244963005974, "strawberry", 1.0, "target_in_possible_matches_(TP)", "confused", 2.981419086456299, "strawberry", 3, 1, 3, 0.0, "correct", "LM_0", [-0.022483041979164658, 1.4941509263136845, 0.014537135200170186], [0, 0, 0], 1.0, "strawberry", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.022483041979164658, 1.4941509263136845, 0.014537135200170186], [3.069171329337746e-07, 2.05738428791522e-07, 7.879674806093617e-07]], ["mug", 0, 21, 96, [0.0607783771826915, 1.5006516276213273, 0.012870252524684465], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 27.461190211770532, "power_drill", 1.0, "target_in_possible_matches_(TP)", "confused", 2.1256282329559326, "power_drill", 3, 1, 3, 0.0, "correct", "LM_0", [0.06077884527306399, 1.5006505694236727, 0.012870232226401934], [0, 0, 0], 1.0, "power_drill", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.06077884527306399, 1.5006505694236727, 0.012870232226401934], [-5.141601114603027e-08, -4.60764666704249e-08, -6.153363247354684e-07]], ["mug", 0, 21, 143, [0.025292606670351884, 1.5125523384269077, -0.006990445975906282], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 27.853982107152238, "padlock", 1.0, "target_in_possible_matches_(TP)", "confused", 2.735154151916504, "padlock", 3, 1, 2, 0.0, "correct", "LM_0", [0.025292629277046834, 1.5125522919067147, -0.006990440650448338], [0, 0, 0], 1.0, "padlock", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.025292629277046834, 1.5125522919067147, -0.006990440650448338], [3.1581362644556584e-07, -2.2243500503623926e-07, -6.641737488498684e-07]], ["mug", 5, 31, 164, [-0.020560822505702507, 1.5021220861254372, -0.005396609621132874], 31, [0.0, 0.0, 0.0], 1.0, 0.0, 40.15903012897385, "golf_ball", 1.0, "target_in_possible_matches_(TP)", "confused", 3.4966840744018555, "golf_ball", 4, 1, 3, 0.0, "correct", "LM_0", [-0.020561978042439668, 1.5021215708859097, -0.0053966076227389215], [0, 0, 0], 1.0, "golf_ball", [0.0, 1.5, 0.0], 31, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.020561978042439668, 1.5021215708859097, -0.0053966076227389215], [3.756775589794819e-09, 1.0791634756424439e-08, -3.995064025605598e-07]], ["mug", 0, 21, 140, [-0.08936299573781488, 1.5164153812126449, -0.06981923905831437], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 32.11904099562834, "hammer", 1.0, "target_in_possible_matches_(TP)", "confused", 2.4098939895629883, "hammer", 3, 1, 3, 0.0, "correct", "LM_0", [-0.08936322089907862, 1.51641757013629, -0.0698195857937624], [0, 0, 0], 1.0, "hammer", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.08936322089907862, 1.51641757013629, -0.0698195857937624], [2.8081628726758928e-08, 4.786865834455469e-08, 3.184652947777561e-08]], ["mug", 0, 21, 96, [0.023809492301238182, 1.4916664761063327, -0.041080143688566155], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 30.913145983784357, "softball", 1.0, "target_in_possible_matches_(TP)", "confused", 2.1489899158477783, "softball", 3, 1, 2, 0.0, "correct", "LM_0", [0.02380926064348559, 1.4916661767939705, -0.04108013907011449], [0, 0, 0], 1.0, "softball", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.02380926064348559, 1.4916661767939705, -0.04108013907011449], [1.3763687260527922e-08, 5.185173058324508e-08, -2.286077889700552e-07]], ["mug", 5, 30, 268, [-0.010087424674388638, 1.5317160535103995, -0.011577657866651093], 30, [0.0, 0.0, 0.0], 1.0, 0.0, 40.488834200881485, "orange", 1.0, "target_in_possible_matches_(TP)", "confused", 4.293344974517822, "orange", 4, 1, 4, 0.0, "correct", "LM_0", [-0.010086676395520362, 1.5317144119614134, -0.011577700108721051], [0, 0, 0], 1.0, "orange", [0.0, 1.5, 0.0], 30, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.010086676395520362, 1.5317144119614134, -0.011577700108721051], [1.2717523419955188e-08, -6.937184149052432e-09, -2.3049135434469525e-07]], ["mug", 0, 21, 172, [0.00896796010932069, 1.5108152240037913, -0.021279409379506165], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 25.5012843838844, "c_lego_duplo", 1.0, "target_in_possible_matches_(TP)", "confused", 3.0858230590820312, "c_lego_duplo", 3, 1, 3, 0.0, "correct", "LM_0", [0.008967959137758573, 1.5108152084227153, -0.021279408716235505], [0, 0, 0], 1.0, "c_lego_duplo", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.008967959137758573, 1.5108152084227153, -0.021279408716235505], [-1.2614421786175082e-07, 2.5534116371506678e-08, -3.9970177339689945e-06]], ["mug", 5, 49, 220, [-0.0013958466886639358, 1.4784311823554086, 0.015033785062225604], 49, [0.0, 0.0, 0.0], 1.0, 0.0, 77.36548115262285, "c_toy_airplane", 1.0, "target_in_possible_matches_(TP)", "confused", 4.365694046020508, "c_toy_airplane", 7, 1, 7, 0.0, "correct", "LM_0", [-0.001395697747141717, 1.478431184136395, 0.015033776569308405], [0, 0, 0], 1.0, "c_toy_airplane", [0.0, 1.5, 0.0], 49, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.001395697747141717, 1.478431184136395, 0.015033776569308405], [1.8762509628470994e-08, 4.756299431744732e-08, 3.6807548572767705e-08]], ["mug", 5, 26, 202, [-0.015661252657065985, 1.4805078724291, 0.0027466257564161397], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 35.61927747949522, "b_lego_duplo", 1.0, "target_in_possible_matches_(TP)", "confused", 5.495729923248291, "b_lego_duplo", 4, 1, 4, 0.0, "correct", "LM_0", [-0.015661252795231652, 1.4805078469078181, 0.002746625705774469], [0, 0, 0], 1.0, "b_lego_duplo", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.015661252795231652, 1.4805078469078181, 0.002746625705774469], [-1.6888438061761374e-08, -7.657479278875984e-08, -1.4952429343304704e-07]], ["mug", 0, 21, 120, [0.04999636392422889, 1.4926427326428324, -0.08664617105846416], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 35.0843388480251, "banana", 1.0, "target_in_possible_matches_(TP)", "confused", 2.532254934310913, "banana", 3, 1, 2, 0.0, "correct", "LM_0", [0.04999610932316649, 1.4926414909632164, -0.08664602004554531], [0, 0, 0], 1.0, "banana", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.04999610932316649, 1.4926414909632164, -0.08664602004554531], [7.212638700917754e-08, -4.298314060943333e-07, -8.665352505319452e-08]], ["mug", 0, 21, 189, [0.06829271960677807, 1.5092310143980514, 0.024522233403470463], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 28.89686457843024, "nine_hole_peg_test", 1.0, "target_in_possible_matches_(TP)", "confused", 3.480201005935669, "nine_hole_peg_test", 2, 1, 2, 0.0, "correct", "LM_0", [0.0682927168309078, 1.5092309757674087, 0.024522235732481114], [0, 0, 0], 1.0, "nine_hole_peg_test", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.0682927168309078, 1.5092309757674087, 0.024522235732481114], [1.2288010302865685e-07, 5.545917931405463e-08, -2.1705431498744936e-07]], ["mug", 0, 24, 120, [-0.021786044485101727, 1.4595706636613341, 0.0255713402053138], 24, [0.0, 0.0, 0.0], 1.0, 0.0, 30.52393790334865, "tomato_soup_can", 1.0, "target_in_possible_matches_(TP)", "confused", 4.832170009613037, "tomato_soup_can", 3, 1, 3, 0.0, "correct", "LM_0", [-0.02178593246148498, 1.4595707462146077, 0.025571353419951516], [0, 0, 0], 1.0, "tomato_soup_can", [0.0, 1.5, 0.0], 24, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.02178593246148498, 1.4595707462146077, 0.025571353419951516], [9.940338626840203e-06, 1.0206910629953862e-05, -0.00012654160219840383]], ["mug", 5, 55, 332, [-0.001395912139444873, 1.5007095122921121, 0.036457242748678634], 55, [0.0, 0.0, 0.0], 1.0, 0.0, 68.45322973317656, "baseball", 1.0, "target_in_possible_matches_(TP)", "confused", 6.463134050369263, "baseball", 8, 1, 8, 0.0, "correct", "LM_0", [-0.0013941509784767222, 1.5007086479617526, 0.03645743906096772], [0, 0, 0], 1.0, "baseball", [0.0, 1.5, 0.0], 55, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.0013941509784767222, 1.5007086479617526, 0.03645743906096772], [-1.4231639448092587e-07, 5.6411132052435386e-08, 2.869372163189743e-07]], ["mug", 5, 29, 227, [-0.03020814975685856, 1.4949623541868144, -0.021989608505187185], 29, [0.0, 0.0, 0.0], 1.0, 0.0, 44.48066690958218, "g_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 3.7465782165527344, "g_cups", 4, 1, 4, 0.0, "correct", "LM_0", [-0.03020814764104972, 1.4949624345337917, -0.021989609104783328], [0, 0, 0], 1.0, "g_cups", [0.0, 1.5, 0.0], 29, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.03020814764104972, 1.4949624345337917, -0.021989609104783328], [-1.461074131721693e-09, 2.2093150594943655e-09, 1.0428786855703858e-07]], ["mug", 0, 21, 139, [0.024218102396946856, 1.4854862471179302, -0.035130154683703695], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 33.840996086191105, "gelatin_box", 1.0, "target_in_possible_matches_(TP)", "confused", 2.929960012435913, "gelatin_box", 3, 1, 2, 0.0, "correct", "LM_0", [0.0242177959350136, 1.4854867515376784, -0.03513005337949673], [0, 0, 0], 1.0, "gelatin_box", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.0242177959350136, 1.4854867515376784, -0.03513005337949673], [4.8946342199837105e-08, -5.70719495374937e-08, 1.1486151335007983e-11]], ["mug", 0, 21, 104, [-0.02397364858699426, 1.4828361027200831, 0.006341299067296411], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 30.337422686136602, "lemon", 1.0, "target_in_possible_matches_(TP)", "confused", 3.0383880138397217, "lemon", 3, 1, 3, 0.0, "correct", "LM_0", [-0.023987830645856602, 1.4828287742192205, 0.006343702378537796], [0, 0, 0], 1.0, "lemon", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.023987830645856602, 1.4828287742192205, 0.006343702378537796], [-1.0911125239494148e-07, -2.794013219916522e-07, -2.1080659515179813e-07]], ["mug", 5, 66, 444, [-0.02014041122217691, 1.4790847579672548, 0.009746393211508294], 66, [0.0, 0.0, 0.0], 1.0, 0.0, 93.83029986464999, "plum", 1.0, "target_in_possible_matches_(TP)", "confused", 8.828011989593506, "plum", 10, 1, 10, 0.0, "correct", "LM_0", [-0.020132559413568264, 1.4790838929651904, 0.009745738836979077], [0, 0, 0], 1.0, "plum", [0.0, 1.5, 0.0], 66, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.020132559413568264, 1.4790838929651904, 0.009745738836979077], [-1.2626560780473395e-07, -9.089344640920897e-08, 1.604731065777067e-08]], ["mug", 5, 26, 256, [-0.02601823075771129, 1.4905113188739025, -0.003364700854350724], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 34.56418499584817, "racquetball", 1.0, "target_in_possible_matches_(TP)", "confused", 4.054422616958618, "racquetball", 4, 1, 4, 0.0, "correct", "LM_0", [-0.02601824599394718, 1.4905112969032095, -0.00336470047210706], [0, 0, 0], 1.0, "racquetball", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.02601824599394718, 1.4905112969032095, -0.00336470047210706], [-8.9729868849713e-09, 1.6214467766101008e-10, 3.2924117494038723e-07]], ["mug", 0, 21, 120, [0.12731631726337145, 1.511367814641785, -0.025756866410615153], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 34.10138692558046, "plate", 1.0, "target_in_possible_matches_(TP)", "confused", 2.430644989013672, "plate", 3, 1, 3, 0.0, "correct", "LM_0", [0.12730217438889505, 1.511376210164995, -0.025739881210696317], [0, 0, 0], 1.0, "plate", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.12730217438889505, 1.511376210164995, -0.025739881210696317], [-1.384592939250937e-08, 2.5330344594174676e-08, 6.320530765161761e-08]], ["mug", 0, 21, 111, [0.05658958738003382, 1.5188752749676189, -0.0004900949009547034], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 26.724298479012443, "pudding_box", 1.0, "target_in_possible_matches_(TP)", "confused", 6.125288963317871, "pudding_box", 2, 1, 1, 0.0, "correct", "LM_0", [0.05658951404616127, 1.518875272515805, -0.0004900572112048868], [0, 0, 0], 1.0, "pudding_box", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.05658951404616127, 1.518875272515805, -0.0004900572112048868], [4.807383574930318e-05, -1.6852567057958376e-06, 9.20543606621566e-05]], ["mug", 5, 28, 230, [-0.02412639691499362, 1.5141960784386084, 0.019481499311588], 28, [0.0, 0.0, 0.0], 1.0, 0.0, 44.52505081171024, "e_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 3.7416369915008545, "e_cups", 4, 1, 3, 0.0, "correct", "LM_0", [-0.024126410023237344, 1.514196162326231, 0.019481499961677015], [0, 0, 0], 1.0, "e_cups", [0.0, 1.5, 0.0], 28, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.024126410023237344, 1.514196162326231, 0.019481499961677015], [-1.226963295352981e-08, 1.3942190446132392e-08, -1.3488483762848794e-07]], ["mug", 5, 26, 192, [0.00890036687526325, 1.5218210193950097, -0.03317252288238913], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 38.27046801078195, "apple", 1.0, "target_in_possible_matches_(TP)", "confused", 3.3109419345855713, "apple", 4, 1, 4, 0.0, "correct", "LM_0", [0.008901138337193273, 1.5218218795701504, -0.03317250765756441], [0, 0, 0], 1.0, "apple", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.008901138337193273, 1.5218218795701504, -0.03317250765756441], [8.325699270173036e-09, -3.1767380158457604e-11, 3.577334635689302e-08]], ["mug", 5, 27, 187, [0.01834485475645096, 1.5137055349584598, -0.038022772402432126], 27, [0.0, 0.0, 0.0], 1.0, 0.0, 38.25178485897642, "j_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 3.1815459728240967, "j_cups", 4, 1, 4, 0.0, "correct", "LM_0", [0.01834492622437694, 1.5137056654943704, -0.03802277240133925], [0, 0, 0], 1.0, "j_cups", [0.0, 1.5, 0.0], 27, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.01834492622437694, 1.5137056654943704, -0.03802277240133925], [-3.07352254326985e-08, 5.293956933983669e-09, -6.373734588124007e-08]], ["mug", 5, 29, 188, [0.025773348796873513, 1.511990049430586, -0.019900461473993172], 29, [0.0, 0.0, 0.0], 1.0, 0.0, 37.61551783246367, "foam_brick", 1.0, "target_in_possible_matches_(TP)", "confused", 5.8385090827941895, "foam_brick", 4, 1, 4, 0.0, "correct", "LM_0", [0.025773348735492526, 1.5119900354865423, -0.019900459848678966], [0, 0, 0], 1.0, "foam_brick", [0.0, 1.5, 0.0], 29, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.025773348735492526, 1.5119900354865423, -0.019900459848678966], [-2.9848930023665315e-08, 2.1064966321957583e-08, -1.3163780007784034e-06]], ["mug", 0, 21, 102, [0.006193471737380048, 1.49469246091396, -0.05054006494669676], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 29.051538311302593, "large_marker", 1.0, "target_in_possible_matches_(TP)", "confused", 1.9610989093780518, "large_marker", 3, 1, 3, 0.0, "correct", "LM_0", [0.006219625676366891, 1.4946479237425423, -0.05054631826850897], [0, 0, 0], 1.0, "large_marker", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.006219625676366891, 1.4946479237425423, -0.05054631826850897], [-3.046577487236324e-08, -5.176630470116659e-07, 5.992637955277851e-08]], ["mug", 5, 47, 364, [-0.011660418100068657, 1.5272513997662231, -0.004403605116993696], 47, [0.0, 0.0, 0.002], 1.0, 0.0, 63.48727133555072, "peach", 1.0, "target_in_possible_matches_(TP)", "confused", 9.48008918762207, "peach", 5, 1, 5, 0.0, "correct", "LM_0", [-0.0116605146616824, 1.5272518321243649, -0.004403587150273924], [0, 0, 0], 1.0, "peach", [0.0, 1.5, 0.0], 47, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.0116605146616824, 1.5272518321243649, -0.004403587150273924], [-1.8532166940548457e-05, -7.87610069198251e-05, 0.0020872634954710147]], ["mug", 0, 21, 120, [0.07359490598836674, 1.5117562014543748, -0.047225929270875155], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 30.132343885014084, "phillips_screwdriver", 1.0, "target_in_possible_matches_(TP)", "confused", 2.511167049407959, "phillips_screwdriver", 3, 1, 3, 0.0, "correct", "LM_0", [0.07358189305260646, 1.5117030030950143, -0.04721880077665576], [0, 0, 0], 1.0, "phillips_screwdriver", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.07358189305260646, 1.5117030030950143, -0.04721880077665576], [-1.8822584884257116e-07, 2.4227544508361466e-07, -1.2562335736242214e-07]], ["mug", 0, 21, 92, [-0.013723930506280265, 1.5302513502230657, -0.12668851671942918], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 30.823183131299917, "a_toy_airplane", 1.0, "target_in_possible_matches_(TP)", "confused", 2.2451770305633545, "a_toy_airplane", 3, 1, 3, 0.0, "correct", "LM_0", [-0.013722894590835687, 1.5302182016480772, -0.12664735489539963], [0, 0, 0], 1.0, "a_toy_airplane", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.013722894590835687, 1.5302182016480772, -0.12664735489539963], [4.242162964010779e-07, 4.616891394501819e-07, -4.039533099360415e-07]], ["mug", 0, 21, 182, [0.0005249298300300086, 1.5086285627564675, -0.036980901107723355], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 27.481394151484356, "e_lego_duplo", 1.0, "target_in_possible_matches_(TP)", "confused", 4.907229900360107, "e_lego_duplo", 2, 1, 2, 0.0, "correct", "LM_0", [0.0005249305814857418, 1.5086285259618146, -0.036980900909997484], [0, 0, 0], 1.0, "e_lego_duplo", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.0005249305814857418, 1.5086285259618146, -0.036980900909997484], [-1.4453553695740785e-07, 3.516145056457526e-07, 4.0410121554219816e-07]], ["mug", 0, 21, 131, [0.015144434969368133, 1.566694853351155, 0.02283510522978194], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 28.515136037399806, "sugar_box", 1.0, "target_in_possible_matches_(TP)", "confused", 3.9257490634918213, "sugar_box", 2, 1, 2, 0.0, "correct", "LM_0", [0.015144435206448413, 1.566694882471776, 0.022835105868472337], [0, 0, 0], 1.0, "sugar_box", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.015144435206448413, 1.566694882471776, 0.022835105868472337], [-6.193728148543687e-09, -9.100902776633336e-09, -2.761167441494966e-07]], ["mug", 0, 21, 95, [0.06448335178152571, 1.5802309889456472, 0.006877712421728496], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 27.60932277460668, "a_colored_wood_blocks", 1.0, "target_in_possible_matches_(TP)", "confused", 2.0425400733947754, "a_colored_wood_blocks", 3, 1, 3, 0.0, "correct", "LM_0", [0.06448276389810213, 1.5802306058432845, 0.006877681814289835], [0, 0, 0], 1.0, "a_colored_wood_blocks", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.06448276389810213, 1.5802306058432845, 0.006877681814289835], [-5.671004375540414e-08, 8.020510334661975e-08, 2.9170327482716527e-08]], ["mug", 5, 26, 215, [0.021959197902006913, 1.4970765748332882, -0.008973479933753428], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 37.43123685935174, "c_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 3.583096981048584, "c_cups", 3, 1, 3, 0.0, "correct", "LM_0", [0.021959196876338752, 1.4970765878466896, -0.008973479976334377], [0, 0, 0], 1.0, "c_cups", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.021959196876338752, 1.4970765878466896, -0.008973479976334377], [4.79145107922374e-09, 4.111142997937947e-08, 7.751774837814164e-08]], ["mug", 0, 21, 143, [-0.03205868121741111, 1.5047303272465269, 0.008261472138463153], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 30.928362314296145, "pear", 1.0, "target_in_possible_matches_(TP)", "confused", 2.3684051036834717, "pear", 3, 1, 3, 0.0, "correct", "LM_0", [-0.0320628198151277, 1.5046820651474075, 0.008259561977227863], [0, 0, 0], 1.0, "pear", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.0320628198151277, 1.5046820651474075, 0.008259561977227863], [-7.059614395153523e-09, -4.7972815149263255e-09, 4.609885870790416e-08]], ["mug", 0, 21, 132, [-0.021797523810922363, 1.4768186365132046, -0.006346578758270417], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 33.94150492429328, "f_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 2.53764009475708, "f_cups", 3, 1, 3, 0.0, "correct", "LM_0", [-0.021797440492872876, 1.4768186979970155, -0.006346577422487109], [0, 0, 0], 1.0, "f_cups", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.021797440492872876, 1.4768186979970155, -0.006346577422487109], [1.8054089923185844e-08, 2.2528135975399333e-08, 1.0809321361671394e-08]], ["mug", 5, 26, 180, [0.0015727416854576922, 1.4287363983427837, -0.0441974311118683], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 31.692463308938752, "wood_block", 1.0, "target_in_possible_matches_(TP)", "confused", 4.595459699630737, "wood_block", 3, 1, 3, 0.0, "correct", "LM_0", [0.001572744733378294, 1.4287364425565061, -0.04419742903969984], [0, 0, 0], 1.0, "wood_block", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.001572744733378294, 1.4287364425565061, -0.04419742903969984], [3.693577982544113e-07, 6.767968462223592e-08, -1.1455330282166099e-06]], ["mug", 5, 33, 283, [0.022630138173965607, 1.4848955012394376, 0.011901942931729966], 33, [0.0, 0.0, 0.0], 1.0, 0.0, 42.550994343440244, "d_lego_duplo", 1.0, "target_in_possible_matches_(TP)", "confused", 6.149057149887085, "d_lego_duplo", 4, 1, 4, 0.0, "correct", "LM_0", [0.022630136386442793, 1.4848955434587403, 0.011901941068608765], [0, 0, 0], 1.0, "d_lego_duplo", [0.0, 1.5, 0.0], 33, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.022630136386442793, 1.4848955434587403, 0.011901941068608765], [-2.1599752999900807e-06, -1.7772143149125892e-07, 2.18490081224626e-06]], ["mug", 0, 21, 127, [-0.05346293734890001, 1.4954213600433415, -0.08384961230613641], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 31.33061777260099, "b_toy_airplane", 1.0, "target_in_possible_matches_(TP)", "confused", 2.4385883808135986, "b_toy_airplane", 3, 1, 3, 0.0, "correct", "LM_0", [-0.053462850648362696, 1.4954174295115343, -0.08384728465597016], [0, 0, 0], 1.0, "b_toy_airplane", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.053462850648362696, 1.4954174295115343, -0.08384728465597016], [8.860265628584557e-08, -5.756774076234959e-07, 3.6437078875194225e-07]], ["mug", 5, 29, 160, [0.015836393543579134, 1.4932126845001843, -0.0013951159517239242], 29, [0.0, 0.0, 0.0], 1.0, 0.0, 36.874251356692916, "b_colored_wood_blocks", 1.0, "target_in_possible_matches_(TP)", "confused", 5.326285123825073, "b_colored_wood_blocks", 3, 1, 3, 0.0, "correct", "LM_0", [0.01583639776838622, 1.4932127449009605, -0.0013951126024622385], [0, 0, 0], 1.0, "b_colored_wood_blocks", [0.0, 1.5, 0.0], 29, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.01583639776838622, 1.4932127449009605, -0.0013951126024622385], [1.1319074929286233e-05, -2.546463142949168e-07, -1.734804828165303e-05]], ["mug", 0, 21, 115, [0.024690980000377317, 1.4754455455810715, -0.14748971771776215], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 22.00373180432215, "g_lego_duplo", 1.0, "target_in_possible_matches_(TP)", "confused", 1.7088782787322998, "g_lego_duplo", 3, 1, 3, 0.0, "correct", "LM_0", [0.024694935925308685, 1.4754508945069509, -0.14749286447833662], [0, 0, 0], 1.0, "g_lego_duplo", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.024694935925308685, 1.4754508945069509, -0.14749286447833662], [-2.6353850048330477e-07, -8.369984212507134e-07, -2.668130858890677e-07]], ["mug", 0, 21, 140, [-0.02965511013707084, 1.4930165309775367, 0.011883406914776202], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 30.66796677520314, "a_lego_duplo", 1.0, "target_in_possible_matches_(TP)", "confused", 6.740611791610718, "a_lego_duplo", 2, 1, 2, 0.0, "correct", "LM_0", [-0.02965511015737373, 1.4930165138869118, 0.011883406658381125], [0, 0, 0], 1.0, "a_lego_duplo", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.02965511015737373, 1.4930165138869118, 0.011883406658381125], [4.4803274151770743e-10, 4.2535466088020487e-10, 1.281969664019101e-07]], ["mug", 0, 21, 111, [0.028406678382164898, 1.445322011336761, -0.025865440544014403], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 31.14902674705282, "mini_soccer_ball", 1.0, "target_in_possible_matches_(TP)", "confused", 2.4020259380340576, "mini_soccer_ball", 3, 1, 3, 0.0, "correct", "LM_0", [0.028406648891900987, 1.4453198940796033, -0.025865602955566225], [0, 0, 0], 1.0, "mini_soccer_ball", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.028406648891900987, 1.4453198940796033, -0.025865602955566225], [8.28683273995448e-08, 1.0260975746706594e-07, 5.259233835479154e-07]], ["mug", 0, 21, 267, [-0.008196768572761423, 1.4989713949682995, 0.01691424625790631], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 36.437304122341736, "medium_clamp", 1.0, "target_in_possible_matches_(TP)", "confused", 3.475105047225952, "medium_clamp", 3, 1, 1, 0.0, "correct", "LM_0", [-0.008196757427818242, 1.4989724402952513, 0.01691420868028582], [0, 0, 0], 1.0, "medium_clamp", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.008196757427818242, 1.4989724402952513, 0.01691420868028582], [-2.566721470935756e-08, -2.421103234067718e-07, 1.1348102687734508e-07]], ["mug", 0, 21, 84, [-0.0008363305514484075, 1.4719129034904983, -0.0277973803964281], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 31.226854801914573, "a_marbles", 1.0, "target_in_possible_matches_(TP)", "confused", 2.562704086303711, "a_marbles", 3, 1, 3, 0.0, "correct", "LM_0", [-0.0008385735072723715, 1.471905452915518, -0.027798889919995334], [0, 0, 0], 1.0, "a_marbles", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.0008385735072723715, 1.471905452915518, -0.027798889919995334], [1.387917051290641e-07, -1.1661007573055676e-07, 4.252091525062251e-07]], ["mug", 0, 23, 160, [-0.04642853764058283, 1.4895094472553752, -0.02736683785930301], 23, [4.297, 357.836, 1.911], 1.0, 0.0909, 31.68014339873748, "extra_large_clamp", 1.0, "target_in_possible_matches_(TP)", "confused", 2.9785306453704834, "extra_large_clamp", 2, 1, 1, 0.0909, "correct", "LM_0", [-0.050589249553175314, 1.4869648544128544, -0.02406575675164632], [0, 0, 0], 1.0, "extra_large_clamp", [0.0, 1.5, 0.0], 23, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.050589249553175314, 1.4869648544128544, -0.02406575675164632], [4.297471712901697, -2.1644011013563675, 1.9105909471585405]], ["mug", 5, 26, 207, [0.013619272903943713, 1.5340805609937538, 0.02739543323998253], 26, [0.0, 0.0, 0.0], 1.0, 0.0, 40.76337474644447, "d_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 3.2761940956115723, "d_cups", 4, 1, 4, 0.0, "correct", "LM_0", [0.013619373402337837, 1.5340806192635654, 0.027395432745833517], [0, 0, 0], 1.0, "d_cups", [0.0, 1.5, 0.0], 26, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.013619373402337837, 1.5340806192635654, 0.027395432745833517], [-1.1186024097457567e-08, 2.838454219925301e-10, 2.4272754022858302e-08]], ["mug", 0, 21, 99, [0.0005884034200349967, 1.530676487523676, -0.0056058399299580425], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 34.52816142363956, "e_toy_airplane", 1.0, "target_in_possible_matches_(TP)", "confused", 2.643127679824829, "e_toy_airplane", 2, 1, 1, 0.0, "correct", "LM_0", [0.000590468557303349, 1.5306763228719786, -0.005605576240353792], [0, 0, 0], 1.0, "e_toy_airplane", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.000590468557303349, 1.5306763228719786, -0.005605576240353792], [7.70445915618136e-09, 3.5625735214756576e-07, -2.076175056360565e-08]], ["mug", 0, 21, 95, [-0.06075777783067664, 1.4953284754166085, -0.07563968492183148], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 31.474524793619473, "adjustable_wrench", 1.0, "target_in_possible_matches_(TP)", "confused", 2.399264097213745, "adjustable_wrench", 3, 1, 2, 0.0, "correct", "LM_0", [-0.0603677834662528, 1.4962811908320817, -0.07578768616123087], [0, 0, 0], 1.0, "adjustable_wrench", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.0603677834662528, 1.4962811908320817, -0.07578768616123087], [-4.373339822683372e-07, -1.234025815360369e-06, -9.875240275947209e-08]], ["mug", 0, 22, 112, [0.02930559299260976, 1.520434016676451, -0.005036851441924679], 22, [0.0, 0.0, 0.0], 1.0, 0.0, 32.37461817601978, "rubiks_cube", 1.0, "target_in_possible_matches_(TP)", "confused", 4.510087013244629, "rubiks_cube", 3, 1, 3, 0.0, "correct", "LM_0", [0.02930559344083395, 1.5204339632141166, -0.005036854134239618], [0, 0, 0], 1.0, "rubiks_cube", [0.0, 1.5, 0.0], 22, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.02930559344083395, 1.5204339632141166, -0.005036854134239618], [-6.709667558069438e-08, 1.1755898785866204e-07, -5.928232068782541e-08]], ["mug", 5, 30, 311, [-0.001606452840190905, 1.5084366383921222, 0.016324149916447147], 30, [0.0, 0.0, 0.0], 1.0, 0.0, 39.00383447137875, "f_lego_duplo", 1.0, "target_in_possible_matches_(TP)", "confused", 5.853499889373779, "f_lego_duplo", 4, 1, 4, 0.0, "correct", "LM_0", [-0.0016064534295260613, 1.5084366509694163, 0.016324150597512407], [0, 0, 0], 1.0, "f_lego_duplo", [0.0, 1.5, 0.0], 30, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.0016064534295260613, 1.5084366509694163, 0.016324150597512407], [-4.71963760598312e-07, -1.4310980850335577e-07, -2.5634127067004607e-06]], ["mug", 5, 40, 215, [-0.00684580002015771, 1.4973559939512933, 0.022451868581220377], 40, [0.0, 0.0, 0.0], 1.0, 0.0, 61.27737668923544, "a_cups", 1.0, "target_in_possible_matches_(TP)", "confused", 4.182770252227783, "a_cups", 6, 1, 6, 0.0, "correct", "LM_0", [-0.006845786341020503, 1.4973560600089886, 0.022451868728371092], [0, 0, 0], 1.0, "a_cups", [0.0, 1.5, 0.0], 40, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.006845786341020503, 1.4973560600089886, 0.022451868728371092], [-2.385638231181478e-08, -3.042943530393444e-08, 6.144458183883027e-08]], ["mug", 0, 66, 463, [0.04667621600455177, 1.5015561152206516, 0.01556694099762057], 66, [102.901, 1.895, 272.832], 1.0, null, 15.325503842181295, "extra_large_clamp", 1.0, "target_not_matched_(FN)", "confused", 8.282363176345825, "skillet_lid", 9, 1, 3, null, "confused", "LM_0", [0.04774922335221679, 1.5038103534407417, -0.05245105966021918], [0, 0, 0], 1.0, "extra_large_clamp", [0.0, 1.5, 0.0], 66, [1.0, 0.0, 0.0, 0.0], 1.0, "confused", [0.04774922335221679, 1.5038103534407417, -0.05245105966021918], [102.90125130007165, 1.8946119458547988, -87.16790236367773]], ["mug", 0, 21, 98, [-0.02986381864651801, 1.502400369450085, -0.04753535262595907], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 31.4240374470404, "sponge", 1.0, "target_in_possible_matches_(TP)", "confused", 2.5321381092071533, "sponge", 3, 1, 3, 0.0, "correct", "LM_0", [-0.02986365842094188, 1.5024000531537536, -0.04753529501231149], [0, 0, 0], 1.0, "sponge", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.02986365842094188, 1.5024000531537536, -0.04753529501231149], [-1.0021283878882292e-07, 3.520474554712121e-08, 7.2803219238675965e-09]], ["mug", 5, 43, 372, [-0.021158390157550628, 1.4935274668989063, -0.02519018621834163], 43, [0.0, 0.0, 0.0], 1.0, 0.0, 59.407255310975664, "tennis_ball", 1.0, "target_in_possible_matches_(TP)", "confused", 6.136940002441406, "tennis_ball", 6, 1, 6, 0.0, "correct", "LM_0", [-0.02116572581682372, 1.493515547957453, -0.025189892291222177], [0, 0, 0], 1.0, "tennis_ball", [0.0, 1.5, 0.0], 43, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.02116572581682372, 1.493515547957453, -0.025189892291222177], [8.23555070277191e-09, 8.39231717692506e-08, 3.731314733903993e-08]], ["mug", 0, 21, 115, [-0.12175552002858901, 1.496492982564294, -0.06099144101834385], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 34.79965686190356, "spatula", 1.0, "target_in_possible_matches_(TP)", "confused", 3.1404669284820557, "spatula", 3, 1, 3, 0.0, "correct", "LM_0", [-0.12192923147303711, 1.4969987846057842, -0.06111466274807363], [0, 0, 0], 1.0, "spatula", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.12192923147303711, 1.4969987846057842, -0.06111466274807363], [3.270665878086279e-08, 2.6525315882434823e-07, 5.395772504913877e-08]], ["mug", 0, 500, 3061, [-0.006162935772082101, 1.516082567118756, 0.003658789953301088], 500, [null, null, null], null, null, 770.7491363838127, "d_toy_airplane", 1.0, "target_in_possible_matches_(TP)", "confused_mlh", 41.348637104034424, "d_toy_airplane", 13, 3, 11, 0.0, "time_out", "LM_0", [-0.0061656997018358995, 1.5160834646003778, 0.0036585847187743407], [0, 0, 0], 1.0, "c_toy_airplane^e_toy_airplane^d_toy_airplane", [0.0, 1.5, 0.0], null, [1.0, 0.0, 0.0, 0.0], 1.0, "correct_mlh", [null, null, null], [-7.107110341276853e-09, 9.787807171189012e-08, 3.9706202960508915e-08]], ["mug", 0, 14, 5000, [-0.15141925235774265, 1.4891544788861557, 0.02612133570968665], 14, [null, null, null], null, null, 16.25119281725015, "chain", 1.0, "target_in_possible_matches_(TP)", "confused_mlh", 1.6236340999603271, "chain", 2, 1, 2, 0.0, "time_out", "LM_0", [-0.15879278733537833, 1.494758885118274, 0.02154421198825293], [0, 0, 0], 1.0, "chain", [0.0, 1.5, 0.0], null, [1.0, 0.0, 0.0, 0.0], 1.0, "correct_mlh", [null, null, null], [-1.2424319258925178e-07, 4.390522666713327e-07, 1.3491317283133785e-07]], ["mug", 0, 21, 154, [-0.004111261216923142, 1.5046550793909932, 0.05410703188751412], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 32.018492725413864, "scissors", 1.0, "target_in_possible_matches_(TP)", "confused", 2.405874252319336, "scissors", 3, 1, 1, 0.0, "correct", "LM_0", [-0.004112996591524591, 1.5046557836834022, 0.05410382059474984], [0, 0, 0], 1.0, "scissors", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.004112996591524591, 1.5046557836834022, 0.05410382059474984], [1.9729248048608046e-07, 2.0107247874050267e-07, -4.0382850067022937e-07]], ["mug", 0, 21, 128, [-0.04669157815259967, 1.4195070507531236, -0.012485787169302697], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 32.75971002257706, "mustard_bottle", 1.0, "target_in_possible_matches_(TP)", "confused", 2.5266458988189697, "mustard_bottle", 3, 1, 3, 0.0, "correct", "LM_0", [-0.04669000455359485, 1.419508580462143, -0.01248534385125952], [0, 0, 0], 1.0, "mustard_bottle", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.04669000455359485, 1.419508580462143, -0.01248534385125952], [-5.712573753028095e-08, 4.0561905191513053e-07, 6.495054640409139e-08]], ["mug", 0, 21, 108, [0.0013991339648871289, 1.6231972288383723, 0.008731127128962396], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 29.054244386929202, "bleach_cleanser", 1.0, "target_in_possible_matches_(TP)", "confused", 2.3191659450531006, "bleach_cleanser", 3, 1, 3, 0.0, "correct", "LM_0", [0.0014005658321301832, 1.6231980537024178, 0.00873108627304419], [0, 0, 0], 1.0, "bleach_cleanser", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.0014005658321301832, 1.6231980537024178, 0.00873108627304419], [1.1208552123932394e-07, 3.5619578933115134e-08, 1.4416263329270406e-08]], ["mug", 5, 28, 135, [0.02772777592963985, 1.5062505608657086, -0.03146190731844463], 28, [0.0, 0.0, 0.0], 1.0, 0.0, 32.92291185265618, "tuna_fish_can", 1.0, "target_in_possible_matches_(TP)", "confused", 3.186677932739258, "tuna_fish_can", 4, 1, 4, 0.0, "correct", "LM_0", [0.02772779843858247, 1.5062521598505865, -0.03146191369069118], [0, 0, 0], 1.0, "tuna_fish_can", [0.0, 1.5, 0.0], 28, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.02772779843858247, 1.5062521598505865, -0.03146191369069118], [-4.205806027038188e-09, 9.487754914761617e-08, 1.5419872473725607e-07]], ["mug", 0, 21, 122, [0.031840198714671156, 1.5761458957956196, -0.00632128298722591], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 30.202159198624194, "cracker_box", 1.0, "target_in_possible_matches_(TP)", "confused", 4.4996538162231445, "cracker_box", 3, 1, 3, 0.0, "correct", "LM_0", [0.03184019771699553, 1.5761458566348552, -0.006321286281927668], [0, 0, 0], 1.0, "cracker_box", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.03184019771699553, 1.5761458566348552, -0.006321286281927668], [-1.2667574328654016e-07, -1.6994862657185495e-07, 3.966528113372399e-06]], ["mug", 0, 21, 106, [-0.09635164056884264, 1.4944504492246633, 0.011020375376106938], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 34.346303529159215, "fork", 1.0, "target_in_possible_matches_(TP)", "confused", 2.9264886379241943, "fork", 3, 1, 1, 0.0, "correct", "LM_0", [-0.0962974059814378, 1.4942711918364702, 0.011038192904899534], [0, 0, 0], 1.0, "fork", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [-0.0962974059814378, 1.4942711918364702, 0.011038192904899534], [3.459114004201396e-09, -1.5654893697663043e-07, 4.753650646128803e-09]], ["mug", 0, 21, 93, [0.03953448835571541, 1.4892284618245861, 0.03865756463441112], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 29.69844553957842, "large_clamp", 1.0, "target_in_possible_matches_(TP)", "confused", 2.2276570796966553, "large_clamp", 3, 1, 3, 0.0, "correct", "LM_0", [0.03953445869573176, 1.4892287668672668, 0.038657488049244404], [0, 0, 0], 1.0, "large_clamp", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.03953445869573176, 1.4892287668672668, 0.038657488049244404], [2.2369694927138885e-07, 1.1902515105016999e-06, 8.096323919518863e-08]], ["mug", 0, 21, 84, [0.008401287497639156, 1.505226918269893, -0.003095559976135689], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 33.59601015642152, "dice", 1.0, "target_in_possible_matches_(TP)", "confused", 2.25891375541687, "dice", 3, 1, 3, 0.0, "correct", "LM_0", [0.008403778224524885, 1.5052308849203762, -0.00309542575117003], [0, 0, 0], 1.0, "dice", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.008403778224524885, 1.5052308849203762, -0.00309542575117003], [2.5415967851851892e-09, 1.397519315372913e-07, -9.443684653472572e-09]], ["mug", 0, 21, 107, [0.0741365922516242, 1.511559674978936, 0.05338221980156817], 21, [0.0, 0.0, 0.0], 1.0, 0.0, 34.35134276519298, "flat_screwdriver", 1.0, "target_in_possible_matches_(TP)", "confused", 2.5896949768066406, "flat_screwdriver", 3, 1, 0, 0.0, "correct", "LM_0", [0.0741359830097426, 1.5115612333920456, 0.053381747711566424], [0, 0, 0], 1.0, "flat_screwdriver", [0.0, 1.5, 0.0], 21, [1.0, 0.0, 0.0, 0.0], 1.0, "correct", [0.0741359830097426, 1.5115612333920456, 0.053381747711566424], [-2.332205989743868e-08, -3.0221635672153827e-07, 1.2298390445681771e-07]], ["mug", 5, 31, 192, [-0.03219230708524666, 1.4810735300433442, 0.03689071755658005], 31, [178.87, 333.814, 179.95], 1.0, 0.1432, 39.39496466554794, "mug", 1.0, "target_in_possible_matches_(TP)", "correct", 4.004611968994141, "mug", 4, 1, 3, 0.1432, "correct", "LM_0", [0.028937824684812916, 1.4823926717832143, -0.007742167826543724], [0, 90, 0], 1.0, "mug", [0.0, 1.5, 0.0], 31, [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], 1.0, "correct", [0.028937824684812916, 1.4823926717832143, -0.007742167826543724], [178.86950411661493, -26.185509334751373, 179.95040381793928]], ["mug", 0, 21, 190, [0.07099276007775276, 1.5132781633461567, 0.0043656846882122995], 21, [92.115, 90.0, 92.115], 1.0, 0.0, 28.672922208457877, "bowl", 1.0, "target_in_possible_matches_(TP)", "confused", 3.042407274246216, "bowl", 3, 1, 3, 0.0, "correct", "LM_0", [-0.004365754347761016, 1.513278344833432, 0.0709927248402559], [0, 90, 0], 1.0, "bowl", [0.0, 1.5, 0.0], 21, [0.7071067811865476, 0.0, 0.7071067811865475, 0.0], 1.0, "correct", [-0.004365754347761016, 1.513278344833432, 0.0709927248402559], [92.11476273035329, 89.99987913749152, 92.11476023172347]]]}