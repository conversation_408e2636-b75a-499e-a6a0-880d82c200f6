# Copyright 2025 Thousand Brains Project
# Copyright 2022-2024 Numenta Inc.
#
# Copyright may exist in Contributors' modifications
# and/or contributions to the work.
#
# Use of this source code is governed by the MIT
# license that can be found in the LICENSE file or at
# https://opensource.org/licenses/MIT.


# NOTE: heavy_master_chef_can removed because it has the same mesh as master_chef_can
# The only difference is mass, which we can add back in once we are running simulations
# with physics.
YCB_OBJECTS_LIST = [
    "master_chef_can",
    "cracker_box",
    "sugar_box",
    "tomato_soup_can",
    "mustard_bottle",
    "tuna_fish_can",
    "pudding_box",
    "gelatin_box",
    "potted_meat_can",
    "banana",
    "strawberry",
    "apple",
    "lemon",
    "peach",
    "pear",
    "orange",
    "plum",
    "pitcher_base",
    "bleach_cleanser",
    "bowl",
    "mug",
    "sponge",
    "skillet_lid",
    "plate",
    "fork",
    "spoon",
    "knife",
    "spatula",
    "power_drill",
    "wood_block",
    "scissors",
    "padlock",
    "large_marker",
    "adjustable_wrench",
    "phillips_screwdriver",
    "flat_screwdriver",
    "hammer",
    "medium_clamp",
    "large_clamp",
    "extra_large_clamp",
    "mini_soccer_ball",
    "softball",
    "baseball",
    "tennis_ball",
    "racquetball",
    "golf_ball",
    "chain",
    "foam_brick",
    "dice",
    "a_marbles",
    "b_marbles",
    "a_cups",
    "b_cups",
    "c_cups",
    "d_cups",
    "e_cups",
    "f_cups",
    "g_cups",
    "h_cups",
    "i_cups",
    "j_cups",
    "a_colored_wood_blocks",
    "b_colored_wood_blocks",
    "nine_hole_peg_test",
    "a_toy_airplane",
    "b_toy_airplane",
    "c_toy_airplane",
    "d_toy_airplane",
    "e_toy_airplane",
    "a_lego_duplo",
    "b_lego_duplo",
    "c_lego_duplo",
    "d_lego_duplo",
    "e_lego_duplo",
    "f_lego_duplo",
    "g_lego_duplo",
    "rubiks_cube",
]

SHUFFLED_YCB_OBJECTS = [
    "mug",
    "bowl",
    "potted_meat_can",
    "master_chef_can",
    "i_cups",
    "spoon",
    "b_cups",
    "pitcher_base",
    "knife",
    "b_marbles",
    "h_cups",
    "strawberry",
    "power_drill",
    "padlock",
    "golf_ball",
    "hammer",
    "softball",
    "orange",
    "c_lego_duplo",
    "c_toy_airplane",
    "b_lego_duplo",
    "banana",
    "nine_hole_peg_test",
    "tomato_soup_can",
    "baseball",
    "g_cups",
    "gelatin_box",
    "lemon",
    "plum",
    "racquetball",
    "plate",
    "pudding_box",
    "e_cups",
    "apple",
    "j_cups",
    "foam_brick",
    "large_marker",
    "peach",
    "phillips_screwdriver",
    "a_toy_airplane",
    "e_lego_duplo",
    "sugar_box",
    "a_colored_wood_blocks",
    "c_cups",
    "pear",
    "f_cups",
    "wood_block",
    "d_lego_duplo",
    "b_toy_airplane",
    "b_colored_wood_blocks",
    "g_lego_duplo",
    "a_lego_duplo",
    "mini_soccer_ball",
    "medium_clamp",
    "a_marbles",
    "extra_large_clamp",
    "d_cups",
    "e_toy_airplane",
    "adjustable_wrench",
    "rubiks_cube",
    "f_lego_duplo",
    "a_cups",
    "skillet_lid",
    "sponge",
    "tennis_ball",
    "spatula",
    "d_toy_airplane",
    "chain",
    "scissors",
    "mustard_bottle",
    "bleach_cleanser",
    "tuna_fish_can",
    "cracker_box",
    "fork",
    "large_clamp",
    "dice",
    "flat_screwdriver",
]

# 10 objects that have little similarities in morphology
DISTINCT_OBJECTS = [
    "mug",
    "bowl",
    "potted_meat_can",
    "spoon",
    "strawberry",
    "mustard_bottle",
    "dice",
    "golf_ball",
    "c_lego_duplo",
    "banana",
]

SIMILAR_OBJECTS = [
    "mug",  # Distinguished from e_cups by handle
    "e_cups",
    "knife",  # Distinguished from fork and spoon by tip
    "fork",
    "spoon",
    "c_cups",  # Distinguished from d_cups by height and circumference
    # c_ and d_cups are also similar in shape to mug and e_cups (but not in color)
    "d_cups",
    "cracker_box",  # Distinguished from sugar_box by colors
    "sugar_box",
    "pudding_box",
]
