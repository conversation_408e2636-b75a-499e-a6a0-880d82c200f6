---
title: Identify an issue to work on
---
# Do I Have to Ask Before Working on an Issue?

No. Multiple people can work on the same issue, and different people can submit multiple pull requests for the same issue. Ultimately, which pull request will be merged will be decided by Maintainers during the [Review](../pull-requests/reviewing-a-pull-request.md). However, to avoid double efforts, **it is encouraged to comment on an issue when you start working on it** to inform others about this. Also, if you decide to stop working on an issue, it is helpful to leave a comment so that someone else can pick up the issue and know what roadblocks you may have hit.

# Find an Existing Issue

If you have nothing specific to work on or want to become more familiar with the project, you can start by finding existing issues in the [Issue Tracker](https://github.com/thousandbrainsproject/tbp.monty/issues). Every [`triaged`](https://github.com/thousandbrainsproject/tbp.monty/issues?q=is%3Aissue+is%3Aopen+label%3Atriaged+) issue contains labels that provide additional information.

- Issues with a [`good first issue`](https://github.com/thousandbrainsproject/tbp.monty/labels/good%20first%20issue) label should be appropriate for first-time committers and don't usually require broad changes across the code base or a deep understanding of the algorithms intricacies.

If you decide to start work on one of the issues, please leave a comment to let Maintainers and others know.

# Create a New Issue

**Before creating a new issue, please check if a similar issue exists** in the [Issue Tracker](https://github.com/thousandbrainsproject/tbp.monty/issues). Perhaps someone already reported it, and someone might even be working on it.

If you do not see a similar issue reported, please [create a New issue using one of the provided templates](https://github.com/thousandbrainsproject/tbp.monty/issues/new/choose).