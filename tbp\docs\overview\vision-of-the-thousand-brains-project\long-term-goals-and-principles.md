---
title: Long-Term Goals and Principles
---
A central long-term goal is to build a universal Platform and messaging protocol for intelligent sensorimotor systems. We call this protocol the "Cortical Messaging Protocol" (CMP). The CMP can be used as an interface between different custom modules, and its universality is central to the ease of use of the SDK we are developing. For instance, one person may have modules optimized for flying drones using birds-eye observations, while another may be working with different sensors and actuators regulating a smart home. Those two are quite different modules but they should be able to communicate on the same channels defined here. Third parties could develop sensor modules and learning modules according to their specific requirements but they would be compatible with all existing modules due to a shared messaging protocol.

A second goal of the Thousand Brains Project (TBP) is to be a catalyst for a whole new way of thinking about machine intelligence. The principles of the TBP differ from many principles of popular AI methodologies today and are more in line with the principles of learning in the brain. Most concepts presented here derive from the Thousand Brains Theory (TBT) (<PERSON> et al., 2019) and experimental evidence about how the brain works. Modules in Monty are inspired by cortical columns in the neocortex (Mountcastle, 1997). The CMP between modules relies on sparse location and reference frame-based data structures. They are analogous to long-range connections in the neocortex. In our implementation, we do not need to strictly adhere to all biological details and it is important to note that should an engineering solution serve us better for implementing certain aspects, then it is acceptable to deviate from the neuroscience and the TBT. In general, the inner workings of the modules can be relatively arbitrary and do not have to rely on neuroscience as long as they adhere to the CMP. However, the core principles of the TBP are motivated by what we have learned from studying the neocortex.

Third, this project aims to eventually bring together prior work into a single framework, including sparsity, active dendrites, sequence memory, and grid cells (Hawkins and Ahmad, 2016; Hawkins, Ahmad, and Cui, 2017; Ahmad and Scheinkman, 2019; Hawkins et al., 2019; Lewis et al., 2019).

Finally, it will be important to showcase the capabilities of our SDK. We will work towards creating a non-trivial demo where the implementation can be used to showcase capabilities that would be hard to demonstrate any other way. This may not be one specific task but could play to the strength of this system to tackle a wide variety of tasks. We will also work on making Monty an easy-to-use open-source SDK that other practitioners can apply and test on their applications. We want this to be a platform for all kinds of sensorimotor applications and not just a specific technology showcase.

We have a set of guiding principles that steer the Thousand Brains Project. Throughout the life of the project there may be several different implementations and within each implementation there may be different versions of the core building blocks but everything we work on should follow these core principles:

- Sensorimotor learning and inference: We are using actively generated temporal sequences of sensory inputs instead of static inputs.

- Modular structure: Easily expandable and scalable.

- Cortical Messaging Protocol: Inner workings of modules are highly customizable but their inputs and outputs adhere to a defined protocol such that many different sensor modules (and modalities) and learning modules can work together seamlessly.

- Voting: A mechanism by which a collection of experts can use different information and models to come to a faster, more robust and stable conclusion.

- Reference frames: The learned models should have inductive biases that make them naturally good at modeling a structured 4D world. The learned models can be used for a variety of tasks such as manipulation, planning, imagining previously unseen states of the world, fast learning, generalization, and many more.

- Rapid, continual learning where learning and inference are closely intertwined: supported by sensorimotor embodiment and reference frames, biologically plausible learning mechanisms enable rapid knowledge accumulation and updates to stored representations while remaining robust under the setting of continual learning. There is also no clear distinction between learning and inference. We are always learning, and always performing inference.