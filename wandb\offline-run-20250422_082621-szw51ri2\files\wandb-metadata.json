{"os": "macOS-10.16-x86_64-i386-64bit", "python": "3.8.20", "heartbeatAt": "2025-04-22T00:26:21.188397", "startedAt": "2025-04-22T00:26:21.159364", "docker": null, "cuda": null, "args": ["-e", "base_77obj_surf_agent"], "state": "running", "program": "run.py", "codePathLocal": "run.py", "codePath": "run.py", "host": "deMacBook-Air.local", "username": "", "executable": "/opt/anaconda3/envs/tbp.monty/bin/python", "cpu_count": 8, "cpu_count_logical": 8, "cpu_freq": {"current": 2400, "min": 2400, "max": 2400}, "cpu_freq_per_core": [{"current": 2400, "min": 2400, "max": 2400}], "disk": {"/": {"total": 228.27386474609375, "used": 171.81040954589844}}, "memory": {"total": 8.0}}