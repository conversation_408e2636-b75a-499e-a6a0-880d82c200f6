---
title: Why Contribute?
---
We are excited about all contributors and there may be a wide range of motivations you may have for contributing. Here is a (non exhaustive) list of what those reasons might be and benefits you may have from contributing.

- You are tired of incremental progress on ANN benchmarks.
- You don't believe that LLMs are the path to true machine intelligence/understanding the brain.
- You want to do exciting research but don't have a big compute budget.
- You are looking for a wide open space to explore new ideas.
- You want to solve tasks where little training data is available.
- You want to solve sensorimotor tasks.
- You want to solve a task that requires quick, continuous learning and adaptation.
- You want to better understand the brain and principles underlying our intelligence.
- You want to work on the future of AI.
- You want to be part of a truly unique and special project.

Here is a list of concrete output you may get out of working on this project.

- Write a publication.
- Write your bachelor or master thesis on the thousand brains approach.
- Be part of an awesome community.
- Have your project showcased on our [showcase page](../community/project-showcase.md).
- Have your paper listed on our [TBP based papers page](../community/tbp-based-papers.md).
- Become a code contributor.
- Lastly, for those out there who love achievements, note that when an RFC you have made is merged and active, you can get a player icon of your choice on our [project roadmap](../future-work/project-roadmap.md). Maybe we'll see you there soon? 🎯

As we are putting this code under an [MIT license](../../LICENSE) and Numenta has put its related patents under a [non-assert pledge](https://www.numenta.com/thousand-brains-project/patents/), people can also build commercial applications on this framework. However, the current code is very much research code and not an out-of-the-box solution so it will require significant engineering effort to tailor it to your application.