---
title: Governance
---
# Summary

Thousand Brains Project adopts a hierarchical technical governance structure for <PERSON>.

- A community of **Contributors** who file issues, make pull requests, and contribute to the project.
- A small set of **Maintainers**, who drive the overall project direction.
- The **Maintainers** have a **Lead Maintainer**, the catch-all decision maker.

All **Maintainers** are expected to have a strong bias towards the Thousand Brains Project and <PERSON>'s design philosophy.

**Contributors** are encouraged to participate by filing issues, making pull requests, and contributing to the project.

## Lead Maintainer

The current **Lead Maintainer** is listed in the [MAINTAINERS file](https://github.com/thousandbrainsproject/tbp.monty/blob/main/MAINTAINERS.md).

### Responsibilities

- Make the final decision when **Maintainers** cannot reach a consensus or make a decision.
- Confirm or deny adding new or removing current **Maintainers**.

## Maintainers

The current **Maintainers** are listed in the [MAINTAINERS file](https://github.com/thousandbrainsproject/tbp.monty/blob/main/MAINTAINERS.md).

### Responsibilities

- Maintain <PERSON>'s design philosophy, technical direction, and conventions.
- Engage with the community and **Contributors**.
- Triage, review, and comment on issues.
- Triage and comment on pull requests.
- Review and merge assigned pull requests.
- Serve as the primary point of contact for assigned [Requests For Comments (RFCs)](https://thousandbrainsproject.readme.io/docs/request-for-comments-rfc).
- Propose disposition of [RFCs](https://thousandbrainsproject.readme.io/docs/request-for-comments-rfc) (e.g., merge or close).
- Approve or abstain from the disposition of [RFCs](https://thousandbrainsproject.readme.io/docs/request-for-comments-rfc).

## Adding Maintainers

- Any **Maintainer** can nominate a new **Maintainer** by starting a private email thread amongst all the current **Maintainers** with the nomination and proposing that the nominee be invited to join.
- The **Maintainers** are given sufficient time to respond to the nomination. If there is disagreement, a discussion ensues, possibly resulting in a vote.
- The **Lead Maintainer** confirms or denies the nomination.

## Removing Maintainers

- Any **Maintainer** can propose to remove a current **Maintainer** by starting a private email thread amongst all the current **Maintainers**, excluding the person proposed to be removed.
- The **Maintainers** are given sufficient time to respond to the removal proposal. If there is disagreement, a discussion ensues, possibly resulting in a vote.
- The **Lead Maintainer** confirms or denies the removal.

## Resigning as a Maintainer

- Any **Maintainer** can resign by contacting the **Lead Maintainer** privately.

## Selecting the Lead Maintainer

- The **Lead Maintainer** is selected by the Executive Director of the Thousand Brains Project.