# This file is case-sensitive by default and you can use regex
# see the docs here: https://vale.sh/docs/keys/vocab#file-format
Bluesky
[Nn]eocortex
[a-zA-Z.]+(_[a-zA-Z.]+)+
Mountcastle
[Cc]onfigs?
skillset
LMs?
SMs?
SDRs?
[hH]ippocampal
[hH]eterarchy
saccading
HTM
ANNs?
CNNs?
GNNs?
DNNs?
LLMs?
CPUs?
TGZ|tgz
saccade
colliculus
affordances
[Ss]ubgoals
KDTree
resampling
wandb
distractor
parallelization
[aA]rXiv
Num
bioRxiv
worldimages
[nN]eocortical
[Ii]nitializer
resample
actuations
Calli
[oO]mniglot
iter
Dataloader
kd
[Tt]hresholding
multimodal
heterarchical
subfolders
von
Gabor
compositionality
Thrun
Mnih
Hafner
GPTs
Spelke
Gavrikov
Szegedy
Stanovich
subcomponents
Drost
voxel
voxels
subclassed
youtube
URL
fullscreen
favicon
href
dataclass
[pP]retrained
[Rr]untimes
neurophysiologist
[Mm]ixin
[cC]onda
[mM]iniconda
zsh
[Ww]andb
utils
matplotlib
timeseries
pretrain
misclassification
_all_
args
eval
[Dd]ata[Ll]oader
docstring
Leyman
Sampath
Shen
[nN]euromorphic
interpretability
Walkthrough
presynaptic
subcortically
PyTorch
Guillery
Blekeslee
Felleman
Interlaminar
Markram
Silberberg
Interneurons
Elsevier
Neuroanatomy
transthalamic
corticocortical
Gu
GPU
Lewallen
Kinkhabwala
Domnisoru
Yoon
Gauthier
Fiete
entorhinal
Usrey
Corticofugal
Behrens
Rao
Pennartz
Aru
Scheinkman
Klukas
Purdy
xyz|XYZ
[Rr]eadme|README
substep
CLA
unmerged
github
VSCode
APIs
Frontmatter
prechecks
repo
rdme
cli
semver
[Cc]allouts
[Dd]iscretized?
discretization
profiler
overconstrained
loopdown
perceptrons
bool
[gG]aussian
Cui
learnable
Eitan
Azoff
Sync'ing
subdocuments
sync'd
NumPy|Numpy
Numenta
Leadholm
Kalman
biofilm
Tolman's
[eE]fference
[Tt]riaged
YouTube
GitHub
[nN]euroscientists
Constantinescu
O'Keefe
Nadel
Milner
Goodale
[aA]llocentric
Hebbian
Hopfield
Arcimboldo
