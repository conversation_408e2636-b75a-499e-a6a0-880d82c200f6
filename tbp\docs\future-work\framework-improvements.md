---
title: Framework Improvements
description: Improvements we would like to make on the general code framework.
---
These are the things we would like to implement:

- [Add infrastructure for multiple agents that move independently](framework-improvements/add-infrastructure-for-multiple-agents-that-move-independently.md) #numsteps #infrastructure #goalpolicy
- [Automate benchmark experiments & analysis](framework-improvements/automate-benchmark-experiments-analysis.md) #infrastructure
- [Add more wandb logging for learning from unsupervised](framework-improvements/add-more-wandb-logging-for-learning-unsupervised.md) #learning
- [Add GPU support for Monty](framework-improvements/add-gpu-support-for-monty.md) #speed
- [Use State class inside of LMs](framework-improvements/use-state-class-inside-of-lms.md) #infrastructure
- [Make configs easier to use](framework-improvements/make-configs-easier-to-use.md) #infrastructure
- [Find faster alternative to KDTree search](framework-improvements/find-faster-alternative-to-kdtree-search.md) #speed

!snippet[../snippets/contributing-tasks.md]