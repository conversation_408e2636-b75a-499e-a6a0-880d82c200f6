---
title: Voting Improvements
description: Improvements we would like to add to voting.
---
These are the things we would like to implement:

- [Use pose for voting](voting-improvements/use-pose-for-voting.md) #numsteps #pose
- [Outline routing protocol/attention](voting-improvements/outline-routing-protocol-attention.md) #speed #multiobj
- [Generalize voting to associative connections](voting-improvements/generalize-voting-to-associative-connections.md) (arbitrary object ID and reference frame pairs) #infrastructure
- [Can we change the CMP to use displacements instead of locations?](voting-improvements/can-we-change-the-cmp-to-use-displacements-instead-of-locations.md)

!snippet[../snippets/contributing-tasks.md]