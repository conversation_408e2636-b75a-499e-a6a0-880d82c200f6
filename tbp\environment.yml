# This file may be used to create an environment using:
#
# ## Miniconda or Anaconda
#     $ conda env create --file environment.yml
# If you are using the zsh shell, run:
#     $ conda init zsh
# Or, if you are using a different shell, run:
#     $ conda init
# After init, if you do not want conda to change your global shell when
# you open a new terminal, run:
#     $ conda config --set auto_activate_base false
# Finally, activate the environment with:
#     $ conda activate tbp.monty
#
# platform: default
name: tbp.monty
channels:
  - aihabitat
  - pytorch
  - pyg
  - conda-forge
  - defaults

dependencies:
  - python=3.8

  - aihabitat::habitat-sim=0.2.2
  - aihabitat::withbullet
  - conda-forge::cmake>=3.14.0
  - conda-forge::importlib_resources
  - conda-forge::matplotlib
  - conda-forge::mkl==2024.0.0 # see https://github.com/pytorch/pytorch/issues/123097
  - conda-forge::numpy<=1.23.5 # numpy >=1.24.0 missing np.long
  - conda-forge::pandas
  - conda-forge::pillow
  - conda-forge::quaternion=2023.0.3 # later versions missing np.long
  - conda-forge::scikit-image
  - conda-forge::scikit-learn=1.3.2
  - conda-forge::scipy
  - conda-forge::sympy
  - conda-forge::tqdm
  - conda-forge::wandb
  - conda-forge::wget
  - pyg::pyg
  - pytorch::pytorch=1.11.0
  - pytorch::torchvision

  - pip
  - pip:
      - -e .[dev]

