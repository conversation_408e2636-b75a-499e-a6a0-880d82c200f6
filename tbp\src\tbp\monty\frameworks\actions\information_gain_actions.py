from __future__ import annotations
from tbp.monty.frameworks.actions.actions import (
    Action,
    LookDown,
    LookUp,
    MoveForward,
    MoveTangentially,
    OrientHorizontal,
    OrientVertical,
    SetAgentPitch,
    SetAgentPose,
    SetSensorPitch,
    SetSensorPose,
    SetSensorRotation,
    SetYaw,
    TurnLeft,
    TurnRight
)
from tbp.monty.frameworks.actions.actuator import Actuator
from tbp.monty.frameworks.actions.action_samplers import ActionSampler
from typing import TYPE_CHECKING, Optional
import numpy as np


if TYPE_CHECKING:
    from tbp.monty.frameworks.actions.action_samplers import ActionSampler


class InformationGainAction(Action):
    """基于信息增益的动作类"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainAction:
        """基于代理当前状态采样一个动作"""
        return sampler.sample_information_gain_action(agent_id)

    def __init__(self, agent_id: str, action: Action, information_gain: float) -> None:
        super().__init__(agent_id=agent_id)
        self.action = action
        self.information_gain = information_gain

    def calculate_information_gain(self) -> float:
        """计算当前动作的信息增益"""
        return np.log(1 + self.information_gain)  # 简单的模拟实现

    def act(self, actuator: Actuator) -> None:
        """执行动作并根据信息增益进行更新"""
        self.action.act(actuator)

    def __iter__(self):
        """返回动作和信息增益"""
        yield "action", self.action.name
        yield "information_gain", self.information_gain


# 前进动作
class InformationGainMoveForward(InformationGainAction):
    """基于信息增益的前进动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainMoveForward:
        """基于信息增益采样前进动作"""
        action = sampler.sample_move_forward(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainMoveForward(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: MoveForward, information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的前进动作"""
        self.action.act(actuator)


# 向左转动作
class InformationGainTurnLeft(InformationGainAction):
    """基于信息增益的向左转动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainTurnLeft:
        """基于信息增益采样向左转动作"""
        action = sampler.sample_turn_left(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainTurnLeft(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: TurnLeft, information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的向左转动作"""
        self.action.act(actuator)


# 向右转动作
class InformationGainTurnRight(InformationGainAction):
    """基于信息增益的向右转动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainTurnRight:
        """基于信息增益采样向右转动作"""
        action = sampler.sample_turn_right(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainTurnRight(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: TurnRight, information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的向右转动作"""
        self.action.act(actuator)


# 向下看动作
class InformationGainLookDown(InformationGainAction):
    """基于信息增益的向下看动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainLookDown:
        """基于信息增益采样向下看动作"""
        action = sampler.sample_look_down(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainLookDown(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: LookDown, information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的向下看动作"""
        self.action.act(actuator)


# 向上看动作
class InformationGainLookUp(InformationGainAction):
    """基于信息增益的向上看动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainLookUp:
        """基于信息增益采样向上看动作"""
        action = sampler.sample_look_up(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainLookUp(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: LookUp, information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的向上看动作"""
        self.action.act(actuator)


# 横向移动动作
class InformationGainMoveTangentially(InformationGainAction):
    """基于信息增益的横向移动动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainMoveTangentially:
        """基于信息增益采样横向移动动作"""
        from tbp.monty.frameworks.actions.actions import MoveTangentially
        action = sampler.sample_move_tangentially(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainMoveTangentially(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'MoveTangentially', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的横向移动动作"""
        self.action.act(actuator)


# 水平方向移动动作
class InformationGainOrientHorizontal(InformationGainAction):
    """基于信息增益的水平方向移动动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainOrientHorizontal:
        """基于信息增益采样水平方向移动动作"""
        from tbp.monty.frameworks.actions.actions import OrientHorizontal
        action = sampler.sample_orient_horizontal(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainOrientHorizontal(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'OrientHorizontal', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的水平方向移动动作"""
        self.action.act(actuator)


# 垂直方向移动动作
class InformationGainOrientVertical(InformationGainAction):
    """基于信息增益的垂直方向移动动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainOrientVertical:
        """基于信息增益采样垂直方向移动动作"""
        from tbp.monty.frameworks.actions.actions import OrientVertical
        action = sampler.sample_orient_vertical(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainOrientVertical(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'OrientVertical', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的垂直方向移动动作"""
        self.action.act(actuator)


# 设置代理俯仰角动作
class InformationGainSetAgentPitch(InformationGainAction):
    """基于信息增益的设置代理俯仰角动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainSetAgentPitch:
        """基于信息增益采样设置代理俯仰角动作"""
        from tbp.monty.frameworks.actions.actions import SetAgentPitch
        action = sampler.sample_set_agent_pitch(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainSetAgentPitch(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'SetAgentPitch', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的设置代理俯仰角动作"""
        self.action.act(actuator)


# 设置代理姿态动作
class InformationGainSetAgentPose(InformationGainAction):
    """基于信息增益的设置代理姿态动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainSetAgentPose:
        """基于信息增益采样设置代理姿态动作"""
        from tbp.monty.frameworks.actions.actions import SetAgentPose
        action = sampler.sample_set_agent_pose(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainSetAgentPose(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'SetAgentPose', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的设置代理姿态动作"""
        self.action.act(actuator)


# 设置传感器俯仰角动作
class InformationGainSetSensorPitch(InformationGainAction):
    """基于信息增益的设置传感器俯仰角动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainSetSensorPitch:
        """基于信息增益采样设置传感器俯仰角动作"""
        from tbp.monty.frameworks.actions.actions import SetSensorPitch
        action = sampler.sample_set_sensor_pitch(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainSetSensorPitch(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'SetSensorPitch', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的设置传感器俯仰角动作"""
        self.action.act(actuator)


# 设置传感器姿态动作
class InformationGainSetSensorPose(InformationGainAction):
    """基于信息增益的设置传感器姿态动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainSetSensorPose:
        """基于信息增益采样设置传感器姿态动作"""
        from tbp.monty.frameworks.actions.actions import SetSensorPose
        action = sampler.sample_set_sensor_pose(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainSetSensorPose(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'SetSensorPose', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的设置传感器姿态动作"""
        self.action.act(actuator)


# 设置传感器旋转动作
class InformationGainSetSensorRotation(InformationGainAction):
    """基于信息增益的设置传感器旋转动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainSetSensorRotation:
        """基于信息增益采样设置传感器旋转动作"""
        from tbp.monty.frameworks.actions.actions import SetSensorRotation
        action = sampler.sample_set_sensor_rotation(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainSetSensorRotation(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'SetSensorRotation', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的设置传感器旋转动作"""
        self.action.act(actuator)


# 设置偏航角动作
class InformationGainSetYaw(InformationGainAction):
    """基于信息增益的设置偏航角动作"""

    @classmethod
    def sample(cls, agent_id: str, sampler: ActionSampler) -> InformationGainSetYaw:
        """基于信息增益采样设置偏航角动作"""
        from tbp.monty.frameworks.actions.actions import SetYaw
        action = sampler.sample_set_yaw(agent_id)
        information_gain = np.random.rand()  # 替换为实际逻辑
        return InformationGainSetYaw(agent_id, action, information_gain)

    def __init__(self, agent_id: str, action: 'SetYaw', information_gain: float) -> None:
        super().__init__(agent_id, action, information_gain)

    def act(self, actuator: Actuator) -> None:
        """执行基于信息增益的设置偏航角动作"""
        self.action.act(actuator)

from typing import List

class InformationGainActionSelector:
    """选择具有最高信息增益的动作"""

    def __init__(self, actions: List[InformationGainAction]):
        self.actions = actions

    def select_best_action(self) -> InformationGainAction:
        """从动作列表中选择具有最高信息增益的动作"""
        best_action = max(self.actions, key=lambda action: action.calculate_information_gain())
        return best_action
