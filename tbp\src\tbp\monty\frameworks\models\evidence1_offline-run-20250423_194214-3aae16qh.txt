(tbp.monty) MacBook-Air tbp.monty-main % python benchmarks/run.py -e randrot_10distinctobj_surf_agent
MONTY_LOGS not set. Using default directory: ~/tbp/results/monty/
MONTY_MODELS not set. Using default directory: ~/tbp/results/monty/pretrained_models/
MONTY_DATA not set. Using default directory: /Users/<USER>/data/
WANDB_DIR not set. Using default directory: ~/tbp/results/monty/
wandb: WARNING Path ~/tbp/results/monty/wandb/ wasn't writable, using system temp directory.
wandb: WARNING Path ~/tbp/results/monty/wandb/ wasn't writable, using system temp directory
wandb: WARNING Path ~/tbp/results/monty/wandb/ wasn't writable, using system temp directory
wandb: (1) Create a W&B account
wandb: (2) Use an existing W&B account
wandb: (3) Don't visualize my results
wandb: Enter your choice: 3
wandb: You chose "Don't visualize my results"
wandb: Tracking run with wandb version 0.16.6
wandb: W&B syncing is set to `offline` in this directory.
wandb: Run `wandb online` or set WANDB_MODE=online to enable cloud syncing.
---------evaluating---------
wandb:
wandb:
wandb: Run history:
wandb: LM_0/episode/individual_ts_rotation_error ▅▁█▁▁▁▁▂▁▁▁▂▂▁▁▁▁▁▁▁▁▁██▁▂▁█▁▁▁▁▁▁▁▁▁▁▁▁
wandb:       LM_0/episode/steps_to_individual_ts █▄▁▂▃▁▄▂▁▁▂▂▃▃▂▁▁▂▁▅▄▁▁▁▄▁▂▄▂▁▃▃▅▁▃▁▂▁▂▁
wandb:                          episode/confused ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                      episode/confused_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                           episode/correct ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                       episode/correct_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           episode/goal_state_success_rate ▅▅███████▆██▆▁█▆█▅█▂▆▃█▃██▃▆██▃▅▆█▆█▃█▃█
wandb:             episode/goal_states_attempted █▅▃▅▅▁▅▅▁▃▃▅▅▅▃▃▁▅▃▆▅▃▃▃▅▃▅▅▃▃▅▅▅▃▅▃▃▃▅▃
wandb:                          episode/lm_steps █▄▁▂▃▁▄▂▁▁▂▂▃▃▂▁▁▂▁▅▄▁▁▁▄▁▂▄▂▁▃▃▅▁▃▁▂▁▂▁
wandb:          episode/mean_lm_steps_to_indv_ts █▄▁▂▃▁▄▂▁▁▂▂▃▃▂▁▁▂▁▅▄▁▁▁▄▁▂▄▂▁▃▃▅▁▃▁▂▁▂▁
wandb:              episode/monty_matching_steps █▄▁▂▃▁▄▂▁▁▂▂▃▃▂▁▁▂▁▅▄▁▁▁▄▁▂▄▂▁▃▃▅▁▃▁▂▁▂▁
wandb:                       episode/monty_steps █▄▂▅▃▁▃▃▂▂▃▄▄▃▂▂▁▂▂▇▄▇▂▃▃▁▂▅▅▂▂▅▄▁▂▂▄▂▂▂
wandb:                          episode/no_match ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                     episode/pose_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                    episode/rotation_error ▅▁█▁▁▁▁▂▁▁▁▂▂▁▁▁▁▁▁▁▁▁██▁▂▁█▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                          episode/run_time █▅▂▅▃▁▅▃▂▂▂▅▃▃▃▂▂▃▃▆▄▅▂▂▃▁▃▄▅▁▃█▅▁▃▃▄▁▂▂
wandb:                 episode/symmetry_evidence █▁▁█▁▁██▁▁▁███▁▁▁█▁██▁▁▁▁▁█▁█▁███▁█▁█▁█▁
wandb:                          episode/time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           episode/used_mlh_after_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:              overall/avg_episode_run_time █▄▂▂▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                  overall/avg_num_lm_steps █▄▂▂▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:      overall/avg_num_monty_matching_steps █▄▂▂▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:               overall/avg_num_monty_steps █▄▂▂▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▁▂▂▂▂▂▂▂▂▂▂▁▁▁▁▁▁▁
wandb:                overall/avg_rotation_error █▃▄▃▂▂▃▂▂▂▂▂▂▁▁▁▁▁▁▁▁▁▁▁▁▁▁▂▁▁▁▁▁▁▁▁▁▁▁▂
wandb:                      overall/num_episodes ▁▁▁▁▂▂▂▂▂▃▃▃▃▃▃▄▄▄▄▄▅▅▅▅▅▅▆▆▆▆▆▇▇▇▇▇▇███
wandb:                  overall/percent_confused ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:              overall/percent_confused_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_confused_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                   overall/percent_correct ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:               overall/percent_correct_mlh ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:            overall/percent_correct_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                  overall/percent_no_match ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_no_match_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:             overall/percent_pose_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:      overall/percent_pose_time_out_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                  overall/percent_time_out ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:           overall/percent_time_out_per_lm ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:    overall/percent_used_mlh_after_timeout ▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁▁
wandb:                          overall/run_time ▁▁▁▁▂▂▂▂▂▃▃▃▃▃▃▄▄▄▄▄▅▅▅▅▅▆▆▆▆▆▆▇▇▇▇▇████
wandb:
wandb: Run summary:
wandb: LM_0/episode/individual_ts_rotation_error 0.0329
wandb:       LM_0/episode/steps_to_individual_ts 21
wandb:                          episode/confused 0
wandb:                      episode/confused_mlh 0
wandb:                           episode/correct 1
wandb:                       episode/correct_mlh 0
wandb:           episode/goal_state_success_rate 1.0
wandb:             episode/goal_states_attempted 3
wandb:                          episode/lm_steps 21
wandb:          episode/mean_lm_steps_to_indv_ts 21.0
wandb:              episode/monty_matching_steps 21
wandb:                       episode/monty_steps 143
wandb:                          episode/no_match 0
wandb:                     episode/pose_time_out 0
wandb:                    episode/rotation_error 0.0329
wandb:                          episode/run_time 1.22897
wandb:                 episode/symmetry_evidence 0.0
wandb:                          episode/time_out 0
wandb:           episode/used_mlh_after_time_out 0
wandb:              overall/avg_episode_run_time 1.69362
wandb:                  overall/avg_num_lm_steps 27.62
wandb:      overall/avg_num_monty_matching_steps 27.62
wandb:               overall/avg_num_monty_steps 177.1
wandb:                overall/avg_rotation_error 0.37765
wandb:                      overall/num_episodes 100
wandb:                  overall/percent_confused 0.0
wandb:              overall/percent_confused_mlh 0.0
wandb:           overall/percent_confused_per_lm 0.0
wandb:                   overall/percent_correct 100.0
wandb:               overall/percent_correct_mlh 0.0
wandb:            overall/percent_correct_per_lm 100.0
wandb:                  overall/percent_no_match 0.0
wandb:           overall/percent_no_match_per_lm 0.0
wandb:             overall/percent_pose_time_out 0.0
wandb:      overall/percent_pose_time_out_per_lm 0.0
wandb:                  overall/percent_time_out 0.0
wandb:           overall/percent_time_out_per_lm 0.0
wandb:    overall/percent_used_mlh_after_timeout 0.0
wandb:                          overall/run_time 169.36163
wandb:
wandb: You can sync this run to the cloud by running:
wandb: wandb sync /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250423_194214-3aae16qh
wandb: Find logs at: /var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250423_194214-3aae16qh/logs

！！！
###数据保存: 训练数据和系统信息已保存至临时文件夹/var/folders/nt/0xp7cqd97b7dksvkkwdjt0p40000gn/T/wandb/offline-run-20250423_194214-3aae16qh/